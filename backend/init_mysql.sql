-- TableRAG MySQL数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS aidocsdb
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE aidocsdb;

-- 创建用户（可选，如果需要专门的用户）
-- CREATE USER IF NOT EXISTS 'tablerag_user'@'localhost' IDENTIFIED BY 'tablerag_password';
-- GRANT ALL PRIVILEGES ON tablerag.* TO 'tablerag_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 注意：实际的表结构将由SQLAlchemy自动创建
-- 这个脚本主要用于创建数据库和设置权限
--
-- 表名将使用 tablerag_ 前缀：
-- - tablerag_datasets (数据集表)
-- - tablerag_images (图片表)
-- - tablerag_annotations (标注表)
-- - tablerag_accuracy_evaluations (准确率评估表)
-- - tablerag_reports (报告表)
