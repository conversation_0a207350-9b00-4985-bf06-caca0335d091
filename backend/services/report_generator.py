"""
报告生成服务

生成各种类型的分析报告
"""

import json
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from datetime import datetime

from backend.database.crud import EvaluationCRUD, AnnotationCRUD, DatasetCRUD

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def generate_accuracy_report(self, dataset_name: str, include_details: bool = True) -> Dict[str, Any]:
        """
        生成准确率报告
        
        Args:
            dataset_name: 数据集名称
            include_details: 是否包含详细信息
            
        Returns:
            报告数据字典
        """
        try:
            # 获取数据集信息
            dataset = DatasetCRUD.get_by_name(self.db, dataset_name)
            if not dataset:
                raise ValueError(f"数据集不存在: {dataset_name}")
            
            # 获取评估结果
            evaluations = EvaluationCRUD.get_by_dataset(self.db, dataset_name)
            
            # 获取标注数据
            annotations = AnnotationCRUD.get_all(self.db)
            dataset_annotations = [a for a in annotations if a.image.dataset.name == dataset_name]
            
            # 构建报告数据
            report_data = {
                "report_info": {
                    "title": f"{dataset_name} 数据集准确率报告",
                    "dataset_name": dataset_name,
                    "generated_at": datetime.now().isoformat(),
                    "total_images": len(dataset.images),
                    "total_annotations": len(dataset_annotations),
                    "total_evaluations": len(evaluations)
                },
                "summary": self._generate_summary(evaluations),
                "parser_comparison": self._generate_parser_comparison(evaluations),
                "accuracy_trends": self._generate_accuracy_trends(evaluations)
            }
            
            if include_details:
                report_data["detailed_results"] = self._generate_detailed_results(evaluations)
            
            return report_data
            
        except Exception as e:
            raise Exception(f"生成准确率报告失败: {str(e)}")

    async def generate_html_report(self, dataset_name: str) -> str:
        """
        生成HTML格式的准确率报告

        Args:
            dataset_name: 数据集名称

        Returns:
            HTML报告内容
        """
        try:
            # 获取报告数据
            report_data = await self.generate_accuracy_report(dataset_name, include_details=True)

            # 生成HTML内容
            html_content = self._generate_html_content(report_data)

            return html_content

        except Exception as e:
            raise Exception(f"生成HTML报告失败: {str(e)}")

    def _generate_html_content(self, report_data: Dict[str, Any]) -> str:
        """生成HTML报告内容"""

        report_info = report_data.get('report_info', {})
        summary = report_data.get('summary', {})
        parser_comparison = report_data.get('parser_comparison', {})

        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report_info.get('title', '准确率报告')}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; margin-top: 30px; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px 20px; text-align: center; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #3498db; }}
        .metric-label {{ font-size: 14px; color: #7f8c8d; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #3498db; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .accuracy-bar {{ width: 100px; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }}
        .accuracy-fill {{ height: 100%; background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60); }}
        .best-parser {{ background-color: #d5f4e6 !important; }}
        .footer {{ margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{report_info.get('title', '准确率报告')}</h1>

        <div class="summary">
            <h2>报告概览</h2>
            <div class="metric">
                <div class="metric-value">{report_info.get('total_images', 0)}</div>
                <div class="metric-label">总图片数</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report_info.get('total_annotations', 0)}</div>
                <div class="metric-label">标注数量</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report_info.get('total_evaluations', 0)}</div>
                <div class="metric-label">评估数量</div>
            </div>
            <div class="metric">
                <div class="metric-value">{summary.get('avg_overall_accuracy', 0):.1%}</div>
                <div class="metric-label">平均准确率</div>
            </div>
        </div>

        <h2>解析器性能对比</h2>
        <table>
            <thead>
                <tr>
                    <th>解析器</th>
                    <th>评估数量</th>
                    <th>结构准确率</th>
                    <th>内容准确率</th>
                    <th>综合准确率</th>
                    <th>准确率分布</th>
                </tr>
            </thead>
            <tbody>
        """

        # 添加解析器对比数据
        best_parser = summary.get('best_parser', {}).get('name', '')
        for parser_name, stats in parser_comparison.items():
            row_class = 'best-parser' if parser_name == best_parser else ''
            overall_acc = stats.get('avg_overall_accuracy', 0)

            html += f"""
                <tr class="{row_class}">
                    <td><strong>{parser_name}</strong></td>
                    <td>{stats.get('evaluation_count', 0)}</td>
                    <td>{stats.get('avg_structure_accuracy', 0):.1%}</td>
                    <td>{stats.get('avg_content_accuracy', 0):.1%}</td>
                    <td>{overall_acc:.1%}</td>
                    <td>
                        <div class="accuracy-bar">
                            <div class="accuracy-fill" style="width: {overall_acc * 100}%"></div>
                        </div>
                    </td>
                </tr>
            """

        html += f"""
            </tbody>
        </table>

        <div class="footer">
            <p>报告生成时间: {report_info.get('generated_at', '')}</p>
            <p>数据集: {report_info.get('dataset_name', '')}</p>
        </div>
    </div>
</body>
</html>
        """

        return html.strip()
    
    def _generate_summary(self, evaluations: List) -> Dict[str, Any]:
        """生成摘要信息"""
        if not evaluations:
            return {
                "total_evaluations": 0,
                "avg_overall_accuracy": 0,
                "best_parser": None,
                "worst_parser": None
            }
        
        # 按解析器统计
        parser_stats = {}
        for evaluation in evaluations:
            parser_name = evaluation.parser_name
            if parser_name not in parser_stats:
                parser_stats[parser_name] = []
            
            if evaluation.overall_accuracy is not None:
                parser_stats[parser_name].append(evaluation.overall_accuracy)
        
        # 计算平均准确率
        parser_averages = {}
        for parser_name, accuracies in parser_stats.items():
            if accuracies:
                parser_averages[parser_name] = sum(accuracies) / len(accuracies)
        
        # 找出最佳和最差解析器
        best_parser = max(parser_averages.items(), key=lambda x: x[1]) if parser_averages else None
        worst_parser = min(parser_averages.items(), key=lambda x: x[1]) if parser_averages else None
        
        # 整体平均准确率
        all_accuracies = [e.overall_accuracy for e in evaluations if e.overall_accuracy is not None]
        avg_accuracy = sum(all_accuracies) / len(all_accuracies) if all_accuracies else 0
        
        return {
            "total_evaluations": len(evaluations),
            "avg_overall_accuracy": round(avg_accuracy, 4),
            "best_parser": {
                "name": best_parser[0],
                "accuracy": round(best_parser[1], 4)
            } if best_parser else None,
            "worst_parser": {
                "name": worst_parser[0],
                "accuracy": round(worst_parser[1], 4)
            } if worst_parser else None,
            "parser_count": len(parser_stats)
        }
    
    def _generate_parser_comparison(self, evaluations: List) -> Dict[str, Any]:
        """生成解析器对比信息"""
        parser_stats = {}
        
        for evaluation in evaluations:
            parser_name = evaluation.parser_name
            if parser_name not in parser_stats:
                parser_stats[parser_name] = {
                    "count": 0,
                    "structure_accuracies": [],
                    "content_accuracies": [],
                    "overall_accuracies": []
                }
            
            stats = parser_stats[parser_name]
            stats["count"] += 1
            
            if evaluation.structure_accuracy is not None:
                stats["structure_accuracies"].append(evaluation.structure_accuracy)
            if evaluation.content_accuracy is not None:
                stats["content_accuracies"].append(evaluation.content_accuracy)
            if evaluation.overall_accuracy is not None:
                stats["overall_accuracies"].append(evaluation.overall_accuracy)
        
        # 计算统计信息
        comparison_data = {}
        for parser_name, stats in parser_stats.items():
            comparison_data[parser_name] = {
                "evaluation_count": stats["count"],
                "avg_structure_accuracy": round(
                    sum(stats["structure_accuracies"]) / len(stats["structure_accuracies"]), 4
                ) if stats["structure_accuracies"] else 0,
                "avg_content_accuracy": round(
                    sum(stats["content_accuracies"]) / len(stats["content_accuracies"]), 4
                ) if stats["content_accuracies"] else 0,
                "avg_overall_accuracy": round(
                    sum(stats["overall_accuracies"]) / len(stats["overall_accuracies"]), 4
                ) if stats["overall_accuracies"] else 0,
                "min_accuracy": round(min(stats["overall_accuracies"]), 4) if stats["overall_accuracies"] else 0,
                "max_accuracy": round(max(stats["overall_accuracies"]), 4) if stats["overall_accuracies"] else 0
            }
        
        return comparison_data
    
    def _generate_accuracy_trends(self, evaluations: List) -> Dict[str, Any]:
        """生成准确率趋势信息"""
        # 按时间排序
        sorted_evaluations = sorted(evaluations, key=lambda x: x.created_at)
        
        # 按解析器分组
        parser_trends = {}
        for evaluation in sorted_evaluations:
            parser_name = evaluation.parser_name
            if parser_name not in parser_trends:
                parser_trends[parser_name] = []
            
            parser_trends[parser_name].append({
                "timestamp": evaluation.created_at.isoformat(),
                "overall_accuracy": evaluation.overall_accuracy
            })
        
        return parser_trends
    
    def _generate_detailed_results(self, evaluations: List) -> List[Dict[str, Any]]:
        """生成详细结果信息"""
        detailed_results = []
        
        for evaluation in evaluations:
            result = {
                "evaluation_id": evaluation.id,
                "image_id": evaluation.image_id,
                "image_filename": evaluation.image.filename if evaluation.image else None,
                "parser_name": evaluation.parser_name,
                "structure_accuracy": evaluation.structure_accuracy,
                "content_accuracy": evaluation.content_accuracy,
                "overall_accuracy": evaluation.overall_accuracy,
                "created_at": evaluation.created_at.isoformat()
            }
            
            # 解析详细评估信息
            if evaluation.evaluation_details:
                try:
                    result["details"] = json.loads(evaluation.evaluation_details)
                except json.JSONDecodeError:
                    result["details"] = {"raw": evaluation.evaluation_details}
            
            detailed_results.append(result)
        
        return detailed_results
