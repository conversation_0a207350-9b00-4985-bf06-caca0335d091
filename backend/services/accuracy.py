"""
准确率计算服务

提供多种评估指标的准确率计算功能
"""

import json
import re
from typing import Dict, Any, List, Tuple
from difflib import SequenceMatcher
import pandas as pd
from io import StringIO

class AccuracyCalculator:
    """准确率计算器"""
    
    def __init__(self):
        self.weights = {
            'structure': 0.4,  # 结构准确率权重
            'content': 0.6     # 内容准确率权重
        }
    
    def calculate_accuracy(self, ground_truth: Dict[str, Any], prediction: Dict[str, Any]) -> Dict[str, float]:
        """
        计算综合准确率

        Args:
            ground_truth: 标准答案数据
            prediction: 预测结果数据

        Returns:
            包含各项准确率的字典
        """
        try:
            # 解析输入数据
            gt_structure = self._parse_structure(ground_truth.get('table_structure', '{}'))
            gt_content = ground_truth.get('table_content', '')

            # 提取预测结果的内容
            pred_content = self._extract_prediction_content(prediction)

            # 从预测内容推断结构信息
            pred_structure = self._infer_structure_from_content(pred_content)

            # 计算结构准确率
            structure_accuracy = self.calculate_structure_accuracy(gt_structure, pred_structure)

            # 计算内容准确率
            content_accuracy = self.calculate_content_accuracy(gt_content, pred_content)

            # 计算综合准确率
            overall_accuracy = (
                structure_accuracy * self.weights['structure'] +
                content_accuracy * self.weights['content']
            )

            return {
                'structure_accuracy': round(structure_accuracy, 4),
                'content_accuracy': round(content_accuracy, 4),
                'overall_accuracy': round(overall_accuracy, 4)
            }

        except Exception as e:
            print(f"准确率计算失败: {e}")
            return {
                'structure_accuracy': 0.0,
                'content_accuracy': 0.0,
                'overall_accuracy': 0.0
            }

    def _extract_prediction_content(self, prediction: Dict[str, Any]) -> str:
        """
        从不同解析器的结果中提取内容

        Args:
            prediction: 解析器的预测结果

        Returns:
            提取的文本内容
        """
        try:
            # 获取解析器名称
            parser_name = prediction.get('parser_name', '')
            result = prediction.get('result', {})

            # 根据不同解析器提取内容
            if parser_name == 'vl_llm':
                # VL LLM: result.content.choices[0].message.content
                content = result.get('content', {})
                choices = content.get('choices', [])
                if choices and len(choices) > 0:
                    message = choices[0].get('message', {})
                    return message.get('content', '')

            elif parser_name in ['kdc_markdown', 'kdc_plain']:
                # KDC解析器: result.data[0].markdown
                data = result.get('data', [])
                if data and len(data) > 0:
                    return data[0].get('markdown', '')

            elif parser_name == 'kdc_kdc':
                # KDC KDC解析器: result.data[0].kdc_result
                data = result.get('data', [])
                if data and len(data) > 0:
                    return data[0].get('kdc_result', '')

            elif parser_name in ['monkey_ocr', 'monkey_ocr_latex']:
                # MonkeyOCR: result.html
                return result.get('html', '')

            # 通用fallback
            if isinstance(result, dict):
                # 尝试常见的字段名
                for field in ['content', 'text', 'result', 'output', 'markdown', 'html']:
                    if field in result:
                        value = result[field]
                        if isinstance(value, str):
                            return value
                        elif isinstance(value, list) and value:
                            return str(value[0])

            # 如果是字符串，直接返回
            if isinstance(result, str):
                return result

            return ''

        except Exception as e:
            print(f"提取预测内容失败: {e}")
            return ''

    def _infer_structure_from_content(self, content: str) -> Dict:
        """
        从表格内容推断结构信息

        Args:
            content: 表格内容（HTML或Markdown）

        Returns:
            推断的结构信息
        """
        try:
            structure = {
                'rows': 0,
                'cols': 0,
                'headers': [],
                'data_types': [],
                'has_header': False,
                'merged_cells': [],
                'table_type': 'data_table'
            }

            if not content:
                return structure

            # 解析表格内容
            df = self._parse_table_content(content)
            if df is not None and not df.empty:
                structure['rows'] = len(df) + 1  # +1 for header
                structure['cols'] = len(df.columns)
                structure['headers'] = df.columns.tolist()
                structure['has_header'] = True

                # 推断数据类型
                data_types = []
                for col in df.columns:
                    col_data = df[col].dropna().astype(str)
                    if col_data.empty:
                        data_types.append('string')
                        continue

                    # 检查是否为数字
                    numeric_count = 0
                    for value in col_data:
                        value = str(value).strip()
                        if re.match(r'^-?\d+\.?\d*$', value) or value.endswith('%'):
                            numeric_count += 1

                    if numeric_count / len(col_data) > 0.7:
                        data_types.append('number')
                    else:
                        data_types.append('string')

                structure['data_types'] = data_types

            return structure

        except Exception as e:
            print(f"从内容推断结构失败: {e}")
            return {
                'rows': 0,
                'cols': 0,
                'headers': [],
                'data_types': [],
                'has_header': False,
                'merged_cells': [],
                'table_type': 'data_table'
            }
    
    def calculate_structure_accuracy(self, ground_truth: Dict, prediction: Dict) -> float:
        """
        计算表格结构准确率
        
        评估指标：
        - 行数准确率
        - 列数准确率
        - 合并单元格准确率
        """
        try:
            scores = []
            
            # 行数准确率
            gt_rows = ground_truth.get('rows', 0)
            pred_rows = prediction.get('rows', 0)
            if gt_rows > 0:
                row_accuracy = 1.0 - abs(gt_rows - pred_rows) / max(gt_rows, pred_rows)
                scores.append(max(0.0, row_accuracy))
            
            # 列数准确率
            gt_cols = ground_truth.get('cols', 0)
            pred_cols = prediction.get('cols', 0)
            if gt_cols > 0:
                col_accuracy = 1.0 - abs(gt_cols - pred_cols) / max(gt_cols, pred_cols)
                scores.append(max(0.0, col_accuracy))
            
            # 合并单元格准确率
            gt_merged = set(tuple(cell) for cell in ground_truth.get('merged_cells', []))
            pred_merged = set(tuple(cell) for cell in prediction.get('merged_cells', []))
            
            if gt_merged or pred_merged:
                intersection = len(gt_merged & pred_merged)
                union = len(gt_merged | pred_merged)
                merged_accuracy = intersection / union if union > 0 else 1.0
                scores.append(merged_accuracy)
            
            return sum(scores) / len(scores) if scores else 0.0
            
        except Exception as e:
            print(f"结构准确率计算失败: {e}")
            return 0.0
    
    def calculate_content_accuracy(self, ground_truth: str, prediction: str) -> float:
        """
        计算内容准确率
        
        评估指标：
        - 文本相似度
        - 表格数据准确率
        - 格式一致性
        """
        try:
            if not ground_truth and not prediction:
                return 1.0
            
            if not ground_truth or not prediction:
                return 0.0
            
            # 尝试解析为表格数据
            gt_table = self._parse_table_content(ground_truth)
            pred_table = self._parse_table_content(prediction)
            
            if gt_table is not None and pred_table is not None:
                # 表格数据准确率
                return self._calculate_table_data_accuracy(gt_table, pred_table)
            else:
                # 文本相似度
                return self._calculate_text_similarity(ground_truth, prediction)
                
        except Exception as e:
            print(f"内容准确率计算失败: {e}")
            return 0.0
    
    def _parse_structure(self, structure_data: str) -> Dict:
        """解析表格结构数据"""
        try:
            if isinstance(structure_data, str):
                return json.loads(structure_data)
            return structure_data or {}
        except:
            return {}
    
    def _parse_table_content(self, content: str) -> pd.DataFrame:
        """解析表格内容为DataFrame"""
        try:
            # 尝试解析HTML表格
            if '<table' in content.lower():
                return self._parse_html_table_to_df(content)

            # 尝试解析Markdown表格
            elif '|' in content:
                lines = [line.strip() for line in content.split('\n') if line.strip()]
                table_lines = [line for line in lines if line.startswith('|') and line.endswith('|')]

                if len(table_lines) >= 2:
                    # 移除分隔符行
                    table_lines = [line for line in table_lines if not re.match(r'^\|[\s\-\|]+\|$', line)]
                    
                    # 解析表格数据
                    data = []
                    for line in table_lines:
                        cells = [cell.strip() for cell in line.split('|')[1:-1]]
                        data.append(cells)
                    
                    if data:
                        return pd.DataFrame(data[1:], columns=data[0])
            
            # 尝试解析HTML表格
            if '<table' in content.lower():
                return pd.read_html(StringIO(content))[0]
            
            return None
            
        except Exception:
            return None
    
    def _calculate_table_data_accuracy(self, gt_table: pd.DataFrame, pred_table: pd.DataFrame) -> float:
        """计算表格数据准确率"""
        try:
            # 形状匹配度
            gt_shape = gt_table.shape
            pred_shape = pred_table.shape
            
            shape_score = 1.0
            if gt_shape[0] > 0:
                shape_score *= 1.0 - abs(gt_shape[0] - pred_shape[0]) / max(gt_shape[0], pred_shape[0])
            if gt_shape[1] > 0:
                shape_score *= 1.0 - abs(gt_shape[1] - pred_shape[1]) / max(gt_shape[1], pred_shape[1])
            
            # 内容匹配度
            content_score = 0.0
            if gt_shape == pred_shape and gt_shape[0] > 0 and gt_shape[1] > 0:
                total_cells = gt_shape[0] * gt_shape[1]
                matched_cells = 0
                
                for i in range(min(gt_shape[0], pred_shape[0])):
                    for j in range(min(gt_shape[1], pred_shape[1])):
                        gt_cell = str(gt_table.iloc[i, j]).strip()
                        pred_cell = str(pred_table.iloc[i, j]).strip()
                        
                        if gt_cell == pred_cell:
                            matched_cells += 1
                        else:
                            # 计算单元格文本相似度
                            similarity = SequenceMatcher(None, gt_cell, pred_cell).ratio()
                            matched_cells += similarity
                
                content_score = matched_cells / total_cells
            
            # 综合评分
            return (shape_score * 0.3 + content_score * 0.7)
            
        except Exception as e:
            print(f"表格数据准确率计算失败: {e}")
            return 0.0
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        try:
            # 清理文本
            clean_text1 = re.sub(r'\s+', ' ', text1.strip())
            clean_text2 = re.sub(r'\s+', ' ', text2.strip())
            
            # 计算序列匹配度
            similarity = SequenceMatcher(None, clean_text1, clean_text2).ratio()
            
            return similarity
            
        except Exception:
            return 0.0
    
    def calculate_batch_accuracy(self, annotations: List[Dict], parse_results: List[Dict]) -> Dict[str, Any]:
        """
        批量计算准确率
        
        Args:
            annotations: 标注数据列表
            parse_results: 解析结果列表
            
        Returns:
            批量准确率统计
        """
        try:
            results = []
            
            # 按文件名匹配标注和解析结果
            annotation_map = {ann['image_filename']: ann for ann in annotations}
            
            for result in parse_results:
                filename = result.get('filename', '')
                if filename in annotation_map:
                    annotation = annotation_map[filename]
                    accuracy = self.calculate_accuracy(annotation, result)
                    
                    results.append({
                        'filename': filename,
                        'parser_name': result.get('parser_name', ''),
                        **accuracy
                    })
            
            # 统计结果
            if not results:
                return {'total_count': 0, 'results': []}
            
            # 按解析器分组统计
            parser_stats = {}
            for result in results:
                parser_name = result['parser_name']
                if parser_name not in parser_stats:
                    parser_stats[parser_name] = {
                        'count': 0,
                        'structure_accuracy': [],
                        'content_accuracy': [],
                        'overall_accuracy': []
                    }
                
                stats = parser_stats[parser_name]
                stats['count'] += 1
                stats['structure_accuracy'].append(result['structure_accuracy'])
                stats['content_accuracy'].append(result['content_accuracy'])
                stats['overall_accuracy'].append(result['overall_accuracy'])
            
            # 计算平均值
            summary = {}
            for parser_name, stats in parser_stats.items():
                summary[parser_name] = {
                    'count': stats['count'],
                    'avg_structure_accuracy': sum(stats['structure_accuracy']) / len(stats['structure_accuracy']),
                    'avg_content_accuracy': sum(stats['content_accuracy']) / len(stats['content_accuracy']),
                    'avg_overall_accuracy': sum(stats['overall_accuracy']) / len(stats['overall_accuracy'])
                }
            
            return {
                'total_count': len(results),
                'results': results,
                'summary': summary
            }
            
        except Exception as e:
            print(f"批量准确率计算失败: {e}")
            return {'total_count': 0, 'results': [], 'summary': {}}

    def _parse_html_table_to_df(self, html_content: str) -> pd.DataFrame:
        """解析HTML表格为DataFrame（不使用lxml）"""
        try:
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')

            if not table:
                return None

            # 提取表格数据
            rows = []

            # 处理表头
            thead = table.find('thead')
            if thead:
                header_rows = thead.find_all('tr')
                for tr in header_rows:
                    row_data = []
                    for cell in tr.find_all(['th', 'td']):
                        cell_text = cell.get_text(strip=True)
                        row_data.append(cell_text)
                    if row_data:
                        rows.append(row_data)

            # 处理表体
            tbody = table.find('tbody')
            if tbody:
                body_rows = tbody.find_all('tr')
            else:
                # 如果没有tbody，直接查找所有tr
                body_rows = table.find_all('tr')
                # 如果已经处理过表头，跳过表头行
                if thead:
                    header_count = len(thead.find_all('tr'))
                    body_rows = body_rows[header_count:]

            for tr in body_rows:
                row_data = []
                for cell in tr.find_all(['th', 'td']):
                    cell_text = cell.get_text(strip=True)
                    row_data.append(cell_text)
                if row_data:
                    rows.append(row_data)

            if not rows:
                return None

            # 创建DataFrame
            if len(rows) > 1:
                # 第一行作为列名
                df = pd.DataFrame(rows[1:], columns=rows[0])
            else:
                # 只有一行数据
                df = pd.DataFrame([rows[0]])

            return df

        except Exception as e:
            print(f"解析HTML表格失败: {e}")
            return None
