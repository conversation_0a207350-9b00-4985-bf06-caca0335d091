# TableRAG 统一后端服务

统一的后端服务，提供数据集管理、解析结果访问、人工标注功能等完整功能，替代原有的简单HTTP文件服务器。

## 功能特性

- 🚀 **统一服务**: 一个服务提供所有后端功能
- 📊 **数据集管理**: 自动发现和管理数据集
- 🖼️ **图片访问**: 提供图片文件的高效访问
- 📋 **解析结果**: 统一的解析结果访问接口
- ✏️ **人工标注**: 完整的标注数据管理功能
- 📈 **准确率评估**: 自动计算和比较准确率
- 📄 **报告生成**: 生成详细的分析报告
- 🗄️ **数据库支持**: SQLite/PostgreSQL数据存储
- 📚 **自动文档**: FastAPI自动生成API文档

## 技术栈

- **FastAPI**: 现代化Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 默认数据库（也支持SQLite/PostgreSQL）
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

## 安装和启动

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 启动服务

**方式一：使用启动脚本（推荐）**

```bash
./backend/start_server.sh
```

**方式二：直接启动**

```bash
cd backend
python main.py
```

### 3. 访问服务

- **API文档**: http://localhost:8000/docs
- **静态文件**: http://localhost:8000/static
- **健康检查**: http://localhost:8000/health

## API接口

### 数据集管理

```http
GET    /api/datasets                    # 获取数据集列表
POST   /api/datasets                    # 创建数据集
GET    /api/datasets/{name}             # 获取数据集信息
DELETE /api/datasets/{name}             # 删除数据集
```

### 图片管理

```http
GET    /api/datasets/{name}/images      # 获取图片列表
GET    /api/datasets/{name}/images/{filename}  # 获取图片文件
POST   /api/datasets/{name}/images/sync # 同步图片到数据库
```

### 解析结果

```http
GET    /api/datasets/{name}/parse_results       # 获取解析结果
GET    /api/datasets/{name}/parse_results/files # 获取结果文件列表
GET    /api/datasets/{name}/vl_llm_results      # 获取VL LLM结果
GET    /api/datasets/{name}/monkey_ocr/{image}  # 获取MonkeyOCR结果
```

### 标注功能

```http
GET    /api/annotations                 # 获取标注列表
POST   /api/annotations                 # 创建标注
GET    /api/annotations/{id}            # 获取单个标注
PUT    /api/annotations/{id}            # 更新标注
DELETE /api/annotations/{id}            # 删除标注
GET    /api/images/{id}/annotations     # 获取图片的所有标注
```

### 准确率评估

```http
POST   /api/evaluate/{image_id}         # 评估单个图片
GET    /api/evaluations/{dataset_name}  # 获取评估结果
GET    /api/datasets/{name}/accuracy_summary  # 获取准确率摘要
```

### 报告生成

```http
POST   /api/reports/generate            # 生成报告
GET    /api/reports                     # 获取报告列表
GET    /api/reports/{id}                # 获取报告内容
DELETE /api/reports/{id}                # 删除报告
```

## 配置选项

### 环境变量

```bash
# 服务配置
BACKEND_HOST=0.0.0.0          # 服务主机地址
BACKEND_PORT=8000             # 服务端口
DEBUG=false                   # 调试模式

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/tablerag  # 数据库连接字符串

# 示例：使用SQLite
# DATABASE_URL=sqlite:///./tablerag_annotations.db

# 示例：使用PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost/tablerag
```

### 数据库支持

**MySQL（默认）**
- 高性能，适合生产环境
- 需要先创建数据库：`mysql -u root -p < backend/init_mysql.sql`
- 连接字符串：`mysql+pymysql://user:password@host:port/database`
- 表名使用 `tablerag_` 前缀，避免与其他应用冲突

**SQLite**
- 自动创建数据库文件
- 无需额外配置
- 适合开发和小规模使用
- 连接字符串：`sqlite:///./tablerag_annotations.db`

**PostgreSQL**
- 设置 `DATABASE_URL` 环境变量
- 适合生产环境和大规模使用
- 连接字符串：`postgresql://user:password@host:port/database`

## 与原系统的对比

### 原来的启动方式

```bash
# 需要启动两个服务
python -m http.server 10000  # 文件服务器
cd analyzer && npm start     # React前端
```

### 现在的启动方式

```bash
# 只需要启动两个服务
./backend/start_server.sh    # 统一后端服务（端口8000）
cd analyzer && npm start     # React前端（端口3000）
```

### 主要改进

1. **统一服务**: 一个后端服务提供所有功能
2. **数据库支持**: 持久化存储标注数据
3. **RESTful API**: 标准化的API接口
4. **自动文档**: 完整的API文档
5. **更好的错误处理**: 详细的错误信息
6. **扩展性**: 易于添加新功能

## 开发指南

### 添加新的API接口

1. 在 `backend/api/` 目录创建新的路由文件
2. 在 `main.py` 中注册路由
3. 添加相应的数据库模型（如需要）
4. 编写业务逻辑服务

### 数据库迁移

```bash
# 如果修改了数据库模型，重启服务会自动创建新表
# 对于生产环境，建议使用Alembic进行数据库迁移
```

### 调试模式

```bash
DEBUG=true ./backend/start_server.sh
```

调试模式会启用：
- 详细的日志输出
- 代码热重载
- SQL查询日志

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :8000
   # 或者修改端口
   BACKEND_PORT=8001 ./backend/start_server.sh
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库URL配置
   echo $DATABASE_URL
   # 确保数据库服务正在运行（PostgreSQL）
   ```

3. **静态文件无法访问**
   ```bash
   # 检查项目根目录权限
   ls -la /data/projects/kingsoft/personal/workspace/tablerag/enhance/
   ```

### 日志查看

服务启动后会在控制台输出详细日志，包括：
- API请求日志
- 数据库操作日志
- 错误信息

## 许可证

MIT License
