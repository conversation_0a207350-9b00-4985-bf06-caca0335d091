#!/usr/bin/env python3
"""
TableRAG 统一后端服务

提供数据集管理、解析结果访问、标注功能、报告生成等完整功能
替代原有的简单HTTP文件服务器
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.api import datasets, images, parse_results, annotations, reports, evaluations
from backend.database.database import init_database

# 创建FastAPI应用
app = FastAPI(
    title="TableRAG Backend API",
    description="统一后端服务，提供数据集管理、解析结果访问、标注功能等",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(datasets.router, prefix="/api", tags=["datasets"])
app.include_router(images.router, prefix="/api", tags=["images"])
app.include_router(parse_results.router, prefix="/api", tags=["parse_results"])
app.include_router(annotations.router, prefix="/api", tags=["annotations"])
app.include_router(evaluations.router, prefix="/api", tags=["evaluations"])
app.include_router(reports.router, prefix="/api", tags=["reports"])

# 静态文件服务（替代原HTTP服务器）
# 挂载项目根目录作为静态文件服务
static_root = project_root
app.mount("/static", StaticFiles(directory=static_root), name="static")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    print("正在初始化数据库...")
    init_database()
    print("数据库初始化完成")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "TableRAG Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "static_files": "/static"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

if __name__ == "__main__":
    # 获取配置
    host = os.getenv("BACKEND_HOST", "0.0.0.0")
    port = int(os.getenv("BACKEND_PORT", "8000"))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    print(f"启动TableRAG后端服务...")
    print(f"地址: http://{host}:{port}")
    print(f"API文档: http://{host}:{port}/docs")
    print(f"静态文件: http://{host}:{port}/static")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug"
    )
