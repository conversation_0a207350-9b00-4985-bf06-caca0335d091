"""
数据库连接和配置

管理SQLite数据库连接，提供会话管理
"""

import os
from pathlib import Path
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .models import Base

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://aidocsuser:aidocspass@localhost:13306/aidocsdb")

# 创建数据库引擎
if DATABASE_URL.startswith("sqlite"):
    # SQLite配置
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=os.getenv("DEBUG", "false").lower() == "true"
    )
elif DATABASE_URL.startswith("mysql"):
    # MySQL配置
    engine = create_engine(
        DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=3600,
        echo=os.getenv("DEBUG", "false").lower() == "true"
    )
else:
    # PostgreSQL等其他数据库配置
    engine = create_engine(
        DATABASE_URL,
        echo=os.getenv("DEBUG", "false").lower() == "true"
    )

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_database():
    """初始化数据库，创建所有表"""
    try:
        Base.metadata.create_all(bind=engine)
        print("数据库表创建成功")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise

def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_sync() -> Session:
    """获取同步数据库会话（用于非FastAPI上下文）"""
    return SessionLocal()

# 数据库依赖注入（用于FastAPI路由）
def get_database():
    """FastAPI依赖注入函数"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
