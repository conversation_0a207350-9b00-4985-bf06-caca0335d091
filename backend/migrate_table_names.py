#!/usr/bin/env python3
"""
TableRAG 数据库表名迁移脚本

将旧的表名迁移到带有 tablerag_ 前缀的新表名
"""

import os
import sys
from pathlib import Path
from sqlalchemy import create_engine, text, inspect

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def migrate_table_names():
    """迁移表名"""
    
    # 获取数据库连接
    database_url = os.getenv("DATABASE_URL", "sqlite:///./tablerag_annotations.db")
    engine = create_engine(database_url)
    
    # 表名映射
    table_mappings = {
        "datasets": "tablerag_datasets",
        "images": "tablerag_images", 
        "annotations": "tablerag_annotations",
        "accuracy_evaluations": "tablerag_accuracy_evaluations",
        "reports": "tablerag_reports"
    }
    
    print("TableRAG 数据库表名迁移")
    print("=" * 30)
    print(f"数据库: {database_url}")
    print()
    
    try:
        with engine.connect() as conn:
            # 检查数据库类型
            inspector = inspect(engine)
            existing_tables = inspector.get_table_names()
            
            print("现有表:")
            for table in existing_tables:
                print(f"  - {table}")
            print()
            
            # 检查是否需要迁移
            old_tables = [old for old in table_mappings.keys() if old in existing_tables]
            new_tables = [new for new in table_mappings.values() if new in existing_tables]
            
            if not old_tables:
                print("✓ 没有发现需要迁移的旧表名")
                return
            
            if new_tables:
                print("⚠️  发现新表名已存在，请检查是否已经迁移过:")
                for table in new_tables:
                    print(f"  - {table}")
                
                response = input("\n是否继续迁移? (y/N): ")
                if response.lower() != 'y':
                    print("迁移已取消")
                    return
            
            print("开始迁移表名...")
            print()
            
            # 开始事务
            trans = conn.begin()
            
            try:
                for old_name, new_name in table_mappings.items():
                    if old_name in existing_tables:
                        print(f"迁移: {old_name} -> {new_name}")
                        
                        if database_url.startswith("sqlite"):
                            # SQLite 使用 ALTER TABLE RENAME TO
                            conn.execute(text(f"ALTER TABLE {old_name} RENAME TO {new_name}"))
                        elif database_url.startswith("mysql"):
                            # MySQL 使用 RENAME TABLE
                            conn.execute(text(f"RENAME TABLE {old_name} TO {new_name}"))
                        elif database_url.startswith("postgresql"):
                            # PostgreSQL 使用 ALTER TABLE RENAME TO
                            conn.execute(text(f"ALTER TABLE {old_name} RENAME TO {new_name}"))
                        else:
                            print(f"⚠️  不支持的数据库类型，跳过 {old_name}")
                            continue
                        
                        print(f"✓ {old_name} 迁移完成")
                
                # 提交事务
                trans.commit()
                print()
                print("✓ 所有表名迁移完成!")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"✗ 迁移失败，已回滚: {e}")
                raise
                
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False
    
    return True

def verify_migration():
    """验证迁移结果"""
    
    database_url = os.getenv("DATABASE_URL", "sqlite:///./tablerag_annotations.db")
    engine = create_engine(database_url)
    
    print("\n验证迁移结果...")
    print("-" * 20)
    
    try:
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        expected_tables = [
            "tablerag_datasets",
            "tablerag_images", 
            "tablerag_annotations",
            "tablerag_accuracy_evaluations",
            "tablerag_reports"
        ]
        
        print("期望的表:")
        for table in expected_tables:
            exists = table in existing_tables
            status = "✓" if exists else "✗"
            print(f"  {status} {table}")
        
        print("\n所有现有表:")
        for table in sorted(existing_tables):
            prefix = "tablerag_" if table.startswith("tablerag_") else ""
            marker = "📋" if prefix else "❓"
            print(f"  {marker} {table}")
        
        # 检查是否还有旧表名
        old_tables = ["datasets", "images", "annotations", "accuracy_evaluations", "reports"]
        remaining_old = [table for table in old_tables if table in existing_tables]
        
        if remaining_old:
            print(f"\n⚠️  仍存在旧表名: {remaining_old}")
            return False
        else:
            print(f"\n✓ 迁移验证通过!")
            return True
            
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("TableRAG 数据库表名迁移工具")
    print("=" * 40)
    print()
    
    # 检查环境变量
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("请设置 DATABASE_URL 环境变量")
        print("例如:")
        print("  export DATABASE_URL=sqlite:///./tablerag_annotations.db")
        print("  export DATABASE_URL=mysql+pymysql://user:pass@localhost/tablerag")
        sys.exit(1)
    
    # 执行迁移
    success = migrate_table_names()
    
    if success:
        # 验证迁移
        verify_migration()
        
        print("\n" + "=" * 40)
        print("迁移完成!")
        print("请重启应用以使用新的表结构。")
    else:
        print("\n迁移失败，请检查错误信息。")
        sys.exit(1)
