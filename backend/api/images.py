"""
图片管理API

提供图片列表获取和文件访问功能，替代原有的静态文件服务
"""

import os
from pathlib import Path
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.database.database import get_database
from backend.database.crud import DatasetCRUD, ImageCRUD
from backend.database.models import Image

router = APIRouter()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

class ImageResponse(BaseModel):
    """图片响应模型"""
    id: int
    filename: str
    file_path: str
    url: str
    created_at: str

@router.get("/datasets/{dataset_name}/images", response_model=List[ImageResponse])
async def get_dataset_images(dataset_name: str, db: Session = Depends(get_database)):
    """
    获取数据集的图片列表
    
    替代原有的目录扫描方式，从数据库获取图片信息
    """
    try:
        # 获取数据集
        dataset = DatasetCRUD.get_by_name(db, dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 不存在")
        
        # 获取图片列表
        images = ImageCRUD.get_by_dataset(db, dataset.id)
        
        # 构建响应数据
        result = []
        for image in images:
            result.append(ImageResponse(
                id=image.id,
                filename=image.filename,
                file_path=image.file_path,
                url=f"/static/dataset/{dataset_name}/images/{image.filename}",
                created_at=image.created_at.isoformat()
            ))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")

@router.get("/datasets/{dataset_name}/images/{filename}")
async def get_image_file(dataset_name: str, filename: str):
    """
    获取图片文件
    
    提供图片文件的直接访问，替代静态文件服务
    """
    try:
        # 构建文件路径
        image_path = PROJECT_ROOT / "dataset" / dataset_name / "images" / filename
        
        # 检查文件是否存在
        if not image_path.exists():
            raise HTTPException(status_code=404, detail=f"图片文件不存在: {filename}")
        
        # 检查是否为图片文件
        if not image_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.gif', '.webp']:
            raise HTTPException(status_code=400, detail=f"不支持的图片格式: {filename}")
        
        # 返回文件
        return FileResponse(
            path=str(image_path),
            media_type=f"image/{image_path.suffix[1:]}",
            filename=filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片文件失败: {str(e)}")

@router.post("/datasets/{dataset_name}/images/sync")
async def sync_dataset_images(dataset_name: str, db: Session = Depends(get_database)):
    """
    同步数据集图片
    
    扫描文件系统，将新的图片文件同步到数据库
    """
    try:
        # 获取数据集
        dataset = DatasetCRUD.get_by_name(db, dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 不存在")
        
        # 扫描图片目录
        images_dir = PROJECT_ROOT / "dataset" / dataset_name / "images"
        if not images_dir.exists():
            return {"message": "图片目录不存在", "synced_count": 0}
        
        # 获取现有图片记录
        existing_images = {img.filename: img for img in ImageCRUD.get_by_dataset(db, dataset.id)}
        
        # 扫描新图片
        new_images = []
        for img_file in images_dir.iterdir():
            if img_file.is_file() and img_file.suffix.lower() in ['.png', '.jpg', '.jpeg', '.gif', '.webp']:
                if img_file.name not in existing_images:
                    new_images.append({
                        "filename": img_file.name,
                        "file_path": str(img_file.relative_to(PROJECT_ROOT))
                    })
        
        # 批量创建新图片记录
        if new_images:
            ImageCRUD.bulk_create(db, dataset.id, new_images)
        
        return {
            "message": f"同步完成",
            "synced_count": len(new_images),
            "total_count": len(existing_images) + len(new_images)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步图片失败: {str(e)}")

@router.get("/images/{image_id}", response_model=ImageResponse)
async def get_image_info(image_id: int, db: Session = Depends(get_database)):
    """根据ID获取图片信息"""
    try:
        image = db.query(Image).filter(Image.id == image_id).first()
        if not image:
            raise HTTPException(status_code=404, detail=f"图片不存在: {image_id}")
        
        return ImageResponse(
            id=image.id,
            filename=image.filename,
            file_path=image.file_path,
            url=f"/static/{image.file_path}",
            created_at=image.created_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片信息失败: {str(e)}")
