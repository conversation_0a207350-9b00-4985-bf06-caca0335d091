"""
准确率评估API

提供准确率评估和计算功能
"""

import json
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.database.database import get_database
from backend.database.crud import AnnotationCRUD, EvaluationCRUD, ImageCRUD, DatasetCRUD
from backend.database.models import AccuracyEvaluation
from backend.services.accuracy import AccuracyCalculator

router = APIRouter()

class EvaluationRequest(BaseModel):
    """评估请求模型"""
    image_id: int
    annotation_id: int
    parser_results: dict  # 解析结果数据

class EvaluationResponse(BaseModel):
    """评估响应模型"""
    id: int
    image_id: int
    annotation_id: int
    parser_name: str
    structure_accuracy: float
    content_accuracy: float
    overall_accuracy: float
    evaluation_details: str
    created_at: str

class BatchEvaluationRequest(BaseModel):
    """批量评估请求模型"""
    dataset_name: str
    parser_names: Optional[List[str]] = None  # 指定要评估的解析器

@router.post("/evaluate", response_model=EvaluationResponse)
async def evaluate_single(request: EvaluationRequest, db: Session = Depends(get_database)):
    """评估单个图片的解析结果"""
    try:
        # 获取标注数据
        annotation = AnnotationCRUD.get_by_id(db, request.annotation_id)
        if not annotation:
            raise HTTPException(status_code=404, detail=f"标注不存在: {request.annotation_id}")
        
        # 获取图片信息
        image = ImageCRUD.get_by_id(db, request.image_id)
        if not image:
            raise HTTPException(status_code=404, detail=f"图片不存在: {request.image_id}")
        
        # 准备标准答案数据
        ground_truth = {
            'table_structure': annotation.table_structure,
            'table_content': annotation.table_content
        }
        
        # 计算准确率
        calculator = AccuracyCalculator()
        accuracy_result = calculator.calculate_accuracy(ground_truth, request.parser_results)
        
        # 保存评估结果
        evaluation_data = {
            'image_id': request.image_id,
            'annotation_id': request.annotation_id,
            'parser_name': request.parser_results.get('parser_name', 'unknown'),
            'structure_accuracy': accuracy_result['structure_accuracy'],
            'content_accuracy': accuracy_result['content_accuracy'],
            'overall_accuracy': accuracy_result['overall_accuracy'],
            'evaluation_details': json.dumps({
                'ground_truth': ground_truth,
                'prediction': request.parser_results,
                'calculation_details': accuracy_result
            }, ensure_ascii=False)
        }
        
        evaluation = EvaluationCRUD.create(db, evaluation_data)
        
        return EvaluationResponse(
            id=evaluation.id,
            image_id=evaluation.image_id,
            annotation_id=evaluation.annotation_id,
            parser_name=evaluation.parser_name,
            structure_accuracy=evaluation.structure_accuracy,
            content_accuracy=evaluation.content_accuracy,
            overall_accuracy=evaluation.overall_accuracy,
            evaluation_details=evaluation.evaluation_details,
            created_at=evaluation.created_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")

@router.post("/evaluate/batch")
async def evaluate_batch(request: BatchEvaluationRequest, db: Session = Depends(get_database)):
    """批量评估数据集的解析结果"""
    try:
        # 获取数据集
        dataset = DatasetCRUD.get_by_name(db, request.dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集不存在: {request.dataset_name}")
        
        # 获取数据集的所有标注
        annotations = []
        for image in dataset.images:
            image_annotations = AnnotationCRUD.get_by_image(db, image.id)
            for annotation in image_annotations:
                if annotation.status == 'completed':  # 只评估已完成的标注
                    annotations.append({
                        'image_id': image.id,
                        'image_filename': image.filename,
                        'annotation_id': annotation.id,
                        'table_structure': annotation.table_structure,
                        'table_content': annotation.table_content
                    })
        
        if not annotations:
            return {
                'message': f'数据集 {request.dataset_name} 没有已完成的标注数据',
                'evaluated_count': 0,
                'results': []
            }
        
        # TODO: 这里需要获取解析结果数据
        # 暂时返回模拟结果
        return {
            'message': f'批量评估功能开发中，找到 {len(annotations)} 个标注',
            'evaluated_count': 0,
            'results': []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量评估失败: {str(e)}")

@router.get("/evaluations/{dataset_name}")
async def get_dataset_evaluations(dataset_name: str, db: Session = Depends(get_database)):
    """获取数据集的评估结果"""
    try:
        evaluations = EvaluationCRUD.get_by_dataset(db, dataset_name)
        
        results = []
        for evaluation in evaluations:
            results.append(EvaluationResponse(
                id=evaluation.id,
                image_id=evaluation.image_id,
                annotation_id=evaluation.annotation_id,
                parser_name=evaluation.parser_name,
                structure_accuracy=evaluation.structure_accuracy,
                content_accuracy=evaluation.content_accuracy,
                overall_accuracy=evaluation.overall_accuracy,
                evaluation_details=evaluation.evaluation_details,
                created_at=evaluation.created_at.isoformat()
            ))
        
        return {
            'dataset_name': dataset_name,
            'total_evaluations': len(results),
            'evaluations': results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评估结果失败: {str(e)}")

@router.get("/evaluations/{dataset_name}/summary")
async def get_evaluation_summary(dataset_name: str, db: Session = Depends(get_database)):
    """获取数据集评估结果摘要"""
    try:
        evaluations = EvaluationCRUD.get_by_dataset(db, dataset_name)
        
        if not evaluations:
            return {
                'dataset_name': dataset_name,
                'total_evaluations': 0,
                'parser_summary': {},
                'overall_summary': {}
            }
        
        # 按解析器统计
        parser_stats = {}
        for evaluation in evaluations:
            parser_name = evaluation.parser_name
            if parser_name not in parser_stats:
                parser_stats[parser_name] = {
                    'count': 0,
                    'structure_accuracies': [],
                    'content_accuracies': [],
                    'overall_accuracies': []
                }
            
            stats = parser_stats[parser_name]
            stats['count'] += 1
            if evaluation.structure_accuracy is not None:
                stats['structure_accuracies'].append(evaluation.structure_accuracy)
            if evaluation.content_accuracy is not None:
                stats['content_accuracies'].append(evaluation.content_accuracy)
            if evaluation.overall_accuracy is not None:
                stats['overall_accuracies'].append(evaluation.overall_accuracy)
        
        # 计算平均值
        parser_summary = {}
        for parser_name, stats in parser_stats.items():
            parser_summary[parser_name] = {
                'count': stats['count'],
                'avg_structure_accuracy': round(
                    sum(stats['structure_accuracies']) / len(stats['structure_accuracies']), 4
                ) if stats['structure_accuracies'] else 0,
                'avg_content_accuracy': round(
                    sum(stats['content_accuracies']) / len(stats['content_accuracies']), 4
                ) if stats['content_accuracies'] else 0,
                'avg_overall_accuracy': round(
                    sum(stats['overall_accuracies']) / len(stats['overall_accuracies']), 4
                ) if stats['overall_accuracies'] else 0
            }
        
        # 整体统计
        all_overall_accuracies = [e.overall_accuracy for e in evaluations if e.overall_accuracy is not None]
        best_parser = max(parser_summary.items(), key=lambda x: x[1]['avg_overall_accuracy']) if parser_summary else None
        
        overall_summary = {
            'total_evaluations': len(evaluations),
            'avg_overall_accuracy': round(
                sum(all_overall_accuracies) / len(all_overall_accuracies), 4
            ) if all_overall_accuracies else 0,
            'best_parser': {
                'name': best_parser[0],
                'accuracy': best_parser[1]['avg_overall_accuracy']
            } if best_parser else None
        }
        
        return {
            'dataset_name': dataset_name,
            'total_evaluations': len(evaluations),
            'parser_summary': parser_summary,
            'overall_summary': overall_summary
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评估摘要失败: {str(e)}")

@router.delete("/evaluations/{evaluation_id}")
async def delete_evaluation(evaluation_id: int, db: Session = Depends(get_database)):
    """删除评估结果"""
    try:
        evaluation = db.query(AccuracyEvaluation).filter(AccuracyEvaluation.id == evaluation_id).first()
        if not evaluation:
            raise HTTPException(status_code=404, detail=f"评估结果不存在: {evaluation_id}")
        
        db.delete(evaluation)
        db.commit()
        
        return {"message": f"评估结果 {evaluation_id} 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除评估结果失败: {str(e)}")
