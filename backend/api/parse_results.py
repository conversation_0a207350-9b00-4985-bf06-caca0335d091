"""
解析结果API

提供解析结果的访问功能，替代原有的静态文件访问
"""

import json
from pathlib import Path
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

router = APIRouter()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

@router.get("/datasets/{dataset_name}/parse_results")
async def get_parse_results(dataset_name: str):
    """
    获取数据集的解析结果
    
    替代原有的 /parse_results/{name}/latest_results.json 访问方式
    """
    try:
        # 构建解析结果文件路径
        results_dir = PROJECT_ROOT / "parse_results" / dataset_name
        latest_results_file = results_dir / "latest_results.json"
        
        # 检查最新结果文件是否存在
        if latest_results_file.exists():
            with open(latest_results_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        
        # 如果最新结果不存在，查找最新的结果文件
        if not results_dir.exists():
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 的解析结果不存在")
        
        # 查找所有解析结果文件
        result_files = list(results_dir.glob("parse_results_*.json"))
        if not result_files:
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 没有解析结果")
        
        # 按文件名排序，获取最新的
        result_files.sort(reverse=True)
        latest_file = result_files[0]
        
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取解析结果失败: {str(e)}")

@router.get("/datasets/{dataset_name}/parse_results/files")
async def get_parse_result_files(dataset_name: str):
    """获取数据集的所有解析结果文件列表"""
    try:
        results_dir = PROJECT_ROOT / "parse_results" / dataset_name
        
        if not results_dir.exists():
            return {"files": []}
        
        # 获取所有解析结果文件
        result_files = []
        for file_path in results_dir.glob("parse_results_*.json"):
            try:
                # 读取文件元数据
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                result_files.append({
                    "filename": file_path.name,
                    "timestamp": data.get("timestamp", ""),
                    "parse_date": data.get("metadata", {}).get("parse_date", ""),
                    "total_files": data.get("metadata", {}).get("total_files", 0),
                    "status": data.get("metadata", {}).get("status", "unknown"),
                    "file_size": file_path.stat().st_size
                })
            except Exception as e:
                # 如果文件损坏，跳过
                continue
        
        # 按时间戳排序
        result_files.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return {"files": result_files}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取解析结果文件列表失败: {str(e)}")

@router.get("/datasets/{dataset_name}/parse_results/{filename}")
async def get_specific_parse_result(dataset_name: str, filename: str):
    """获取特定的解析结果文件"""
    try:
        # 验证文件名格式
        if not filename.startswith("parse_results_") or not filename.endswith(".json"):
            raise HTTPException(status_code=400, detail="无效的解析结果文件名")
        
        # 构建文件路径
        file_path = PROJECT_ROOT / "parse_results" / dataset_name / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"解析结果文件不存在: {filename}")
        
        # 读取并返回JSON数据
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取解析结果文件失败: {str(e)}")

@router.get("/datasets/{dataset_name}/vl_llm_results")
async def get_vl_llm_results(dataset_name: str):
    """获取VL LLM结果"""
    try:
        vl_llm_dir = PROJECT_ROOT / "vl_llm_results" / dataset_name
        
        if not vl_llm_dir.exists():
            return {"results": []}
        
        # 扫描VL LLM结果文件
        results = []
        for file_path in vl_llm_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                results.append({
                    "filename": file_path.name,
                    "data": data
                })
            except Exception:
                continue
        
        return {"results": results}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取VL LLM结果失败: {str(e)}")

@router.get("/datasets/{dataset_name}/monkey_ocr/{image_name}")
async def get_monkey_ocr_result(dataset_name: str, image_name: str):
    """获取MonkeyOCR结果"""
    try:
        # 移除图片扩展名，添加.json
        base_name = Path(image_name).stem
        json_filename = f"{base_name}.json"
        
        monkey_ocr_file = PROJECT_ROOT / "dataset" / dataset_name / "monkey_ocr" / json_filename
        
        if not monkey_ocr_file.exists():
            raise HTTPException(status_code=404, detail=f"MonkeyOCR结果不存在: {json_filename}")
        
        with open(monkey_ocr_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MonkeyOCR结果失败: {str(e)}")

# 兼容原有的静态文件访问方式
@router.get("/parse_results/{dataset_name}/latest_results.json")
async def get_latest_results_legacy(dataset_name: str):
    """兼容原有的latest_results.json访问方式"""
    return await get_parse_results(dataset_name)
