"""
报告生成API

提供准确率报告生成和访问功能
"""

import json
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.database.database import get_database
from backend.database.crud import ReportCRUD, EvaluationCRUD
from backend.services.report_generator import ReportGenerator

router = APIRouter()

class ReportCreate(BaseModel):
    """创建报告请求模型"""
    dataset_name: str
    report_type: str = "accuracy"  # accuracy/comparison/summary
    title: str
    include_details: bool = True
    created_by: Optional[str] = None

class ReportResponse(BaseModel):
    """报告响应模型"""
    id: int
    dataset_name: str
    report_type: str
    title: str
    file_path: Optional[str]
    created_at: str
    created_by: Optional[str]

@router.post("/reports/generate", response_model=ReportResponse)
async def generate_report(report_request: ReportCreate, db: Session = Depends(get_database)):
    """生成准确率报告"""
    try:
        # 创建报告生成器
        generator = ReportGenerator(db)
        
        # 生成报告
        report_data = await generator.generate_accuracy_report(
            dataset_name=report_request.dataset_name,
            include_details=report_request.include_details
        )
        
        # 保存报告到数据库
        report_record = ReportCRUD.create(db, {
            "dataset_name": report_request.dataset_name,
            "report_type": report_request.report_type,
            "title": report_request.title,
            "content": json.dumps(report_data, ensure_ascii=False),
            "created_by": report_request.created_by
        })
        
        return ReportResponse(
            id=report_record.id,
            dataset_name=report_record.dataset_name,
            report_type=report_record.report_type,
            title=report_record.title,
            file_path=report_record.file_path,
            created_at=report_record.created_at.isoformat(),
            created_by=report_record.created_by
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

@router.get("/reports", response_model=List[ReportResponse])
async def get_reports(
    dataset_name: Optional[str] = None,
    report_type: Optional[str] = None,
    db: Session = Depends(get_database)
):
    """获取报告列表"""
    try:
        reports = ReportCRUD.get_all(db)
        
        # 应用筛选条件
        if dataset_name:
            reports = [r for r in reports if r.dataset_name == dataset_name]
        if report_type:
            reports = [r for r in reports if r.report_type == report_type]
        
        result = []
        for report in reports:
            result.append(ReportResponse(
                id=report.id,
                dataset_name=report.dataset_name,
                report_type=report.report_type,
                title=report.title,
                file_path=report.file_path,
                created_at=report.created_at.isoformat(),
                created_by=report.created_by
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告列表失败: {str(e)}")

@router.get("/reports/{report_id}")
async def get_report(report_id: int, db: Session = Depends(get_database)):
    """获取报告内容"""
    try:
        report = ReportCRUD.get_by_id(db, report_id)
        if not report:
            raise HTTPException(status_code=404, detail=f"报告不存在: {report_id}")
        
        # 解析报告内容
        try:
            content = json.loads(report.content)
        except json.JSONDecodeError:
            content = {"raw_content": report.content}
        
        return {
            "id": report.id,
            "dataset_name": report.dataset_name,
            "report_type": report.report_type,
            "title": report.title,
            "content": content,
            "file_path": report.file_path,
            "created_at": report.created_at.isoformat(),
            "created_by": report.created_by
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告失败: {str(e)}")

@router.delete("/reports/{report_id}")
async def delete_report(report_id: int, db: Session = Depends(get_database)):
    """删除报告"""
    try:
        report = ReportCRUD.get_by_id(db, report_id)
        if not report:
            raise HTTPException(status_code=404, detail=f"报告不存在: {report_id}")
        
        # 删除文件（如果存在）
        if report.file_path:
            file_path = Path(report.file_path)
            if file_path.exists():
                file_path.unlink()
        
        # 删除数据库记录
        db.delete(report)
        db.commit()
        
        return {"message": f"报告 {report_id} 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除报告失败: {str(e)}")

@router.get("/datasets/{dataset_name}/accuracy_summary")
async def get_accuracy_summary(dataset_name: str, db: Session = Depends(get_database)):
    """获取数据集的准确率摘要"""
    try:
        # 获取评估结果
        evaluations = EvaluationCRUD.get_by_dataset(db, dataset_name)
        
        if not evaluations:
            return {
                "dataset_name": dataset_name,
                "total_evaluations": 0,
                "parser_summary": {},
                "overall_summary": {}
            }
        
        # 按解析器统计
        parser_stats = {}
        for evaluation in evaluations:
            parser_name = evaluation.parser_name
            if parser_name not in parser_stats:
                parser_stats[parser_name] = {
                    "count": 0,
                    "structure_accuracy": [],
                    "content_accuracy": [],
                    "overall_accuracy": []
                }
            
            stats = parser_stats[parser_name]
            stats["count"] += 1
            if evaluation.structure_accuracy is not None:
                stats["structure_accuracy"].append(evaluation.structure_accuracy)
            if evaluation.content_accuracy is not None:
                stats["content_accuracy"].append(evaluation.content_accuracy)
            if evaluation.overall_accuracy is not None:
                stats["overall_accuracy"].append(evaluation.overall_accuracy)
        
        # 计算平均值
        parser_summary = {}
        for parser_name, stats in parser_stats.items():
            parser_summary[parser_name] = {
                "count": stats["count"],
                "avg_structure_accuracy": sum(stats["structure_accuracy"]) / len(stats["structure_accuracy"]) if stats["structure_accuracy"] else 0,
                "avg_content_accuracy": sum(stats["content_accuracy"]) / len(stats["content_accuracy"]) if stats["content_accuracy"] else 0,
                "avg_overall_accuracy": sum(stats["overall_accuracy"]) / len(stats["overall_accuracy"]) if stats["overall_accuracy"] else 0
            }
        
        # 整体统计
        all_overall_accuracy = [e.overall_accuracy for e in evaluations if e.overall_accuracy is not None]
        overall_summary = {
            "total_evaluations": len(evaluations),
            "avg_overall_accuracy": sum(all_overall_accuracy) / len(all_overall_accuracy) if all_overall_accuracy else 0,
            "best_parser": max(parser_summary.items(), key=lambda x: x[1]["avg_overall_accuracy"])[0] if parser_summary else None
        }
        
        return {
            "dataset_name": dataset_name,
            "total_evaluations": len(evaluations),
            "parser_summary": parser_summary,
            "overall_summary": overall_summary
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取准确率摘要失败: {str(e)}")
