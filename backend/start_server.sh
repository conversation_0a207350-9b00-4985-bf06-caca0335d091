#!/bin/bash

# TableRAG Backend 启动脚本

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "TableRAG Backend 启动脚本"
echo "========================="
echo "项目根目录: $PROJECT_ROOT"
echo "后端目录: $SCRIPT_DIR"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 python3 命令"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo "警告: 未检测到虚拟环境，建议在虚拟环境中运行"
    echo "可以使用以下命令创建虚拟环境:"
    echo "  python3 -m venv venv"
    echo "  source venv/bin/activate"
    echo ""
fi

# 安装依赖
echo "检查并安装依赖..."
cd "$SCRIPT_DIR"
pip install -r requirements.txt

# 设置环境变量
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 配置参数
HOST=${BACKEND_HOST:-"0.0.0.0"}
PORT=${BACKEND_PORT:-"8000"}
DEBUG=${DEBUG:-"false"}

echo ""
echo "启动配置:"
echo "  主机: $HOST"
echo "  端口: $PORT"
echo "  调试模式: $DEBUG"
echo "  数据库: ${DATABASE_URL:-"sqlite:///./tablerag_annotations.db"}"
echo ""

# 启动服务器
echo "启动 TableRAG Backend 服务..."
echo "API文档: http://$HOST:$PORT/docs"
echo "静态文件: http://$HOST:$PORT/static"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

cd "$SCRIPT_DIR"
python3 main.py
