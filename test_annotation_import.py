#!/usr/bin/env python3
"""
测试标注导入功能
"""

import requests
import json
import sys

def test_annotation_import():
    """测试从gen_data导入标注"""
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 首先检查test25数据集是否存在
        print("1. 检查数据集...")
        response = requests.get(f"{base_url}/api/datasets")
        if response.status_code != 200:
            print(f"获取数据集列表失败: {response.status_code}")
            return False
        
        datasets = response.json()
        test25_exists = any(ds['name'] == 'test25' for ds in datasets)
        
        if not test25_exists:
            print("test25数据集不存在，创建数据集...")
            create_response = requests.post(f"{base_url}/api/datasets", json={
                "name": "test25",
                "description": "测试数据集"
            })
            if create_response.status_code != 200:
                print(f"创建数据集失败: {create_response.status_code}")
                return False
        
        # 2. 检查图片是否存在
        print("2. 检查图片...")
        response = requests.get(f"{base_url}/api/datasets/test25/images")
        if response.status_code != 200:
            print(f"获取图片列表失败: {response.status_code}")
            return False
        
        images = response.json()
        table_1_exists = any(img['filename'] == 'table_1.png' for img in images)

        if not table_1_exists:
            print("table_1.png不存在，同步图片...")
            sync_response = requests.post(f"{base_url}/api/datasets/test25/images/sync")
            if sync_response.status_code != 200:
                print(f"同步图片失败: {sync_response.status_code}")
                print(f"响应内容: {sync_response.text}")
                return False
            sync_result = sync_response.json()
            print(f"同步结果: {sync_result}")
        
        # 3. 自动生成标注
        print("3. 自动生成标注...")
        response = requests.post(f"{base_url}/api/annotations/auto-generate/test25?annotator=test_user&overwrite=true")
        
        if response.status_code != 200:
            print(f"自动生成标注失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        result = response.json()
        print(f"生成结果: {result}")
        
        # 4. 检查生成的标注
        print("4. 检查生成的标注...")
        response = requests.get(f"{base_url}/api/annotations?dataset_name=test25")
        
        if response.status_code != 200:
            print(f"获取标注失败: {response.status_code}")
            return False
        
        annotations = response.json()
        if not annotations:
            print("没有找到标注数据")
            return False
        
        # 检查第一个标注的内容
        annotation = annotations[0]
        print(f"标注ID: {annotation['id']}")
        print(f"图片文件名: {annotation['image_filename']}")
        print(f"标注员: {annotation['annotator']}")
        
        # 解析表格结构
        try:
            structure = json.loads(annotation['table_structure'])
            print(f"表格结构:")
            print(f"  行数: {structure.get('rows', 0)}")
            print(f"  列数: {structure.get('cols', 0)}")
            print(f"  表格类型: {structure.get('table_type', 'unknown')}")
            print(f"  合并单元格数量: {len(structure.get('merged_cells', []))}")
            print(f"  嵌套表格数量: {len(structure.get('nested_tables', []))}")
            
            if structure.get('merged_cells'):
                print("  合并单元格详情:")
                for cell in structure['merged_cells']:
                    print(f"    行{cell['row']},列{cell['col']}: rowspan={cell['rowspan']}, colspan={cell['colspan']}")
        except json.JSONDecodeError as e:
            print(f"解析表格结构失败: {e}")
            return False
        
        # 检查表格内容
        content = annotation['table_content']
        print(f"表格内容长度: {len(content)}")
        
        # 检查关键信息是否保留
        checks = {
            "包含rowspan属性": 'rowspan=' in content,
            "包含colspan属性": 'colspan=' in content,
            "包含嵌套表格": 'nested-table' in content,
            "包含样式信息": 'style=' in content or '<style>' in content,
            "包含电子发票标题": '电子发票' in content
        }
        
        print("\n关键信息检查:")
        print("=" * 50)
        all_passed = True
        for check, passed in checks.items():
            status = "✓" if passed else "✗"
            print(f"  {status} {check}: {passed}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("\n✓ 所有检查通过！标注功能已成功修复")
        else:
            print("\n✗ 部分检查失败，需要进一步调试")
            print(f"保存的内容前500字符:")
            print(content[:500])
        
        return all_passed
        
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始测试标注导入功能...")
    print("=" * 60)
    
    success = test_annotation_import()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 测试通过！标注导入功能正常")
    else:
        print("✗ 测试失败！需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
