# 自动标注功能使用指南

## 功能概述

自动标注功能可以将生成的原始表格数据自动转换为人工标注数据，直接参与准确率指标计算。这个功能解决了手动标注工作量大的问题，特别适合测试和验证场景。

## 核心特性

- 🤖 **自动解析**: 从HTML表格数据中自动提取结构和内容信息
- 📊 **标准格式**: 生成符合标注系统要求的JSON结构和Markdown内容
- 🔄 **无缝集成**: 集成到现有的解析流程中，支持批量处理
- 🎯 **准确率计算**: 自动标注数据可直接参与准确率评估
- 🌐 **API支持**: 提供完整的REST API接口
- 💻 **前端管理**: 在analyzer中提供可视化管理界面

## 工作流程

```mermaid
graph TD
    A[生成表格数据] --> B[HTML表格文件]
    B --> C[表格解析器]
    C --> D[提取结构信息]
    C --> E[生成Markdown内容]
    D --> F[JSON结构数据]
    E --> F
    F --> G[创建标注记录]
    G --> H[存储到数据库]
    H --> I[参与准确率计算]
```

## 快速开始

### 1. 环境准备

确保以下服务正在运行：

```bash
# 启动后端服务
./backend/start_server.sh

# 检查服务状态
curl http://localhost:8000/health
```

### 2. 生成测试数据

```bash
# 进入parser目录
cd parser

# 生成表格数据（会自动创建标注）
DATASET_NAME=test20 ./table_parser.sh --num-tables=2
```

### 3. 手动触发自动标注

如果需要单独运行自动标注：

```bash
# 只运行自动标注
./table_parser.sh --mode=annotate

# 或者通过API触发
curl -X POST http://localhost:8000/api/annotations/auto-generate/test20
```

### 4. 查看结果

```bash
# 启动前端界面
cd analyzer
npm start

# 访问 http://localhost:3000
# 切换到"人工标注"标签，然后选择"数据集管理"
```

## 详细使用说明

### 命令行使用

#### 完整流程（推荐）
```bash
# 生成数据并自动创建标注
DATASET_NAME=my_test ./table_parser.sh --generate-data --num-tables=5
```

#### 分步执行
```bash
# 1. 只生成数据
./table_parser.sh --mode=parse --generate-data

# 2. 单独运行自动标注
./table_parser.sh --mode=annotate

# 3. 生成报告
./table_parser.sh --mode=report
```

### API使用

#### 自动生成标注
```bash
# 为指定数据集自动生成标注
curl -X POST "http://localhost:8000/api/annotations/auto-generate/test20?annotator=auto_generator&overwrite=false"
```

#### 批量创建标注
```bash
curl -X POST http://localhost:8000/api/annotations/batch \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_name": "test20",
    "overwrite": false,
    "annotations": [
      {
        "image_filename": "table_1.png",
        "annotator": "auto_generator",
        "table_structure": "{\"rows\": 3, \"cols\": 3}",
        "table_content": "| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 数据1 | 数据2 | 数据3 |",
        "annotation_type": "auto_generated",
        "status": "completed"
      }
    ]
  }'
```

#### 查询标注
```bash
# 获取数据集的所有标注
curl "http://localhost:8000/api/annotations?dataset_name=test20"

# 获取标注统计
curl "http://localhost:8000/api/datasets/test20/accuracy_summary"
```

### 前端界面使用

1. **访问analyzer**: http://localhost:3000
2. **选择数据集**: 在顶部下拉框中选择数据集
3. **切换到标注模式**: 点击"人工标注"标签
4. **管理标注**: 选择"数据集管理"子标签
5. **自动生成**: 点击"自动生成标注"按钮
6. **查看结果**: 在标注列表中查看生成的标注

## 配置选项

### 环境变量

```bash
# 后端服务地址
export BACKEND_URL=http://localhost:8000

# 生成数据目录
export GEN_DATA_DIR=/path/to/gen_data

# 图片目录
export IMAGES_DIR=/path/to/images

# 数据集名称
export DATASET_NAME=test20
```

### 自动标注参数

- `annotator`: 标注员名称（默认: auto_generator）
- `overwrite`: 是否覆盖已存在的标注（默认: false）
- `annotation_type`: 标注类型（默认: auto_generated）
- `status`: 标注状态（默认: completed）

## 故障排除

### 常见问题

1. **无法连接后端服务**
   ```bash
   # 检查服务状态
   curl http://localhost:8000/health
   
   # 重启服务
   ./backend/start_server.sh
   ```

2. **表格解析失败**
   ```bash
   # 检查HTML文件是否存在
   ls -la dataset/test20/gen_data/
   
   # 检查文件格式
   file dataset/test20/gen_data/table_1.html
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库配置
   echo $DATABASE_URL
   
   # 测试数据库连接
   python -c "from backend.database.database import get_database; print('数据库连接正常')"
   ```

### 调试模式

```bash
# 启用调试模式
DEBUG=true ./backend/start_server.sh

# 查看详细日志
./table_parser.sh --debug --mode=annotate
```

## 扩展开发

### 自定义解析器

```python
from parser.src.utils.table_parser import TableDataParser

class CustomTableParser(TableDataParser):
    def _generate_table_structure(self, table_data):
        # 自定义结构解析逻辑
        structure = super()._generate_table_structure(table_data)
        # 添加自定义字段
        structure['custom_field'] = 'custom_value'
        return structure
```

### 自定义标注服务

```python
from parser.src.services.auto_annotation import AutoAnnotationService

class CustomAnnotationService(AutoAnnotationService):
    def _create_annotation_for_image(self, dataset_name, image_filename, gen_data_dir, annotator):
        # 自定义标注创建逻辑
        result = super()._create_annotation_for_image(dataset_name, image_filename, gen_data_dir, annotator)
        # 添加后处理逻辑
        return result
```

## 性能优化

- 批量处理时建议每次处理不超过100个文件
- 大数据集可以分批次处理
- 使用数据库索引优化查询性能
- 考虑使用缓存减少重复解析

## 许可证

MIT License
