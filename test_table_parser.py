#!/usr/bin/env python3
"""
测试表格解析器修复效果
"""

import sys
import os
import json

# 添加parser模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'parser/src'))

from utils.table_parser import TableDataParser

def test_complex_table():
    """测试复杂表格解析"""
    
    # 读取测试HTML文件
    html_file = "dataset/test25/gen_data/table_1.html"
    
    if not os.path.exists(html_file):
        print(f"测试文件不存在: {html_file}")
        return False
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print("原始HTML内容:")
        print("=" * 50)
        print(html_content[:500] + "..." if len(html_content) > 500 else html_content)
        print("=" * 50)
        
        # 创建解析器
        parser = TableDataParser()
        
        # 解析表格
        result = parser.parse_html_table(html_content)
        
        if not result:
            print("解析失败：未返回结果")
            return False
        
        print("\n解析结果:")
        print("=" * 50)
        
        # 打印表格结构
        structure = json.loads(result['table_structure'])
        print("表格结构信息:")
        print(f"  行数: {structure.get('rows', 0)}")
        print(f"  列数: {structure.get('cols', 0)}")
        print(f"  是否有表头: {structure.get('has_header', False)}")
        print(f"  表格类型: {structure.get('table_type', 'unknown')}")
        print(f"  合并单元格数量: {len(structure.get('merged_cells', []))}")
        print(f"  嵌套表格数量: {len(structure.get('nested_tables', []))}")
        
        if structure.get('merged_cells'):
            print("  合并单元格详情:")
            for cell in structure['merged_cells']:
                print(f"    行{cell['row']},列{cell['col']}: rowspan={cell['rowspan']}, colspan={cell['colspan']}")
        
        if structure.get('nested_tables'):
            print("  嵌套表格详情:")
            for nested in structure['nested_tables']:
                print(f"    位置: 行{nested['row']},列{nested['col']}")
        
        print("\n保存的表格内容:")
        print("=" * 50)
        saved_content = result['table_content']
        print(saved_content[:800] + "..." if len(saved_content) > 800 else saved_content)
        
        # 检查关键信息是否保留
        checks = {
            "包含rowspan属性": 'rowspan=' in saved_content,
            "包含colspan属性": 'colspan=' in saved_content,
            "包含嵌套表格": '<table class="nested-table">' in saved_content,
            "包含样式信息": 'style=' in saved_content or '<style>' in saved_content,
            "包含电子发票标题": '电子发票' in saved_content
        }
        
        print("\n关键信息检查:")
        print("=" * 50)
        all_passed = True
        for check, passed in checks.items():
            status = "✓" if passed else "✗"
            print(f"  {status} {check}: {passed}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始测试表格解析器修复效果...")
    print("=" * 60)
    
    success = test_complex_table()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 测试通过！表格解析器已成功修复")
    else:
        print("✗ 测试失败！需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
