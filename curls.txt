
第1路解析：MonkeyOCR（parse）

上传图片：
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/upload?upload_id=om7f8u8cxqs' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryNKTk3Rnh3AearNlM' \
  -H 'Origin: http://vlrlabmonkey.xyz:7685' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw （图片内容省略）
  --insecure ;
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/upload_progress?upload_id=om7f8u8cxqs' \
  -H 'Accept: text/event-stream' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --insecure ;
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/join?' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://vlrlabmonkey.xyz:7685' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'content-type: application/json' \
  --data-raw '{"data":[{"path":"/tmp/gradio/8dd58f0b9edb5e6ec3c5819b83f1f778afaef4c240e538ddb639febe1dc19578/image 1.png","url":"http://vlrlabmonkey.xyz:7685/gradio_api/file=/tmp/gradio/8dd58f0b9edb5e6ec3c5819b83f1f778afaef4c240e538ddb639febe1dc19578/image 1.png","orig_name":"image (1).png","size":3026957,"mime_type":"image/png","meta":{"_type":"gradio.FileData"}},null],"event_data":null,"fn_index":0,"trigger_id":7,"session_hash":"0enn2zo9rq"}' \
  --insecure ; curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/data?session_hash=0enn2zo9rq' \
  -H 'Accept: text/event-stream' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --insecure ;
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/file=/tmp/gradio/11d568b20c3d678652a9eb9e82f7514ac4f9020648cb90c67f227e1a195c097c/image.webp' \
  -H 'Accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --insecure

点击解析：
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/join?' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://vlrlabmonkey.xyz:7685' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'content-type: application/json' \
  --data-raw '{"data":[null,null],"event_data":null,"fn_index":3,"trigger_id":10,"session_hash":"0enn2zo9rq"}' \
  --insecure ;
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/data?session_hash=0enn2zo9rq' \
  -H 'Accept: text/event-stream' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --insecure


第2路解析：MonkeyOCR（table）
上传图片（跟第1路一样）
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/join?' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://vlrlabmonkey.xyz:7685' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'content-type: application/json' \
  --data-raw '{"data":["This is the image of a table. Please output the table in html format.",null],"event_data":null,"fn_index":4,"trigger_id":11,"session_hash":"0enn2zo9rq"}' \
  --insecure ;
curl 'http://vlrlabmonkey.xyz:7685/gradio_api/queue/data?session_hash=0enn2zo9rq' \
  -H 'Accept: text/event-stream' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Pragma: no-cache' \
  -H 'Referer: http://vlrlabmonkey.xyz:7685/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --insecure