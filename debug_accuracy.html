<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Debug Accuracy Calculation</title>
</head>
<body>
    <h1>Debug Accuracy Calculation</h1>
    
    <div id="results"></div>

    <script>
        // Copy the parsing functions from dataProcessor.js
        const parseTableContent = (content) => {
            try {
                // 尝试解析HTML表格
                if (content.includes('<table')) {
                    return parseHTMLTable(content);
                }
                
                // 尝试解析Markdown表格
                if (content.includes('|')) {
                    return parseMarkdownTable(content);
                }
                
                return null;
            } catch (error) {
                return null;
            }
        };

        const parseHTMLTable = (htmlContent) => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            const table = doc.querySelector('table');
            
            if (!table) return null;
            
            const rows = [];
            const trs = table.querySelectorAll('tr');
            
            trs.forEach(tr => {
                const cells = [];
                const tds = tr.querySelectorAll('th, td');
                tds.forEach(td => {
                    cells.push(td.textContent.trim());
                });
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });
            
            return rows;
        };

        const parseMarkdownTable = (markdownContent) => {
            const lines = markdownContent.split('\n').map(line => line.trim()).filter(line => line);
            const tableLines = lines.filter(line => line.startsWith('|') && line.endsWith('|'));

            if (tableLines.length < 2) return null;

            // 移除分隔符行
            const dataLines = tableLines.filter(line => !line.match(/^\|[\s\-\|]+\|$/));

            const rows = [];
            dataLines.forEach(line => {
                const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            return rows;
        };

        const removeTrailingEmptyColumns = (table) => {
            if (!table || table.length === 0) return table;

            // 找到最大列数
            const maxCols = Math.max(...table.map(row => row.length));

            // 从右到左检查每一列是否为空
            let lastNonEmptyCol = -1;
            for (let col = 0; col < maxCols; col++) {
                let hasContent = false;
                for (let row = 0; row < table.length; row++) {
                    const cell = (table[row][col] || '').trim();
                    if (cell !== '') {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    lastNonEmptyCol = col;
                }
            }

            // 如果所有列都为空，返回原表格
            if (lastNonEmptyCol === -1) return table;

            // 移除尾部空列
            return table.map(row => row.slice(0, lastNonEmptyCol + 1));
        };

        const compareTableData = (table1, table2) => {
            if (!table1 || !table2) return 0;

            // 检查行数
            if (table1.length !== table2.length) {
                return Math.max(0, 100 - Math.abs(table1.length - table2.length) * 10);
            }

            // 移除尾部的空列
            const cleanTable1 = removeTrailingEmptyColumns(table1);
            const cleanTable2 = removeTrailingEmptyColumns(table2);

            let totalCells = 0;
            let matchingCells = 0;

            for (let i = 0; i < cleanTable1.length; i++) {
                const row1 = cleanTable1[i] || [];
                const row2 = cleanTable2[i] || [];
                const maxCols = Math.max(row1.length, row2.length);

                for (let j = 0; j < maxCols; j++) {
                    totalCells++;
                    const cell1 = (row1[j] || '').toLowerCase().trim();
                    const cell2 = (row2[j] || '').toLowerCase().trim();

                    // 对于中文括号的兼容性处理
                    const normalizedCell1 = cell1.replace(/（/g, '(').replace(/）/g, ')');
                    const normalizedCell2 = cell2.replace(/（/g, '(').replace(/）/g, ')');

                    if (normalizedCell1 === normalizedCell2) {
                        matchingCells++;
                    }
                }
            }

            return totalCells > 0 ? (matchingCells / totalCells) * 100 : 0;
        };

        // Test data
        const groundTruthHTML = `<table><tr><th>项目</th><th>营业收入（万元）</th><th>营业成本（万元）</th><th>毛利润（万元）</th><th>毛利率（%）</th><th>净利润（万元）</th></tr><tr><td>主营业务收入</td><td>12000</td><td>8400</td><td>3600</td><td>30</td><td>2100</td></tr><tr><td>投资收益</td><td>500</td><td>200</td><td>300</td><td>60</td><td>180</td></tr><tr><td>其他业务收入</td><td>800</td><td>400</td><td>400</td><td>50</td><td>280</td></tr><tr><td>营业费用</td><td>1000</td><td>800</td><td>200</td><td>20</td><td>150</td></tr></table>`;

        const kdcMarkdown = `| 项目   | 营业收入(万元)   | 营业成本(万元)   | 毛利润(万元)   | 毛利率(%)   | 净利润(万元)   |  |
| --- | --- | --- | --- | --- | --- | --- |
| 主营业务收入   | 12000   | 8400   | 3600   | 30   | 2100   |  |
| 投资收益   | 500   | 200   | 300   | 60   | 180   |  |
| 其他业务收入   | 800   | 400   | 400   | 50   | 280   |  |
| 营业费用   | 1000   | 800   | 200   | 20   | 150   |  |`;

        // Parse tables
        const groundTruthTable = parseTableContent(groundTruthHTML);
        const kdcTable = parseTableContent(kdcMarkdown);

        // Clean tables
        const cleanGroundTruthTable = removeTrailingEmptyColumns(groundTruthTable);
        const cleanKdcTable = removeTrailingEmptyColumns(kdcTable);

        // Calculate accuracy
        const accuracy = compareTableData(groundTruthTable, kdcTable);

        // Display results
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = `
            <h2>Original Ground Truth Table (${groundTruthTable.length} rows):</h2>
            <pre>${JSON.stringify(groundTruthTable, null, 2)}</pre>

            <h2>Original KDC Table (${kdcTable.length} rows):</h2>
            <pre>${JSON.stringify(kdcTable, null, 2)}</pre>

            <h2>Cleaned Ground Truth Table (${cleanGroundTruthTable.length} rows):</h2>
            <pre>${JSON.stringify(cleanGroundTruthTable, null, 2)}</pre>

            <h2>Cleaned KDC Table (${cleanKdcTable.length} rows):</h2>
            <pre>${JSON.stringify(cleanKdcTable, null, 2)}</pre>

            <h2>Accuracy: ${accuracy.toFixed(1)}%</h2>

            <h2>Column Count Comparison:</h2>
            <p>Original - Ground Truth: ${groundTruthTable[0]?.length || 0} columns, KDC: ${kdcTable[0]?.length || 0} columns</p>
            <p>Cleaned - Ground Truth: ${cleanGroundTruthTable[0]?.length || 0} columns, KDC: ${cleanKdcTable[0]?.length || 0} columns</p>
        `;
    </script>
</body>
</html>
