#!/usr/bin/env python3
"""
修复旧数据的特征指标脚本
为parse_results目录下的旧数据添加KDC特征信息
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Tuple
from collections import Counter
import re

def calculate_kdc_features(doc_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算KDC解析结果的特征信息
    
    Args:
        doc_data: KDC解析结果中的doc数据
        
    Returns:
        特征信息字典，包含bbox数量和位置指标
    """
    features = {
        "bbox_count": 0,
        "bbox_position_metrics": {
            "avg_x": 0,
            "avg_y": 0,
            "avg_width": 0,
            "avg_height": 0,
            "total_area": 0,
            "bbox_density": 0  # bbox数量/页面面积
        },
        "bbox_types": {
            "textbox": 0,
            "table": 0,
            "component": 0,
            "table_cell": 0
        },
        "table_detection": {
            "has_table": False,  # 是否检测到表格
            "table_count": 0,    # 表格数量
            "table_areas": []    # 表格区域信息
        },
        "textbox_count_distribution": {
            "cell_textbox_counts": {},  # {textbox_count: cell_count}
            "total_cells": 0,
            "max_textboxes_per_cell": 0,
            "avg_textboxes_per_cell": 0,
            "outlier_cells": []  # 包含异常多文本框的单元格信息
        }
    }
    
    if not doc_data or not isinstance(doc_data, dict):
        return features
    
    bboxes = []
    page_width = 0
    page_height = 0
    
    def extract_bboxes_recursive(data, level=0):
        """递归提取所有bounding_box信息"""
        nonlocal page_width, page_height
        
        if isinstance(data, dict):
            # 提取页面尺寸信息
            if "page_size" in data:
                page_size = data["page_size"]
                if isinstance(page_size, dict):
                    page_width = max(page_width, page_size.get("width", 0))
                    page_height = max(page_height, page_size.get("height", 0))
            
            # 处理bounding_box
            if "bounding_box" in data:
                bbox = data["bounding_box"]
                if isinstance(bbox, dict) and all(k in bbox for k in ["x1", "y1", "x2", "y2"]):
                    x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
                    width = x2 - x1
                    height = y2 - y1
                    
                    if width > 0 and height > 0:  # 只统计有效的bbox
                        bbox_info = {
                            "x1": x1, "y1": y1, "x2": x2, "y2": y2,
                            "width": width, "height": height,
                            "area": width * height,
                            "center_x": (x1 + x2) / 2,
                            "center_y": (y1 + y2) / 2
                        }
                        
                        # 确定bbox类型
                        bbox_type = "unknown"
                        if "textbox" in data:
                            bbox_type = "textbox"
                        elif "table" in data:
                            bbox_type = "table"
                        elif "component" in data:
                            bbox_type = "component"
                        elif "col_span" in data or "row_span" in data:  # 表格单元格
                            bbox_type = "table_cell"
                        
                        bbox_info["type"] = bbox_type
                        bboxes.append(bbox_info)
            
            # 递归处理所有子项
            for key, value in data.items():
                if key not in ["bounding_box"]:  # 避免重复处理
                    extract_bboxes_recursive(value, level + 1)
        
        elif isinstance(data, list):
            for item in data:
                extract_bboxes_recursive(item, level)
    
    # 开始递归提取
    extract_bboxes_recursive(doc_data)
    
    # 计算特征
    if bboxes:
        features["bbox_count"] = len(bboxes)
        
        # 计算位置指标
        total_x = sum(bbox["center_x"] for bbox in bboxes)
        total_y = sum(bbox["center_y"] for bbox in bboxes)
        total_width = sum(bbox["width"] for bbox in bboxes)
        total_height = sum(bbox["height"] for bbox in bboxes)
        total_area = sum(bbox["area"] for bbox in bboxes)
        
        features["bbox_position_metrics"]["avg_x"] = round(total_x / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_y"] = round(total_y / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_width"] = round(total_width / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_height"] = round(total_height / len(bboxes), 2)
        features["bbox_position_metrics"]["total_area"] = total_area
        
        # 计算bbox密度（如果有页面尺寸信息）
        if page_width > 0 and page_height > 0:
            page_area = page_width * page_height
            features["bbox_position_metrics"]["bbox_density"] = round(len(bboxes) / page_area * 1000000, 4)  # 每百万像素的bbox数量
        
        # 统计各类型bbox数量
        table_bboxes = []
        for bbox in bboxes:
            bbox_type = bbox["type"]
            if bbox_type in features["bbox_types"]:
                features["bbox_types"][bbox_type] += 1

            # 收集表格类型的bbox
            if bbox_type == "table":
                table_bboxes.append(bbox)

        # 计算表格检测特征
        features["table_detection"]["table_count"] = len(table_bboxes)
        features["table_detection"]["has_table"] = len(table_bboxes) > 0

        # 记录表格区域信息
        for table_bbox in table_bboxes:
            table_area = {
                "x1": table_bbox["x1"],
                "y1": table_bbox["y1"],
                "x2": table_bbox["x2"],
                "y2": table_bbox["y2"],
                "width": table_bbox["width"],
                "height": table_bbox["height"],
                "area": table_bbox["area"]
            }
            features["table_detection"]["table_areas"].append(table_area)

    # 计算表格单元格文本框数量分布
    textbox_distribution = calculate_textbox_distribution(doc_data)
    features["textbox_count_distribution"] = textbox_distribution

    return features


def calculate_textbox_distribution(doc_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算表格单元格中文本框数量的分布统计

    Args:
        doc_data: KDC解析结果中的doc数据

    Returns:
        文本框数量分布统计信息
    """
    distribution = {
        "cell_textbox_counts": {},  # {textbox_count: cell_count}
        "total_cells": 0,
        "max_textboxes_per_cell": 0,
        "avg_textboxes_per_cell": 0,
        "outlier_cells": []  # 包含异常多文本框的单元格信息
    }

    if not doc_data or not isinstance(doc_data, dict):
        return distribution

    cell_textbox_counts = []  # 存储每个单元格的文本框数量

    def extract_table_cells_recursive(data, level=0):
        """递归提取所有表格单元格信息"""
        if isinstance(data, dict):
            # 处理表格结构
            if "table" in data:
                table = data["table"]
                if isinstance(table, dict) and "rows" in table:
                    rows = table["rows"]
                    if isinstance(rows, list):
                        for row in rows:
                            if isinstance(row, dict) and "cells" in row:
                                cells = row["cells"]
                                if isinstance(cells, list):
                                    for cell in cells:
                                        if isinstance(cell, dict):
                                            # 统计当前单元格中的文本框数量
                                            textbox_count = count_textboxes_in_cell(cell)
                                            cell_textbox_counts.append(textbox_count)

            # 递归处理所有子项
            for key, value in data.items():
                extract_table_cells_recursive(value, level + 1)

        elif isinstance(data, list):
            for item in data:
                extract_table_cells_recursive(item, level)

    def count_textboxes_in_cell(cell: Dict[str, Any]) -> int:
        """统计单元格中的文本框数量"""
        textbox_count = 0

        if "blocks" in cell and isinstance(cell["blocks"], list):
            for block in cell["blocks"]:
                if isinstance(block, dict) and "textbox" in block:
                    textbox_count += 1

        return textbox_count

    # 开始递归提取
    extract_table_cells_recursive(doc_data)

    # 计算分布统计
    if cell_textbox_counts:
        distribution["total_cells"] = len(cell_textbox_counts)
        distribution["max_textboxes_per_cell"] = max(cell_textbox_counts)
        distribution["avg_textboxes_per_cell"] = round(sum(cell_textbox_counts) / len(cell_textbox_counts), 2)

        # 统计每种文本框数量对应的单元格数量
        for count in cell_textbox_counts:
            if count in distribution["cell_textbox_counts"]:
                distribution["cell_textbox_counts"][count] += 1
            else:
                distribution["cell_textbox_counts"][count] = 1

        # 检测异常单元格（文本框数量明显高于平均值）
        avg_count = distribution["avg_textboxes_per_cell"]
        threshold = avg_count * 2  # 超过平均值2倍的认为是异常

        outlier_counts = [count for count in cell_textbox_counts if count > threshold and count > 1]
        if outlier_counts:
            distribution["outlier_cells"] = [
                {
                    "textbox_count": count,
                    "is_outlier": True,
                    "threshold": threshold
                }
                for count in set(outlier_counts)  # 去重
            ]

    return distribution


def extract_table_matrix(data: Any, parser_type: str) -> List[List[str]]:
    """
    从不同解析器的结果中提取表格矩阵

    Args:
        data: 解析器结果数据
        parser_type: 解析器类型 ('kdc', 'monkey_ocr', 'monkey_ocr_latex', 'vl_llm')

    Returns:
        表格矩阵，每个单元格为字符串
    """
    if not data:
        return []

    try:
        if parser_type == 'kdc':
            # KDC结果：从doc.tree中提取表格
            if isinstance(data, dict) and 'doc' in data:
                doc = data['doc']
                if isinstance(doc, dict) and 'tree' in doc:
                    return extract_kdc_table_matrix(doc['tree'])

        elif parser_type in ['monkey_ocr', 'monkey_ocr_latex']:
            # MonkeyOCR结果：从HTML表格中提取
            if isinstance(data, list) and len(data) > 0:
                result = data[0]  # 取第一个结果
                if isinstance(result, dict) and 'result' in result:
                    result_data = result['result']
                    if isinstance(result_data, dict):
                        # 尝试从html字段提取
                        html_content = result_data.get('html', '')
                        if html_content:
                            return extract_html_table_matrix(html_content)

                        # 尝试从text_content字段提取
                        text_content = result_data.get('text_content', [])
                        if text_content and isinstance(text_content, list) and len(text_content) > 0:
                            html_content = text_content[0]
                            if html_content:
                                return extract_html_table_matrix(html_content)

        elif parser_type == 'vl_llm':
            # VL-LLM结果：从结果中提取
            if isinstance(data, list) and len(data) > 0:
                result = data[0]  # 取第一个结果
                if isinstance(result, dict) and 'result' in result:
                    result_data = result['result']
                    if isinstance(result_data, dict):
                        # VL-LLM可能有不同的输出格式，需要根据实际情况调整
                        content = result_data.get('content', '') or result_data.get('text', '')
                        if content:
                            return extract_html_table_matrix(content)

    except Exception as e:
        print(f"Error extracting table matrix for {parser_type}: {e}")

    return []


def extract_kdc_table_matrix(tree_data: Any) -> List[List[str]]:
    """从KDC的tree结构中提取表格矩阵"""
    matrix = []

    def extract_table_recursive(data):
        if isinstance(data, dict):
            if "table" in data:
                table = data["table"]
                if isinstance(table, dict) and "rows" in table:
                    rows = table["rows"]
                    if isinstance(rows, list):
                        for row in rows:
                            if isinstance(row, dict) and "cells" in row and row["cells"]:
                                row_cells = []
                                cells = row["cells"]
                                if isinstance(cells, list):
                                    for cell in cells:
                                        if isinstance(cell, dict):
                                            cell_text = extract_cell_text(cell)
                                            row_cells.append(cell_text)
                                if row_cells:
                                    matrix.append(row_cells)

            # 递归处理所有子项
            for key, value in data.items():
                extract_table_recursive(value)

        elif isinstance(data, list):
            for item in data:
                extract_table_recursive(item)

    def extract_cell_text(cell: Dict[str, Any]) -> str:
        """从单元格中提取文本内容"""
        text_parts = []

        if "blocks" in cell and isinstance(cell["blocks"], list):
            for block in cell["blocks"]:
                if isinstance(block, dict) and "textbox" in block:
                    textbox = block["textbox"]
                    if isinstance(textbox, dict) and "text" in textbox:
                        text = textbox["text"]
                        if isinstance(text, str):
                            text_parts.append(text.strip())

        return " ".join(text_parts).strip()

    extract_table_recursive(tree_data)
    return matrix


def extract_html_table_matrix(html_content: str) -> List[List[str]]:
    """从HTML表格中提取表格矩阵"""
    if not html_content:
        return []

    matrix = []

    # 简单的HTML表格解析
    # 查找所有<table>标签
    table_pattern = r'<table[^>]*>(.*?)</table>'
    table_matches = re.findall(table_pattern, html_content, re.DOTALL | re.IGNORECASE)

    if not table_matches:
        return []

    # 取第一个表格
    table_content = table_matches[0]

    # 查找所有<tr>标签
    row_pattern = r'<tr[^>]*>(.*?)</tr>'
    row_matches = re.findall(row_pattern, table_content, re.DOTALL | re.IGNORECASE)

    for row_content in row_matches:
        # 查找所有<td>或<th>标签
        cell_pattern = r'<t[dh][^>]*>(.*?)</t[dh]>'
        cell_matches = re.findall(cell_pattern, row_content, re.DOTALL | re.IGNORECASE)

        if cell_matches:
            row_cells = []
            for cell_content in cell_matches:
                # 清理HTML标签和多余空白
                clean_text = re.sub(r'<[^>]+>', '', cell_content)
                clean_text = re.sub(r'\s+', ' ', clean_text).strip()
                row_cells.append(clean_text)

            if row_cells:
                matrix.append(row_cells)

    return matrix


def calculate_table_accuracy(gt_matrix: List[List[str]], pred_matrix: List[List[str]]) -> float:
    """
    计算表格准确率（基于单元格内容匹配）

    Args:
        gt_matrix: 标准答案表格矩阵
        pred_matrix: 预测结果表格矩阵

    Returns:
        准确率（0-1之间的浮点数）
    """
    if not gt_matrix or not pred_matrix:
        return 0.0

    # 将矩阵展平为单元格列表
    gt_flat = [cell for row in gt_matrix for cell in row]
    pred_flat = [cell for row in pred_matrix for cell in row]

    if not gt_flat:
        return 0.0

    # 使用Counter计算匹配的单元格数量
    gt_counter = Counter(gt_flat)
    pred_counter = Counter(pred_flat)

    # 计算命中的单元格数量
    hit_counter = gt_counter & pred_counter
    hit_count = sum(hit_counter.values())

    # 计算准确率
    total_count = len(gt_flat)
    return hit_count / total_count if total_count > 0 else 0.0


def calculate_accuracy_metrics(result_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算各解析器的准确率指标

    Args:
        result_data: 包含所有解析结果的数据

    Returns:
        准确率指标字典
    """
    accuracy_metrics = {
        "kdc_vs_monkey_ocr": 0.0,
        "kdc_vs_monkey_ocr_latex": 0.0,
        "monkey_ocr_vs_monkey_ocr_latex": 0.0,
        "has_comparison_data": False
    }

    try:
        # 提取各解析器的表格矩阵
        kdc_matrix = []
        monkey_ocr_matrix = []
        monkey_ocr_latex_matrix = []

        # 从KDC结果中提取表格矩阵
        if 'kdc_kdc' in result_data and result_data['kdc_kdc']:
            kdc_data = result_data['kdc_kdc']
            if isinstance(kdc_data, list) and len(kdc_data) > 0:
                kdc_result = kdc_data[0]
                if isinstance(kdc_result, dict) and 'result' in kdc_result:
                    kdc_matrix = extract_table_matrix(kdc_result['result'], 'kdc')

        # 从MonkeyOCR结果中提取表格矩阵
        if 'monkey_ocr' in result_data and result_data['monkey_ocr']:
            monkey_ocr_matrix = extract_table_matrix(result_data['monkey_ocr'], 'monkey_ocr')

        # 从MonkeyOCR LaTeX结果中提取表格矩阵
        if 'monkey_ocr_latex' in result_data and result_data['monkey_ocr_latex']:
            monkey_ocr_latex_matrix = extract_table_matrix(result_data['monkey_ocr_latex'], 'monkey_ocr_latex')

        # 计算准确率比较
        if kdc_matrix and monkey_ocr_matrix:
            accuracy_metrics["kdc_vs_monkey_ocr"] = calculate_table_accuracy(kdc_matrix, monkey_ocr_matrix)
            accuracy_metrics["has_comparison_data"] = True

        if kdc_matrix and monkey_ocr_latex_matrix:
            accuracy_metrics["kdc_vs_monkey_ocr_latex"] = calculate_table_accuracy(kdc_matrix, monkey_ocr_latex_matrix)
            accuracy_metrics["has_comparison_data"] = True

        if monkey_ocr_matrix and monkey_ocr_latex_matrix:
            accuracy_metrics["monkey_ocr_vs_monkey_ocr_latex"] = calculate_table_accuracy(monkey_ocr_matrix, monkey_ocr_latex_matrix)
            accuracy_metrics["has_comparison_data"] = True

    except Exception as e:
        print(f"Error calculating accuracy metrics: {e}")

    return accuracy_metrics


def fix_json_file(file_path: Path) -> bool:
    """
    修复单个JSON文件，为KDC KDC结果添加特征信息
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        是否成功修复
    """
    try:
        print(f"处理文件: {file_path}")
        
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否有parse_results
        if "parse_results" not in data:
            print(f"  跳过: 没有parse_results字段")
            return False
        
        parse_results = data["parse_results"]
        
        # 检查是否有kdc_kdc结果
        if "kdc_kdc" not in parse_results:
            print(f"  跳过: 没有kdc_kdc结果")
            return False
        
        kdc_kdc_results = parse_results["kdc_kdc"]
        if not isinstance(kdc_kdc_results, list):
            print(f"  跳过: kdc_kdc结果格式不正确")
            return False
        
        modified = False
        
        # 为每个KDC KDC结果添加特征信息
        for i, result in enumerate(kdc_kdc_results):
            if not isinstance(result, dict):
                continue
                
            # 检查是否已经有完整的特征信息（包括新的textbox_count_distribution字段、accuracy_metrics字段和table_detection字段）
            if ("features" in result and
                isinstance(result["features"], dict) and
                "textbox_count_distribution" in result["features"] and
                "accuracy_metrics" in result["features"] and
                "table_detection" in result["features"]):
                print(f"  结果 {i+1}: 已有完整特征信息，跳过")
                continue
            
            # 提取doc数据
            try:
                kdc_result = result.get("result", {})
                if isinstance(kdc_result, dict) and "data" in kdc_result:
                    data_array = kdc_result["data"]
                    if isinstance(data_array, list) and len(data_array) > 0:
                        doc_data = data_array[0].get("doc", {})
                        if doc_data:
                            # 计算特征
                            features = calculate_kdc_features(doc_data)

                            # 计算准确率指标
                            accuracy_metrics = calculate_accuracy_metrics(data)
                            features["accuracy_metrics"] = accuracy_metrics

                            result["features"] = features
                            modified = True
                            print(f"  结果 {i+1}: 添加特征信息，bbox数量={features.get('bbox_count', 0)}，准确率指标={accuracy_metrics.get('has_comparison_data', False)}")
                        else:
                            print(f"  结果 {i+1}: 没有doc数据")
                    else:
                        print(f"  结果 {i+1}: data数组为空或格式不正确")
                else:
                    print(f"  结果 {i+1}: 没有result.data字段")
            except Exception as e:
                print(f"  结果 {i+1}: 处理时出错: {e}")
        
        # 如果有修改，保存文件
        if modified:
            # 备份原文件
            backup_path = file_path.with_suffix('.json.backup')
            if not backup_path.exists():
                import shutil
                shutil.copy2(file_path, backup_path)
                print(f"  已备份原文件到: {backup_path}")
            
            # 保存修改后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"  ✅ 文件已更新")
            return True
        else:
            print(f"  无需修改")
            return False
            
    except Exception as e:
        print(f"  ❌ 处理失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 如果提供了参数，处理指定的数据集
        dataset_name = sys.argv[1]
        parse_results_dir = Path("parse_results") / dataset_name
    else:
        # 否则处理所有数据集
        parse_results_dir = Path("parse_results")
    
    if not parse_results_dir.exists():
        print(f"❌ 目录不存在: {parse_results_dir}")
        return
    
    print(f"🔍 扫描目录: {parse_results_dir}")
    
    # 查找所有JSON文件
    json_files = []
    
    if len(sys.argv) > 1:
        # 处理指定数据集
        for json_file in parse_results_dir.glob("*.json"):
            if json_file.name != "latest_results.json":  # 跳过latest_results.json
                json_files.append(json_file)
        # 也处理latest_results.json
        latest_file = parse_results_dir / "latest_results.json"
        if latest_file.exists():
            json_files.append(latest_file)
    else:
        # 处理所有数据集
        for dataset_dir in parse_results_dir.iterdir():
            if dataset_dir.is_dir():
                for json_file in dataset_dir.glob("*.json"):
                    if json_file.name != "latest_results.json":  # 跳过latest_results.json
                        json_files.append(json_file)
                # 也处理latest_results.json
                latest_file = dataset_dir / "latest_results.json"
                if latest_file.exists():
                    json_files.append(latest_file)
    
    if not json_files:
        print("❌ 没有找到JSON文件")
        return
    
    print(f"📁 找到 {len(json_files)} 个JSON文件")
    
    # 处理每个文件
    success_count = 0
    for json_file in json_files:
        if fix_json_file(json_file):
            success_count += 1
    
    print(f"\n✅ 处理完成: {success_count}/{len(json_files)} 个文件已更新")

if __name__ == "__main__":
    main()
