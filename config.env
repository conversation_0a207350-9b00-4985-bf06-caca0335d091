# KS3 配置
export KS3_AK=AKLTV3HRQgmmSnm94aEKSo5A
export KS3_SK=OJx3DQrRgDULwK7VbnCg83hlPk47Jksxc9OgdE9V
export KS3_HOST=ks3-cn-beijing.ksyun.com
export KS3_BUCKET=test-platform-qa
export KS3_PROXY_HOST=127.0.0.1
export KS3_PROXY_PORT=18899

# 项目路径配置
export PROJECT_ROOT_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance
export PYTHON_VENV_PATH=/data/deploy/pyvenvs/pyldu/bin/activate
export SSL_CERT_PATH=/etc/ssl/certs/ca-certificates.crt

# 生成数据目录配置
export GEN_DATA_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance/dataset/test25/gen_data

# WPS配置
export WPS_AK=AK20230407UWDQSR
export WPS_SK=a9560a5967c5aaf44c9d8885abbd000c

# 代理配置
export http_proxy=http://localhost:18899
export https_proxy=http://localhost:18899

# LLM配置
export LLM_SERVICE_TYPE=custom
export CUSTOM_LLM_ENDPOINT=http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions
export CUSTOM_LLM_USER_AGENT="Apifox/1.0.0 (https://apifox.com)"
export CUSTOM_LLM_HOST=kmd-api.kas.wps.cn
export LLM_USE_PROXY=true
export LLM_MAX_TOKENS=12000
export LLM_TEMPERATURE=0.8
export LLM_TIMEOUT=60
export LLM_RETRY_COUNT=3
export LLM_RETRY_DELAY=1

# 默认测试集（命令行参数有最高优先级）
export DATASET_NAME=kingsoft

# VL LLM单元测试结果路径
export VL_LLM_RESULTS_BASE_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance/vl_llm_results

# MonkeyOCR超时配置（秒）
export MONKEY_OCR_TIMEOUT=300

# 表格生成配置
export TABLE_DEFAULT_ROWS=5
export TABLE_DEFAULT_COLS=4

# DST客户端超时配置（秒）
export DST_REQUEST_TIMEOUT=180
export DST_CONNECT_TIMEOUT=60

# 日志配置
export LOG_LEVEL=DEBUG
export LOG_FILE=parse.log
export LOG_USE_COLOR=true

# 多路解析器配置
# PARSER_ENABLED_LIST: 启用的解析器列表（逗号分隔）
# 可选解析器:
#   - kdc_markdown: KDC默认解析器（markdown格式）
#   - kdc_plain: KDC纯文本解析器
#   - kdc_kdc: KDC原生格式解析器
#   - monkey_ocr: MonkeyOCR远程解析器（HTML格式，fn_index=4）
#   - monkey_ocr_latex: MonkeyOCR远程解析器（LaTeX格式，fn_index=3）
#   - monkey_ocr_local: 本地MonkeyOCR解析器
#   - vl_llm: VL LLM解析器
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm"


# PARSER_EXECUTION_MODE: 解析器执行模式
# parallel: 并行执行（默认，速度快）
# sequential: 串行执行（资源占用少，便于调试）
export PARSER_EXECUTION_MODE=parallel

# PARSER_SEQUENTIAL_ORDER: 串行执行时的顺序（逗号分隔）
# 建议: 先执行本地解析器，再执行远程解析器
# 本配置只在 PARSER_EXECUTION_MODE=sequential 时生效
export PARSER_SEQUENTIAL_ORDER="monkey_ocr_local,kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm"

# 解析器重试配置
# PARSER_RETRY_COUNT: 解析失败时的最大重试次数（默认3次）
export PARSER_RETRY_COUNT=3

# PARSER_RETRY_DELAY: 重试间隔时间（秒，默认2秒）
export PARSER_RETRY_DELAY=2

# 表格生成配置
export TABLE_DEFAULT_ROWS=5
export TABLE_DEFAULT_COLS=4

# DST客户端超时配置（秒）
export DST_REQUEST_TIMEOUT=180
export DST_CONNECT_TIMEOUT=60

# 日志配置
export LOG_LEVEL=DEBUG
export LOG_FILE=parse.log
export LOG_USE_COLOR=true