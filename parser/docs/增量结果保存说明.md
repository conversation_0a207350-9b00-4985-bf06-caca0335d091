# 增量结果保存功能说明

## 功能概述

为了提升用户体验，避免长时间等待才能看到解析结果，系统新增了**增量结果保存**功能。现在每个case解析完成后都会立即更新结果文件，用户可以实时查看当前解析进度和已完成的case结果。

## 主要改进

### 1. 实时结果保存
- **原来**：等待所有文件解析完成后才保存结果
- **现在**：每个文件解析完成后立即保存到结果文件
- **优势**：可以实时查看解析进度，即使解析中断也不会丢失已完成的结果

### 2. 会话管理机制
- 新增会话管理功能，每次解析任务创建独立的会话
- 会话文件实时更新，包含进度信息和已完成的结果
- 支持进度查询和中途生成报告

### 3. 增强的进度展示
解析过程中会显示详细的进度信息：
```
📄 解析文件 (1/5): table_1.pdf
✅ 文件 table_1.pdf 解析完成: 6/7 个解析器成功
📄 解析文件 (2/5): table_2.pdf
✅ 文件 table_2.pdf 解析完成: 5/7 个解析器成功
...
```

## 新增功能

### 1. 进度报告生成
可以在解析进行中生成当前进度的报告：

```bash
# 生成当前解析进度报告
python main.py --mode progress
```

输出示例：
```
📈 生成进度报告...
✨ 加载进行中的解析会话结果
📊 解析进度: 3/5 文件已完成
🔄 当前处理: table_4.pdf (monkey_ocr)
📈 各解析器进度:
  kdc_markdown: 3/3 成功
  kdc_plain: 2/3 成功
  monkey_ocr: 1/3 成功
  ...
✅ 进度报告生成完成: reports/test/report_progress_2025_01_26_15_30_45.html
```

### 2. 线程安全的文件操作
- 使用文件锁机制确保多线程环境下的数据完整性
- 支持并发解析和结果保存

### 3. 会话状态跟踪
会话文件包含以下信息：
- 解析进度（已完成/总数）
- 当前处理的文件和解析器
- 各解析器的完成情况
- 开始时间和预估完成时间

## 结果文件结构

### 增量保存的会话文件结构
```json
{
  "timestamp": "2025_01_26_15_30_00",
  "dataset_name": "test_dataset",
  "metadata": {
    "parse_date": "2025-01-26T15:30:00",
    "version": "2.0",
    "total_files": 5,
    "completed_files": 3,
    "status": "in_progress"
  },
  "progress": {
    "current_file": "table_4.pdf",
    "current_parser": "monkey_ocr",
    "completed_count": 3,
    "total_count": 5,
    "start_time": "2025-01-26T15:30:00"
  },
  "processed_files": [...],
  "uploaded_files": [...],
  "parse_results": {
    "kdc_markdown": [...],
    "kdc_plain": [...],
    "monkey_ocr": [...],
    ...
  }
}
```

## 使用方法

### 1. 正常解析（自动增量保存）
```bash
# 运行完整流程，解析过程中自动保存进度
python main.py --mode full
```

### 2. 查看解析进度
```bash
# 在另一个终端查看当前解析进度
python main.py --mode progress
```

### 3. 生成最终报告
```bash
# 解析完成后生成完整报告
python main.py --mode report
```

## 技术实现

### 1. ResultManager 增强
- 新增 `init_session_results()` 初始化会话
- 新增 `add_file_results()` 增量添加结果
- 新增 `update_progress()` 更新进度信息
- 新增 `finalize_session()` 完成会话

### 2. Pipeline 修改
- 在解析开始时初始化会话
- 每个文件解析完成后立即保存结果
- 在解析结束时完成会话并生成最终文件

### 3. ReportGenerator 增强
- 支持读取进行中的会话文件
- 新增 `generate_progress_report()` 方法
- 在报告文件名中区分进度报告和完整报告

## 兼容性

- **向后兼容**：保留原有的 `save_parse_results()` 方法
- **文件格式兼容**：新格式可以被旧版本的报告生成器读取
- **命令行兼容**：原有的命令行参数和使用方式不变

## 故障恢复

如果解析过程中出现异常中断：
1. 已完成的结果不会丢失，保存在会话文件中
2. 可以通过 `--mode progress` 查看已完成的部分
3. 可以生成部分结果的报告进行查看
4. 重新运行解析任务会创建新的会话

## 性能影响

- **磁盘I/O**：每个文件完成后都会写入一次，但文件大小通常不大
- **内存使用**：会话数据保存在内存中，但会及时写入磁盘
- **执行时间**：增加的I/O时间相比解析时间很小，几乎不影响总体性能

## 配置选项

可以通过环境变量控制相关行为：
```bash
# 结果保存目录
export PARSE_RESULTS_BASE_DIR="/path/to/results"

# 报告保存目录  
export REPORTS_BASE_DIR="/path/to/reports"

# 测试集名称（用于隔离不同测试）
export DATASET_NAME="my_test_set"
``` 