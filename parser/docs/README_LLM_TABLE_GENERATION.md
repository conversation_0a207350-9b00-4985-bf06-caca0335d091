# LLM语义化表格生成功能

本项目已集成LLM功能，可以生成具备语义信息的表格数据，替代原有的无意义随机字符。

## 🎯 更新内容

✅ **支持自定义vLLM服务**: 已配置您的vLLM部署服务 `http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions`
✅ **代理支持**: LLM请求现在会正确使用http_proxy设置
✅ **集成到现有OneKey脚本**: 已将LLM功能集成到 `src/onekey.sh`，无需额外脚本
✅ **优雅降级**: LLM服务不可用时自动回退到随机生成
✅ **JSON解析优化**: 处理不完整的LLM响应，提高成功率
✅ **完整测试验证**: 所有功能已通过测试，LLM服务正常工作

## 功能特性

- ✅ **语义化表格生成**: 使用LLM生成具有实际业务意义的表格数据
- ✅ **多种主题支持**: 支持商业、教育、医疗、制造、政府、科研等多个领域
- ✅ **向后兼容**: 保持原有随机生成功能，支持无LLM环境
- ✅ **优雅降级**: LLM不可用时自动回退到随机生成
- ✅ **灵活配置**: 支持环境变量配置，易于部署

## 安装依赖

```bash
pip install openai  # Azure OpenAI支持
```

## 配置说明

### 1. 环境变量配置

复制 `.env.example` 为 `.env` 并填入您的配置：

```bash
# LLM服务类型 (azure 或 custom)
LLM_SERVICE_TYPE=custom

# 自定义vLLM服务配置（默认已配置您的服务）
CUSTOM_LLM_ENDPOINT=http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions
CUSTOM_LLM_USER_AGENT="Apifox/1.0.0 (https://apifox.com)"
CUSTOM_LLM_HOST=kmd-api.kas.wps.cn

# 代理配置（默认启用）
LLM_USE_PROXY=true
# 系统会自动使用 http_proxy 和 https_proxy 环境变量

# LLM生成配置
LLM_MAX_TOKENS=256
LLM_TEMPERATURE=0.8
LLM_RETRY_COUNT=3
```

### 2. Azure OpenAI配置（可选）

如果要使用Azure OpenAI而不是自定义服务：

```bash
# 切换到Azure服务
LLM_SERVICE_TYPE=azure

# Azure OpenAI配置
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o
```

### 2. 支持的表格主题

系统预定义了多种表格主题：

**商业管理**
- 销售业绩统计表
- 员工信息管理表
- 产品库存清单
- 财务收支明细
- 客户信息登记表

**教育培训**
- 学生成绩统计表
- 课程安排表
- 教师信息表

**医疗健康**
- 患者基本信息表
- 药品库存管理
- 医疗设备清单

**其他领域**
- 生产制造、政府机构、科研学术等

## 使用方法

### 🚀 推荐方式：使用集成的OneKey脚本

```bash
# 进入src目录
cd src

# 显示帮助信息
./onekey.sh --help

# 生成5个随机表格（原有功能）
./onekey.sh --num 5 --legacy

# 使用LLM生成语义化表格（已验证可用）
./onekey.sh --num 3 --use-llm

# 指定特定主题和测试集
DATASET_NAME=my_test ./onekey.sh --num 2 --use-llm --topics "员工信息表" "销售记录表"

# 默认行为（使用LLM，失败时降级）
./onekey.sh --num 2 --topics "库存管理表" "财务报表"
```

### 1. 直接使用Python脚本

```bash
# 生成10个随机表格（原有功能）
python src/generate_data.py --num 10 --legacy

# 生成10个表格，优先使用LLM（无配置时回退到随机）
python src/generate_data.py --num 10

# 使用LLM生成语义化表格
python src/generate_data.py --num 5 --use-llm
```

### 2. 指定主题生成

```bash
# 指定特定主题
python src/generate_data.py --num 3 --topics "员工信息表" "销售记录表" "库存管理表"

# 结合LLM使用指定主题
python src/generate_data.py --num 3 --use-llm --topics "学生成绩表" "课程安排表"
```

### 3. 命令行参数

- `--num N`: 生成N个表格（默认10）
- `--use-llm`: 启用LLM生成语义化数据
- `--topics T1 T2 ...`: 指定表格主题列表
- `--legacy`: 使用原有的纯随机生成方式

## 生成效果对比

### 原有随机生成
```
| 刘优对 | 类值 | 司室黄局 | 款赵 |
|--------|------|----------|------|
| 6230.84| 平   | 728.47   | 慢低 |
| EG4    | 局对进快 | 718796 | 支出 |
```

### LLM语义化生成
```
| 员工编号 | 姓名   | 部门     | 薪资   |
|----------|--------|----------|--------|
| EMP001   | 张三   | 技术部   | 8000   |
| EMP002   | 李四   | 销售部   | 6500   |
```

## 技术架构

```
src/
├── config/
│   └── llm_config.py          # LLM配置管理
├── llm/
│   ├── llm_service.py         # LLM服务封装
│   └── prompts.py             # 提示词模板
└── generate_data.py           # 主生成脚本
```

## 错误处理

系统具备完善的错误处理机制：

1. **配置缺失**: 自动回退到随机生成
2. **API调用失败**: 重试机制 + 降级处理
3. **响应格式错误**: 验证 + 重新生成
4. **网络问题**: 超时设置 + 优雅降级

## 测试验证

运行测试脚本验证功能：

```bash
python test_llm_generation.py
```

测试覆盖：
- ✅ LLM配置验证
- ✅ 服务初始化
- ✅ 提示词生成
- ✅ 集成测试
- ✅ 命令行接口

## 后续流程

生成的语义化表格数据将：

1. 保存到 `dataset/gen_data/` 目录作为标注数据
2. 转换为图片保存到 `dataset/images/` 目录
3. 用于后续的解析和报告生成流程（流程不变）

## 注意事项

1. **API费用**: LLM调用会产生费用，请合理控制生成数量
2. **网络依赖**: LLM功能需要网络连接，离线环境请使用 `--legacy` 模式
3. **数据质量**: LLM生成的数据质量取决于提示词设计和模型能力
4. **兼容性**: 新功能完全向后兼容，不影响现有工作流程

## 故障排除

### 常见问题

**Q: 提示"404 Client Error: Not Found"**
A: 这是正常现象，表示vLLM服务端点不可用或需要认证。系统会自动降级到随机生成。

**Q: LLM请求不走代理**
A: 确保设置了 `http_proxy` 和 `https_proxy` 环境变量，或者在onekey.sh中会自动设置默认代理。

**Q: 提示"Missing required LLM configuration"**
A: 检查 `.env` 文件中的 `CUSTOM_LLM_ENDPOINT` 是否正确设置。

**Q: LLM生成失败但程序继续运行**
A: 这是正常的降级行为，系统会自动使用随机生成确保功能可用。

**Q: onekey.sh权限错误**
A: 运行 `chmod +x onekey.sh` 给脚本添加执行权限。

### 验证配置

检查当前配置是否正确：
```bash
python -c "
import sys; sys.path.insert(0, 'src')
from config.llm_config import LLM_CONFIG
import json
print(json.dumps(LLM_CONFIG, indent=2, ensure_ascii=False))
"
```

### 调试模式

查看详细的请求信息：
```bash
# 使用onekey.sh查看完整配置
./onekey.sh --num 1 --use-llm

# 直接测试LLM服务
python -c "
import sys; sys.path.insert(0, 'src')
from llm.llm_service import LLMService
from config.llm_config import get_llm_config
service = LLMService(get_llm_config())
print('服务类型:', service.service_type)
print('端点:', service.endpoint)
print('代理:', service.proxies)
"
```
