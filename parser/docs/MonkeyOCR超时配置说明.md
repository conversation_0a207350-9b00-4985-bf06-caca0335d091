# MonkeyOCR超时配置说明

## 概述

为了避免MonkeyOCR处理时间过长导致流程阻塞，系统新增了可配置的超时功能。当MonkeyOCR处理时间超过设定值时，系统会自动终止处理并在报告中标记超时信息。

## 配置方法

### 1. 环境变量配置

在 `config.env` 文件中配置超时时间（单位：秒）：

```bash
# MonkeyOCR超时配置（秒）
export MONKEY_OCR_TIMEOUT=300
```

默认超时时间为300秒（5分钟）。

### 2. 支持的超时设置

- **最小值**: 30秒（建议不低于此值）
- **默认值**: 300秒（5分钟）
- **推荐值**: 
  - 测试环境: 180秒（3分钟）
  - 生产环境: 600秒（10分钟）

## 超时处理机制

### 1. 超时检测

系统在以下两个层面检测超时：

1. **请求级超时**: HTTP请求本身的超时
2. **流式处理超时**: 处理过程中的实时超时检查

### 2. 超时后的行为

当发生超时时，系统会：

1. 立即终止当前处理
2. 记录超时信息（包含实际处理时间）
3. 在HTML报告中显示超时状态
4. 继续处理其他文件（不影响整体流程）

## 报告显示

### 1. 超时标识

在HTML报告中，超时的MonkeyOCR结果会以红色显示：

- **准确率列**: `⏰ 超时 (实际时间秒)`
- **原始文本列**: 红色文本显示超时信息
- **渲染结果列**: 红色背景显示超时信息

### 2. 超时信息格式

```
MonkeyOCR处理超时 (300.0秒)
```

## 使用示例

### 1. 修改超时设置

```bash
# 编辑配置文件
vim config.env

# 修改超时设置为10分钟
export MONKEY_OCR_TIMEOUT=600

# 重新运行流程
./onekey.sh
```

### 2. 查看当前配置

运行脚本时会显示当前超时设置：

```bash
./onekey.sh
# 输出:
# === 配置信息 ===
# MonkeyOCR超时: 300 秒
```

### 3. 测试超时配置

```bash
# 测试配置是否正确加载
python test_timeout_config.py
```

## 故障排除

### 1. 超时设置不生效

检查配置文件是否正确加载：

```bash
# 检查环境变量
echo $MONKEY_OCR_TIMEOUT

# 重新加载配置
source config.env
```

### 2. 频繁超时

如果经常发生超时，考虑：

1. 增加超时时间设置
2. 检查网络连接状况
3. 检查MonkeyOCR服务状态
4. 优化输入图片大小

### 3. 超时信息不显示

确保使用的是最新版本的HTML报告生成器，超时信息需要最新的代码支持。

## 注意事项

1. **超时时间不宜过短**: 过短可能导致正常处理被误判为超时
2. **超时时间不宜过长**: 过长可能导致流程长时间阻塞
3. **网络环境影响**: 网络不稳定时建议适当增加超时时间
4. **批量处理**: 超时不会影响其他文件的处理，流程会继续执行 