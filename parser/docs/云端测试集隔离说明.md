# 云端测试集隔离功能说明

## 📋 概述

现在除了本地的 `dataset`、`reports`、`parse_results` 目录按测试集名称隔离外，金山云文档的目录也实现了自动隔离管理。

## 🌐 云端目录结构

```
金山云文档根目录 (414531444880)
├── default_test/           # 默认测试集文件夹
│   ├── table_1.pdf
│   ├── table_2.pdf
│   └── ...
├── experiment_1/           # 实验1测试集文件夹
│   ├── table_1.pdf
│   ├── table_2.pdf
│   └── ...
├── comparison_test/        # 对比测试集文件夹
│   ├── table_1.pdf
│   ├── table_2.pdf
│   └── ...
└── my_custom_test/         # 自定义测试集文件夹
    ├── table_1.pdf
    ├── table_2.pdf
    └── ...
```

## 🔧 实现原理

### 1. 新增的云端文件夹管理功能

在 `kdocs_client.py` 中新增了以下方法：

#### `create_folder()`
- 在指定父目录下创建文件夹
- 自动处理文件夹名称冲突（如果已存在则返回现有文件夹信息）
- 使用标准的金山云文档 API

#### `find_folder_by_name()`
- 根据名称查找文件夹
- 在指定父目录下搜索匹配的文件夹

#### `get_or_create_test_folder()`
- 获取或创建测试集文件夹的核心方法
- 先查找现有文件夹，如果不存在则创建新文件夹
- 返回文件夹信息（包含重要的文件夹ID）

### 2. 解析流程的自动集成

在 `parse_pipeline.py` 中的变化：

```python
# 获取当前测试集名称
dataset_name = os.getenv("DATASET_NAME", "default_test")

# 创建或获取测试集对应的云端文件夹
test_folder_result = kdocs.get_or_create_test_folder(
    group_id=KDOCS_CONFIG["group_id"],
    parent_id=KDOCS_CONFIG["root_parent_id"],  # 根目录ID
    test_name=dataset_name,
    company_id=KDOCS_CONFIG["company_id"]
)

test_folder = test_folder_result["folder"]
test_parent_id = str(test_folder["id"])  # 获取测试集文件夹ID

# 后续所有操作都在测试集文件夹内进行
```

## 🚀 使用效果

### 自动云端隔离
当您运行不同测试集时，云端文件夹会自动创建和管理：

```bash
# 运行默认测试集
./onekey.sh
# → 云端创建/使用 "default_test" 文件夹

# 运行自定义测试集
DATASET_NAME=my_experiment ./onekey.sh
# → 云端创建/使用 "my_experiment" 文件夹

# 运行对比测试
DATASET_NAME=comparison_v2 ./onekey.sh
# → 云端创建/使用 "comparison_v2" 文件夹
```

### 完全的数据隔离
- **本地隔离**: `dataset/测试集名称/`、`parse_results/测试集名称/`、`reports/测试集名称/`
- **云端隔离**: 金山云文档根目录下的 `测试集名称/` 文件夹
- **解析隔离**: 每个测试集的文件只会上传到对应的云端文件夹

## ✅ 优势

### 1. **完全自动化**
- 无需手动创建云端文件夹
- 程序自动检测和创建所需的目录结构
- 处理文件夹名称冲突（复用现有文件夹）

### 2. **数据安全性**
- 不同测试集的数据完全隔离
- 避免文件覆盖和混淆
- 清理操作只影响对应测试集

### 3. **便于管理**
- 云端文件夹结构清晰
- 易于在金山云文档中查看和管理
- 支持并行运行多个测试集

### 4. **向后兼容**
- 现有配置无需修改
- 默认使用 "default_test" 测试集
- 保持原有的 API 调用方式

## 🔍 运行日志示例

```
🔄 当前测试集: my_experiment
创建或获取云端测试集文件夹...
ℹ️  获取或创建测试集文件夹: my_experiment
ℹ️  创建新测试集文件夹: my_experiment
ℹ️  文件夹 'my_experiment' 创建成功 (ID: 414531306999)
✅ 测试集云端文件夹: my_experiment (ID: 414531306999)
获取测试集文件夹内的现有文件列表...
找到 0 个现有文件
```

## 🔧 配置说明

### 主要配置项
在 `parse_pipeline.py` 中：

```python
KDOCS_CONFIG = {
    "cookie": "...",  # 金山云文档认证 cookie
    "group_id": "2255465142",  # 文件组ID
    "company_id": "41000207",  # 企业ID
    "root_parent_id": "414531444880"  # 根目录文件夹ID（原 parent_id）
}
```

### 关键变化
- `parent_id` → `root_parent_id`: 明确这是根目录ID
- 动态获取 `test_parent_id`: 测试集文件夹ID
- 所有文件操作使用 `test_parent_id` 而不是 `root_parent_id`

## ⚠️ 注意事项

1. **权限要求**: 确保 cookie 具有创建文件夹的权限
2. **网络连接**: 需要稳定的网络连接来创建云端文件夹
3. **API 限制**: 遵循金山云文档的 API 调用频率限制
4. **测试集命名**: 建议使用有意义的测试集名称以便管理

## 🚀 未来扩展

可以考虑的增强功能：
- 云端文件夹的批量删除功能
- 测试集之间的云端文件复制
- 云端存储使用量统计
- 文件夹权限管理 