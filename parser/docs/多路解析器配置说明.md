# 多路解析器配置说明

## 概述

系统已重构为统一的多路解析架构，支持环境变量配置解析器的选择、执行模式和执行顺序。所有解析器（包括本地MonkeyOCR）现在都通过统一的接口进行管理。

## 🔧 配置选项

### 1. 启用的解析器列表 (PARSER_ENABLED_LIST)

```bash
# 指定启用的解析器，逗号分隔
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,monkey_ocr_local,vl_llm"
```

#### 可选解析器

| 解析器名称 | 描述 | 输出格式 | 依赖 |
|-----------|------|----------|-----|
| `kdc_markdown` | KDC默认解析器 | Markdown | WPS AK/SK |
| `kdc_plain` | KDC纯文本解析器 | Plain Text | WPS AK/SK |
| `kdc_kdc` | KDC原生格式解析器 | KDC Document | WPS AK/SK |
| `monkey_ocr` | MonkeyOCR远程解析器 | HTML (fn_index=4) | 网络连接 |
| `monkey_ocr_latex` | MonkeyOCR远程解析器 | LaTeX (fn_index=3) | 网络连接 |
| `monkey_ocr_local` | 本地MonkeyOCR解析器 | HTML/Markdown/LaTeX | 本地MonkeyOCR环境 |
| `vl_llm` | VL LLM解析器 | Markdown | LLM服务 |

### 2. 执行模式 (PARSER_EXECUTION_MODE)

```bash
# 并行执行（默认，推荐）
export PARSER_EXECUTION_MODE=parallel

# 串行执行（调试或资源受限时使用）
export PARSER_EXECUTION_MODE=sequential
```

#### 执行模式对比

| 模式 | 优点 | 缺点 | 适用场景 |
|------|------|------|---------|
| `parallel` | 速度快，效率高 | 资源占用多 | 生产环境，性能优先 |
| `sequential` | 资源占用少，便于调试 | 速度慢 | 调试环境，资源受限 |

### 3. 串行执行顺序 (PARSER_SEQUENTIAL_ORDER)

```bash
# 建议顺序：本地解析器优先，远程解析器其次
export PARSER_SEQUENTIAL_ORDER="monkey_ocr_local,kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm"
```

**顺序建议**:
1. `monkey_ocr_local` - 本地处理，无网络依赖
2. `kdc_markdown` 系列 - 稳定的云端服务
3. `monkey_ocr` 系列 - 第三方服务，可能不稳定
4. `vl_llm` - LLM服务，处理时间较长

## 📋 使用示例

### 示例1：只使用KDC解析器

```bash
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc"
export PARSER_EXECUTION_MODE=parallel
./table_parser.sh
```

### 示例2：调试MonkeyOCR问题

```bash
export PARSER_ENABLED_LIST="monkey_ocr,monkey_ocr_latex"
export PARSER_EXECUTION_MODE=sequential
export PARSER_SEQUENTIAL_ORDER="monkey_ocr,monkey_ocr_latex"
./table_parser.sh --debug
```

### 示例3：快速测试（最少解析器）

```bash
export PARSER_ENABLED_LIST="kdc_markdown,monkey_ocr_local"
export PARSER_EXECUTION_MODE=parallel
./table_parser.sh
```

### 示例4：完整测试（所有解析器）

```bash
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,monkey_ocr_local,vl_llm"
export PARSER_EXECUTION_MODE=parallel
./table_parser.sh
```

## 🔍 解析器详细说明

### KDC系列解析器

- **kdc_markdown**: 默认markdown格式，适合大多数场景
- **kdc_plain**: 纯文本格式，结构化程度低但兼容性好
- **kdc_kdc**: 原生KDC文档格式，保留最完整的结构信息

### MonkeyOCR系列解析器

- **monkey_ocr**: 使用fn_index=4，生成HTML表格格式
- **monkey_ocr_latex**: 使用fn_index=3，生成LaTeX表格格式
- **monkey_ocr_local**: 本地部署的MonkeyOCR，支持多种输出格式

### VL LLM解析器

- **vl_llm**: 基于视觉语言模型的解析，生成markdown格式

## ⚙️ 配置验证

### 查看当前配置

```bash
# 加载配置（在项目根目录）
source config.env

# 或在parser目录
source ../config.env

# 查看解析器配置
echo "启用的解析器: $PARSER_ENABLED_LIST"
echo "执行模式: $PARSER_EXECUTION_MODE"
echo "执行顺序: $PARSER_SEQUENTIAL_ORDER"
```

### 测试配置

```bash
# 运行系统会自动显示配置信息
./table_parser.sh --debug

# 查看日志中的配置信息
tail -f parse.log | grep "解析器配置"
```

## 🚨 故障排除

### 常见问题

1. **某个解析器被跳过**
   ```
   解决: 检查解析器名称是否正确，依赖是否满足
   ```

2. **WPS AK/SK 未配置**
   ```
   警告: WPS AK/SK 未配置，跳过KDC解析器注册
   解决: 配置 WPS_AK 和 WPS_SK 环境变量
   ```

3. **本地MonkeyOCR不可用**
   ```
   解决: 检查本地MonkeyOCR安装和配置文件路径
   ```

4. **并行执行资源不足**
   ```
   解决: 改为串行执行模式或减少启用的解析器数量
   ```

### 调试建议

1. **启用调试日志**
   ```bash
   export LOG_LEVEL=DEBUG
   ./table_parser.sh --debug
   ```

2. **单独测试解析器**
   ```bash
   export PARSER_ENABLED_LIST="kdc_markdown"
   ./table_parser.sh
   ```

3. **使用串行模式调试**
   ```bash
   export PARSER_EXECUTION_MODE=sequential
   ./table_parser.sh
   ```

## 📊 性能优化

### 推荐配置

#### 高性能配置（服务器环境）
```bash
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,monkey_ocr_local,vl_llm"
export PARSER_EXECUTION_MODE=parallel
```

#### 资源受限配置（开发环境）
```bash
export PARSER_ENABLED_LIST="kdc_markdown,monkey_ocr_local"
export PARSER_EXECUTION_MODE=parallel
```

#### 调试配置（故障排查）
```bash
export PARSER_ENABLED_LIST="kdc_markdown"
export PARSER_EXECUTION_MODE=sequential
export LOG_LEVEL=DEBUG
```

## 🔄 架构变化

### 重构前
- 本地MonkeyOCR单独执行
- 多路解析硬编码
- 执行顺序固定

### 重构后
- 所有解析器统一管理
- 环境变量配置
- 灵活的执行策略
- 清晰的错误处理

### 兼容性
- ✅ 配置文件完全向后兼容
- ✅ 解析结果格式保持一致
- ✅ 报告生成不受影响
- ✅ 现有脚本无需修改 