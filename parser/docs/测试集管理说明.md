# 测试集管理功能说明

## 📋 概述

新的测试集管理系统允许您创建、管理和切换不同的测试数据集，使得能够并行进行多个实验并保持结果的组织性。

## 🏗️ 新的目录结构

```
enhance/
├── dataset/
│   ├── default_test/        # 默认测试集
│   │   ├── images/          # 原始图片
│   │   ├── converted_pdfs/  # 转换的PDF
│   │   ├── monkey_ocr/      # MonkeyOCR输出
│   │   └── gen_data/        # 生成的数据
│   ├── experiment_1/        # 实验1测试集
│   │   └── ...
│   └── comparison_test/     # 对比测试集
│       └── ...
├── parse_results/
│   ├── default_test/        # 默认测试集的解析结果
│   │   ├── parse_results_2025_01_17_10_30_45.json
│   │   ├── parse_results_2025_01_17_11_20_15.json
│   │   └── latest_results.json
│   ├── experiment_1/        # 实验1的解析结果
│   │   └── ...
│   └── comparison_test/     # 对比测试的解析结果
│       └── ...
└── reports/
    ├── default_test/        # 默认测试集的报告
    │   ├── report_2025_01_17_10_35_20.html
    │   └── ...
    ├── experiment_1/        # 实验1的报告
    │   └── ...
    └── comparison_test/     # 对比测试的报告
        └── ...
```

## 🚀 使用方法

### 1. 基本使用（onekey.sh）

#### 使用默认测试集
```bash
./onekey.sh
```

#### 指定测试集名称
```bash
DATASET_NAME=my_experiment ./onekey.sh
```

#### 查看帮助和可用测试集
```bash
./onekey.sh help
./onekey.sh list
```

### 2. 测试集管理工具（manage_datasets.sh）

#### 列出所有测试集
```bash
./manage_datasets.sh list
```
显示所有测试集及其统计信息（图片数量、解析结果数量、报告数量、最后运行时间）。

#### 创建新测试集
```bash
./manage_datasets.sh create my_experiment
```
创建名为 `my_experiment` 的新测试集，会自动创建所有必要的目录结构。

#### 运行指定测试集
```bash
./manage_datasets.sh run my_experiment
```
使用指定的测试集运行完整流程（数据生成→解析→报告）。

#### 复制测试集
```bash
./manage_datasets.sh copy source_test target_test
```
将 `source_test` 测试集的数据复制到 `target_test`（只复制原始数据，不复制解析结果）。

#### 清理测试集
```bash
./manage_datasets.sh clean my_experiment
```
清理指定测试集的解析结果和报告，保留原始数据。

#### 删除测试集
```bash
./manage_datasets.sh delete old_test
```
完全删除测试集及其所有数据（需要确认）。

### 3. 报告生成工具（generate_report.py）

#### 使用当前测试集生成报告
```bash
python generate_report.py
```

#### 切换测试集并生成报告
```bash
DATASET_NAME=my_experiment python generate_report.py
```

#### 使用指定的解析结果文件
```bash
DATASET_NAME=my_experiment python generate_report.py --results-file ../parse_results/my_experiment/parse_results_2025_01_17_10_30_45.json
```

## 🔧 环境变量说明

### 主要环境变量

- `DATASET_NAME`: 测试集名称（默认: `default_test`）
- `PROJECT_ROOT_DIR`: 项目根目录
- `DATASET_BASE_DIR`: 数据集基础目录
- `PARSE_RESULTS_BASE_DIR`: 解析结果基础目录
- `REPORTS_BASE_DIR`: 报告基础目录

### 动态生成的环境变量

基于 `DATASET_NAME` 动态生成：
- `DATASET_DIR`: 当前测试集目录
- `IMAGES_DIR`: 当前测试集图片目录
- `PDF_DIR`: 当前测试集PDF目录
- `MONKEY_OCR_DIR`: 当前测试集MonkeyOCR输出目录
- `GEN_DATA_DIR`: 当前测试集生成数据目录
- `PARSE_RESULTS_DIR`: 当前测试集解析结果目录
- `REPORTS_DIR`: 当前测试集报告目录

## 💡 实际应用场景

### 场景1：对比不同参数设置
```bash
# 创建两个测试集
./manage_datasets.sh create params_v1
./manage_datasets.sh create params_v2

# 分别运行测试
DATASET_NAME=params_v1 ./onekey.sh
DATASET_NAME=params_v2 ./onekey.sh

# 对比结果
ls reports/params_v1/
ls reports/params_v2/
```

### 场景2：创建备份和实验版本
```bash
# 备份当前稳定版本
./manage_datasets.sh copy default_test stable_backup

# 创建实验版本
./manage_datasets.sh create new_algorithm

# 在实验版本中测试新算法
DATASET_NAME=new_algorithm ./onekey.sh
```

### 场景3：重新生成报告
```bash
# 只重新生成默认测试集的报告
python generate_report.py

# 重新生成指定测试集的报告
DATASET_NAME=my_experiment python generate_report.py

# 批量重新生成多个测试集的报告
for dataset in experiment_1 experiment_2 comparison_test; do
    DATASET_NAME=$dataset python generate_report.py
done
```

### 场景4：清理和维护
```bash
# 查看所有测试集状态
./manage_datasets.sh list

# 清理不需要的解析结果
./manage_datasets.sh clean old_experiment

# 删除废弃的测试集
./manage_datasets.sh delete failed_test
```

## ✅ 优势

1. **并行实验**：可以同时进行多个实验而不相互干扰
2. **结果隔离**：每个测试集的结果独立存储
3. **版本管理**：可以保存和对比不同版本的实验结果
4. **快速切换**：通过环境变量轻松切换测试集
5. **备份恢复**：可以轻松备份和恢复测试集
6. **清理管理**：支持选择性清理以节省存储空间

## ⚠️ 注意事项

1. 测试集名称只能包含字母、数字、下划线和连字符
2. 复制测试集时只复制原始数据，不复制解析结果和报告
3. 删除操作不可逆，请谨慎使用
4. 环境变量 `DATASET_NAME` 影响所有相关脚本的行为
5. 每个测试集可以保存多次运行的结果，通过时间戳区分 