#!/usr/bin/env python3
"""
测试VL LLM解析器的重试功能

这个脚本用于测试VL LLM解析器在真实环境中的重试行为
"""

import os
import sys
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.parse_manager import ParseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_vl_llm_retry():
    """测试VL LLM解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试VL LLM解析器重试功能")
    logger.info("=" * 50)
    
    # 设置重试配置
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "1"
    
    # 创建解析管理器并注册默认解析器
    manager = ParseManager().create_default_parsers()
    
    # 测试文件信息 - 使用一个存在的图片文件
    # 但是可能会因为网络问题导致LLM API失败
    file_info = {
        "filename": "test_vl_llm_retry.pdf",
        "file_id": "test_vl_llm_retry_001",
        "original_image": "test_vl_llm_retry.png",
        "image_path": "/data/projects/kingsoft/personal/workspace/tablerag/enhance/dataset/kingsoft/images/博腾报告书_01.png"
    }
    
    # 检查文件是否存在
    if not os.path.exists(file_info["image_path"]):
        logger.error(f"测试图片文件不存在: {file_info['image_path']}")
        return
    
    logger.info("测试VL LLM解析器重试...")
    try:
        result = manager.parse_with_parser_retry("vl_llm", file_info)
        logger.info(f"VL LLM解析结果: success={result.get('success')}")
        
        if result.get('success'):
            logger.info("VL LLM解析成功")
            inner_result = result.get('result', {})
            if isinstance(inner_result, dict):
                logger.info(f"内层success: {inner_result.get('success')}")
                logger.info(f"内层error: {inner_result.get('error')}")
                content = inner_result.get('content')
                if content:
                    logger.info(f"解析内容长度: {len(str(content))} 字符")
        else:
            logger.info(f"VL LLM解析失败: {result.get('error', '未知')}")
            
    except Exception as e:
        logger.error(f"VL LLM解析异常: {e}")
    
    logger.info("VL LLM解析器重试测试完成")


def test_vl_llm_with_nonexistent_file():
    """测试VL LLM解析器处理不存在文件的重试"""
    logger.info("=" * 50)
    logger.info("测试VL LLM解析器处理不存在文件的重试")
    logger.info("=" * 50)
    
    # 设置重试配置
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "0.5"
    
    # 创建解析管理器并注册默认解析器
    manager = ParseManager().create_default_parsers()
    
    # 测试文件信息 - 使用不存在的文件
    file_info = {
        "filename": "nonexistent.pdf",
        "file_id": "nonexistent_001",
        "original_image": "nonexistent.png",
        "image_path": "/nonexistent/path/nonexistent.png"
    }
    
    logger.info("测试VL LLM解析器处理不存在文件...")
    try:
        result = manager.parse_with_parser_retry("vl_llm", file_info)
        logger.info(f"VL LLM解析结果: success={result.get('success')}")
        
        if not result.get('success'):
            error = result.get('error', '未知错误')
            logger.info(f"预期的失败: {error}")
            # 检查是否包含重试信息
            if "重试" in error:
                logger.info("✅ 重试逻辑正常工作")
            else:
                logger.warning("⚠️ 错误信息中没有重试信息")
        else:
            logger.warning("⚠️ 预期失败但实际成功了")
            
    except Exception as e:
        logger.info(f"预期的异常: {e}")
    
    logger.info("VL LLM不存在文件重试测试完成")


def main():
    """主测试函数"""
    logger.info("开始测试VL LLM解析器重试功能")
    
    # 显示当前重试配置
    retry_count = os.getenv("PARSER_RETRY_COUNT", "3")
    retry_delay = os.getenv("PARSER_RETRY_DELAY", "2")
    logger.info(f"当前重试配置: 最大重试次数={retry_count}, 重试间隔={retry_delay}秒")
    
    try:
        test_vl_llm_with_nonexistent_file()
        test_vl_llm_retry()
        
        logger.info("=" * 50)
        logger.info("🎉 VL LLM重试功能测试完成")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
