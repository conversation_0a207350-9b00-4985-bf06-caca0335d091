#!/usr/bin/env python3
"""
测试自定义vLLM服务集成
"""

import os
import sys
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_custom_llm_config():
    """测试自定义LLM配置"""
    print("=== 测试自定义LLM配置 ===")
    try:
        from config.llm_config import LLM_CONFIG, validate_llm_config
        
        print("✓ 配置加载成功")
        print(f"服务类型: {LLM_CONFIG.get('service_type')}")
        print(f"端点: {LLM_CONFIG.get('endpoint')}")
        print(f"使用代理: {LLM_CONFIG.get('use_proxy')}")
        print(f"请求头: {LLM_CONFIG.get('headers', {})}")
        
        # 验证配置
        validate_llm_config()
        print("✓ 配置验证通过")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_custom_llm_service():
    """测试自定义LLM服务"""
    print("\n=== 测试自定义LLM服务 ===")
    try:
        from llm.llm_service import LLMService
        from config.llm_config import get_llm_config
        
        config = get_llm_config()
        service = LLMService(config)
        
        print("✓ LLM服务初始化成功")
        print(f"服务类型: {service.service_type}")
        print(f"端点: {service.endpoint}")
        print(f"代理配置: {service.proxies}")
        print(f"请求头: {service.headers}")
        
        # 测试请求构建（不实际发送）
        try:
            # 这会失败，但我们可以看到请求是否正确构建
            result = service.generate("测试提示", "系统提示")
            print(f"✓ 意外成功: {result[:50]}...")
        except Exception as e:
            if "404" in str(e) or "Connection" in str(e):
                print("✓ 请求构建正确（端点不可用，符合预期）")
            else:
                print(f"✗ 意外错误: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ 服务测试失败: {e}")
        return False

def test_proxy_configuration():
    """测试代理配置"""
    print("\n=== 测试代理配置 ===")
    try:
        # 检查环境变量
        http_proxy = os.getenv('http_proxy') or os.getenv('HTTP_PROXY')
        https_proxy = os.getenv('https_proxy') or os.getenv('HTTPS_PROXY')
        
        print(f"HTTP代理: {http_proxy}")
        print(f"HTTPS代理: {https_proxy}")
        
        if http_proxy or https_proxy:
            print("✓ 代理环境变量已设置")
        else:
            print("⚠️  代理环境变量未设置")
        
        # 测试LLM服务是否正确使用代理
        from llm.llm_service import LLMService
        from config.llm_config import get_llm_config
        
        config = get_llm_config()
        service = LLMService(config)
        
        if service.proxies:
            print(f"✓ LLM服务代理配置: {service.proxies}")
        else:
            print("⚠️  LLM服务未配置代理")
        
        return True
    except Exception as e:
        print(f"✗ 代理测试失败: {e}")
        return False

def test_onekey_script():
    """测试onekey脚本"""
    print("\n=== 测试OneKey脚本 ===")
    try:
        import subprocess
        
        # 测试帮助信息
        result = subprocess.run(['./onekey.sh', '--help'], 
                              capture_output=True, text=True, 
                              cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print("✓ OneKey脚本可执行")
            if "LLM服务类型" in result.stdout:
                print("✓ 配置信息显示正确")
            if "--use-llm" in result.stdout:
                print("✓ 帮助信息完整")
        else:
            print(f"✗ OneKey脚本执行失败: {result.stderr}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ OneKey脚本测试失败: {e}")
        return False

def test_integration():
    """集成测试"""
    print("\n=== 集成测试 ===")
    try:
        from generate_data import init_llm_service, generate_semantic_table_data
        
        # 测试LLM服务初始化
        llm_service = init_llm_service()
        if llm_service:
            print("✓ LLM服务初始化成功")
            
            # 测试表格生成（会失败但应该优雅处理）
            result = generate_semantic_table_data(llm_service, "测试表格", 3, 3)
            if result is None:
                print("✓ LLM生成失败时正确返回None")
            else:
                print(f"✓ 意外成功生成表格: {len(result)}行")
        else:
            print("✗ LLM服务初始化失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试自定义vLLM服务集成...\n")
    
    tests = [
        test_custom_llm_config,
        test_custom_llm_service,
        test_proxy_configuration,
        test_onekey_script,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！自定义vLLM服务集成成功！")
        print("\n📋 下一步:")
        print("1. 确保vLLM服务端点可访问")
        print("2. 运行: ./onekey.sh --num 2 --use-llm")
        print("3. 如果LLM失败，系统会自动降级到随机生成")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置")
        return 1

if __name__ == '__main__':
    sys.exit(main())
