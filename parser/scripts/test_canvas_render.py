#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from processors.html_generator import render_kdc_doc_to_canvas

def test_canvas_render():
    """测试Canvas渲染功能"""
    
    # 读取parse_results.json文件
    parse_results_file = "../parse_results/kingsoft/parse_results_2025_06_20_15_03_08.json"
    
    try:
        with open(parse_results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"文件不存在: {parse_results_file}")
        return
    except Exception as e:
        print(f"读取文件错误: {e}")
        return
    
    # 检查kdc_kdc_results
    kdc_kdc_results = data.get('kdc_kdc_results', [])
    print(f"KDC KDC结果数量: {len(kdc_kdc_results)}")
    
    for i, result in enumerate(kdc_kdc_results):
        print(f"\n=== 测试文档 {i+1} ===")
        
        if not result or not isinstance(result, dict):
            print("无效的结果数据")
            continue
            
        # 提取doc数据
        result_data = result.get("data", [])
        if isinstance(result_data, list) and len(result_data) > 0:
            doc_data = result_data[0].get("doc", {})
        elif isinstance(result_data, dict):
            doc_data = result_data.get("doc", {})
        else:
            doc_data = {}
        
        if not doc_data:
            print("没有找到doc数据")
            continue
        
        print("生成Canvas HTML...")
        canvas_html = render_kdc_doc_to_canvas(doc_data)
        
        # 检查结果
        if "Error" in canvas_html:
            print(f"Canvas渲染错误: {canvas_html}")
        elif "No text blocks found" in canvas_html:
            print("没有找到文本块")
        elif "canvas" in canvas_html and "textBlocks" in canvas_html:
            print("✓ Canvas HTML生成成功")
            print(f"HTML长度: {len(canvas_html)} 字符")
            
            # 检查是否包含文本块数据
            if '"text":' in canvas_html:
                print("✓ 包含文本块数据")
                
                # 提取并验证JSON数据
                try:
                    # 查找textBlocks数据
                    start_pos = canvas_html.find('const textBlocks = ') + len('const textBlocks = ')
                    end_pos = canvas_html.find(';', start_pos)
                    if start_pos > 20 and end_pos > start_pos:
                        json_str = canvas_html[start_pos:end_pos].strip()
                        text_blocks = json.loads(json_str)
                        print(f"✓ 解析到 {len(text_blocks)} 个文本块")
                        
                        # 显示前几个文本块的信息
                        for j, block in enumerate(text_blocks[:3]):
                            print(f"  文本块 {j+1}: '{block.get('text', '')[:20]}...' 位置: ({block.get('x1')},{block.get('y1')})")
                    else:
                        print("✗ 无法提取textBlocks数据")
                except Exception as e:
                    print(f"✗ JSON解析错误: {e}")
            else:
                print("✗ 没有包含文本块数据")
        else:
            print("Canvas HTML生成异常")
        
        # 只测试第一个文档
        break

if __name__ == "__main__":
    test_canvas_render() 