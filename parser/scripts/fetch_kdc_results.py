import os
import requests
import json
from datetime import datetime
from clients.kdc_client import KdcClient  # 使用KDC客户端

# 配置信息
KDC_CONFIG = {
    "ak": "AK20240110ATSZNX",
    "sk": "ef0995f831bf5d94b2f32630e9ab043a",
    "cookie": "wps_sid=V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066"
}

KDOCS_CONFIG = {
    "company_id": "41000207"  # 字符串形式
}

# 初始化KDC客户端
kdc = KdcClient(
    ak=KDC_CONFIG["ak"],
    sk=KDC_CONFIG["sk"],
    default_cookie=KDC_CONFIG["cookie"]
)

# 代理配置
proxies = {
    'http': 'http://127.0.0.1:18899',
    'https': 'http://127.0.0.1:18899'
}

# API端点
url = 'https://365.kdocs.cn/3rd/drive/api/v5/files/share_folder_link/cr36vpClCSD0/folders/caPvFdiFIhlJ/list'
params = {
    'orderby': 'mtime',
    'order': 'desc',
    'offset': '0',
    'count': '30',
    'with_acl': 'true'
}

# 完整的请求头
headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://365.kdocs.cn/folder/cr36vpClCSD0/caPvFdiFIhlJ',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
}

# 完整的Cookie信息（未被省略）
cookies = {
    'weboffice_device_id': 'aa2aa9b695c44bbc775892c590ad33b1',
    'wps_endcloud': '1',
    'userInNewLayout': 'true',
    'cid': '41000207',
    'uid': '1562054758',
    'wps_sid': 'V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066',
    'xsr-diffversion': '3',
    'Hm_lvt_cb2fa0997df4ff8e739c666ee2487fd9': '1741673783,1743144033,1743392329',
    'weboffice_cdn': '21',
    'swi_acc_redirect_limit': '0',
    'visitorid': '1151692900',
    'csrf': 'Ae7XAstwSD33ahE6RSSXm6h6BF847N6n',
    'wpsua': 'V1BTVUEvMS4wICh3ZWIta2RvY3M6Q2hyb21lXzEzNi4wLjAuMDsgbWFjOk9TIFg7IEQ4UzNtYlMxUkRhamVKMFpNeW1YX2c9PTpUV0ZqYVc1MGIzTm9JQ0E9KSBNYWNpbnRvc2gv',
    'HWWAFSESTIME': '1749096088613',
    'HWWAFSESID': '82ef41218ab2762d6b0',
    'userid': '1562054758',
    'appcdn': 'volcengine-kdocs-cache.wpscdn.cn',
    'user_card_img_base_url': 'https://qn.cache.wpscdn.cn/koa/open-admin/static',
    'region': 'hwy',
    'env': 'prod_rc'
}

def fetch_files():
    """获取文件列表"""
    try:
        print("正在从金山文档获取文件列表...")
        response = requests.get(
            url,
            params=params,
            headers=headers,
            cookies=cookies,
            proxies=proxies,
            timeout=30
        )
        
        if response.status_code == 200:
            print("成功获取文件列表")
            data = response.json()
            return data.get('files', [])
        else:
            print(f"获取文件失败，HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return []
    except Exception as e:
        print(f"获取文件时发生异常: {str(e)}")
        return []

def process_files(files):
    """使用KDC处理文件"""
    results = []
    for idx, file in enumerate(files, 1):
        if file.get('ftype') == 'file':
            try:
                print(f"正在处理文件 {idx}/{len(files)}: {file['fname']}")
                res = kdc.parse_document(
                    company_id=KDOCS_CONFIG["company_id"],
                    file_id=str(file["id"]),
                    filename=file["fname"],
                    format="markdown",
                    include_elements="all",
                    pdf_engine="scan"
                )
                # 添加元数据
                res['metadata'] = {
                    'original_filename': file['fname'],
                    'file_size': file.get('fsize'),
                    'last_modified': file.get('mtime'),
                    'creator': file.get('creator', {}).get('name'),
                    'processed_at': datetime.now().isoformat()
                }
                results.append(res)
            except Exception as e:
                print(f"处理文件 {file['fname']} 时出错: {str(e)}")
                continue
    return results

def save_results(results):
    """保存结果到JSON文件"""
    if not results:
        print("没有可保存的结果")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"kdc_processed_results_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'status': 'success',
                'processed_at': datetime.now().isoformat(),
                'total_files': len(results),
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到 {filename}")
        return filename
    except Exception as e:
        print(f"保存结果时出错: {str(e)}")
        return None

if __name__ == "__main__":
    # 1. 获取文件列表
    files = fetch_files()
    
    if not files:
        print("没有获取到文件，程序终止")
        exit()
    
    # 2. 使用KDC处理文件
    processed_results = process_files(files)
    
    if not processed_results:
        print("没有文件被成功处理")
        exit()
    
    # 3. 保存结果
    save_results(processed_results) 