import os
import requests
import json
import time
from datetime import datetime
from typing import Dict, List
from clients.dst_client import DstClient  # 使用新的DST客户端
from clients.dst_models import DstParseRes, DstChunk, Dst, DstImage  # 导入数据模型

# 配置信息
DST_CONFIG = {
    "ak": "AK20240110ATSZNX",
    "sk": "ef0995f831bf5d94b2f32630e9ab043a",
    "cookie": "wps_sid=V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066"
}

KDOCS_CONFIG = {
    "company_id": "41000207"  # 字符串形式
}

# 初始化DST客户端，配置超时参数
dst = DstClient(
    ak=DST_CONFIG["ak"],
    sk=DST_CONFIG["sk"],
    default_cookie=DST_CONFIG["cookie"],
    request_timeout=int(os.getenv("DST_REQUEST_TIMEOUT", "180")),  # 默认3分钟请求超时
    connect_timeout=int(os.getenv("DST_CONNECT_TIMEOUT", "60"))    # 默认1分钟连接超时
)

# 代理配置
proxies = {
    'http': 'http://127.0.0.1:18899',
    'https': 'http://127.0.0.1:18899'
}

# API端点
url = 'https://365.kdocs.cn/3rd/drive/api/v5/files/share_folder_link/cr36vpClCSD0/folders/caPvFdiFIhlJ/list'
params = {
    'orderby': 'mtime',
    'order': 'desc',
    'offset': '0',
    'count': '30',
    'with_acl': 'true'
}

# 完整的请求头
headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://365.kdocs.cn/folder/cr36vpClCSD0/caPvFdiFIhlJ',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
}

# 完整的Cookie信息（未被省略）
cookies = {
    'weboffice_device_id': 'aa2aa9b695c44bbc775892c590ad33b1',
    'wps_endcloud': '1',
    'userInNewLayout': 'true',
    'cid': '41000207',
    'uid': '1562054758',
    'wps_sid': 'V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066',
    'xsr-diffversion': '3',
    'Hm_lvt_cb2fa0997df4ff8e739c666ee2487fd9': '1741673783,1743144033,1743392329',
    'weboffice_cdn': '21',
    'swi_acc_redirect_limit': '0',
    'visitorid': '1151692900',
    'csrf': 'Ae7XAstwSD33ahE6RSSXm6h6BF847N6n',
    'wpsua': 'V1BTVUEvMS4wICh3ZWIta2RvY3M6Q2hyb21lXzEzNi4wLjAuMDsgbWFjOk9TIFg7IEQ4UzNtYlMxUkRhamVKMFpNeW1YX2c9PTpUV0ZqYVc1MGIzTm9JQ0E9KSBNYWNpbnRvc2gv',
    'HWWAFSESTIME': '1749096088613',
    'HWWAFSESID': '82ef41218ab2762d6b0',
    'userid': '1562054758',
    'appcdn': 'volcengine-kdocs-cache.wpscdn.cn',
    'user_card_img_base_url': 'https://qn.cache.wpscdn.cn/koa/open-admin/static',
    'region': 'hwy',
    'env': 'prod_rc'
}

def load_parse_data_from_json_object(data) -> DstParseRes:
    """将字典转换为 DstParseRes 对象"""
    chunks = []
    chunks_data = data.get("chunks", [])
    if chunks_data is None:
        chunks_data = []
    
    for chunk in chunks_data:
        dsts = []
        dsts_data = chunk.get("dsts", [])
        if dsts_data is None:
            dsts_data = []
            
        for dst in dsts_data:
            dsts.append(Dst(
                id=dst.get("id", ""),
                dst_type=dst.get("dst_type", ""),
                content=dst.get("content"),
                attributes=dst.get("attributes"),
                order=dst.get("order"),
                parent=dst.get("parent")
            ))

        chunks.append(DstChunk(
            content=chunk.get("content", ""),
            chunk_id=chunk.get("chunk_id", ""),
            pre_chunk=chunk.get("pre_chunk"),
            next_chunk=chunk.get("next_chunk"),
            label=chunk.get("label"),
            content_embedding=chunk.get("content_embedding"),
            page_num=chunk.get("page_num"),
            block=chunk.get("block"),
            dsts=dsts
        ))

    images = []
    images_data = data.get("image", [])
    if images_data is None:
        images_data = []
        
    for image in images_data:
        images.append(DstImage(
            url=image.get("url", ""),
            chunk_ids=image.get("chunk_ids"),
            image_type=image.get("image_type")
        ))

    parse_res = DstParseRes(
        chunks=chunks,
        page_size=data.get("page_size", 0),
        word_count=data.get("word_count", 0),
        width=data.get("width", 0),
        height=data.get("height", 0),
        image=images if images else None,
        is_scan=data.get("is_scan", False)
    )

    return parse_res

def preprocess_images_and_chunks(
    parse_data: DstParseRes,
    const_field: list
) -> dict:
    """预处理图片和chunks"""
    # 文本中包含的图片
    chunk_include_images_map = {}  # type: Dict[str, List[DstImage]]
    doc_src_images = []  # 原生图片文档

    # 安全地处理图片数据
    if parse_data.Image and isinstance(parse_data.Image, list):
        for image in parse_data.Image:
            if image.ChunkIds and isinstance(image.ChunkIds, list):
                for chunk_id in image.ChunkIds:
                    if chunk_id not in chunk_include_images_map:
                        chunk_include_images_map[chunk_id] = []
                    chunk_include_images_map[chunk_id].append(image)
            else:
                doc_src_images.append(image)

    # 映射图片到 chunk 并拼接 content
    for i, chunk in enumerate(parse_data.Chunks):
        if chunk.ChunkId in chunk_include_images_map:
            chunk.Image = chunk_include_images_map[chunk.ChunkId]
        else:
            chunk.Image = []

        chunk_content = ""
        if chunk.dsts and isinstance(chunk.dsts, list):
            for dst in chunk.dsts:
                if dst.dst_type != "image":
                    if dst.content and isinstance(dst.content, list):
                        for c in dst.content:
                            if c:  # 确保内容不为空
                                chunk_content += str(c) + " "
                else:
                    if dst.content and isinstance(dst.content, list) and len(dst.content) > 0:
                        chunk_content += f"<IMAGE:{dst.content[0]}>" + " "

        chunk.Content = chunk_content.strip()  # 移除末尾空格

    # 构造 DynamicExtractV1Body（简化为 dict）
    chunks = [chunk.to_dict() for chunk in parse_data.Chunks]

    dynamic_extract_body = {
        "Chunks": chunks,
        "ConstFileds": const_field,
        "ReqType": "background",
        "IsImage": False,
        "Image": [img.to_dict() for img in doc_src_images] if doc_src_images else []
    }

    return dynamic_extract_body

def fetch_files():
    """获取文件列表"""
    try:
        print("正在从金山文档获取文件列表...")
        response = requests.get(
            url,
            params=params,
            headers=headers,
            cookies=cookies,
            proxies=proxies,
            timeout=30
        )
        
        if response.status_code == 200:
            print("成功获取文件列表")
            data = response.json()
            return data.get('files', [])
        else:
            print(f"获取文件失败，HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return []
    except Exception as e:
        print(f"获取文件时发生异常: {str(e)}")
        return []

def process_single_file(file: Dict, max_retries: int = 30, retry_interval: int = 2) -> Dict:
    """处理单个文件"""
    try:
        print(f"正在处理文件: {file['fname']}")
        
        # 第一步：发起解析请求
        token = dst.start_parse(
            company_id=KDOCS_CONFIG["company_id"],
            file_id=str(file["id"]),
            filename=file["fname"]
        )
        
        # 第二步：轮询结果
        for attempt in range(max_retries):
            time.sleep(retry_interval)
            print(f"DST轮询结果 - 尝试 {attempt + 1}/{max_retries}")
            
            try:
                result = dst.query_result(token)
                status = result.get("status")
                data = result.get("data", {})
                
                if status == "ok":
                    # 解析成功，处理结果
                    print(f"DST解析成功完成，文件: {file['fname']}")
                    parse_res_data = data.get("parse_res", {})
                    
                    # 使用新的结果处理逻辑
                    parsed_result = preprocess_images_and_chunks(
                        load_parse_data_from_json_object(parse_res_data), 
                        [""]
                    )
                    
                    # 构造返回结果
                    return {
                        "status": "success",
                        "data": parsed_result,
                        "metadata": {
                            'original_filename': file['fname'],
                            'file_size': file.get('fsize'),
                            'last_modified': file.get('mtime'),
                            'creator': file.get('creator', {}).get('name'),
                            'processed_at': datetime.now().isoformat()
                        }
                    }
                elif status == "wait":
                    # 继续等待
                    print(f"DST解析进行中，继续等待...")
                    continue
                else:
                    # 解析失败
                    raise Exception(f"DST parsing failed with status: {status}")
                    
            except Exception as e:
                print(f"查询解析结果时出错: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                continue
        
        # 超时
        raise Exception(f"DST parsing timeout after {max_retries} retries")
        
    except Exception as e:
        print(f"处理文件 {file['fname']} 时出错: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "metadata": {
                'original_filename': file['fname'],
                'file_size': file.get('fsize'),
                'last_modified': file.get('mtime'),
                'creator': file.get('creator', {}).get('name'),
                'processed_at': datetime.now().isoformat()
            }
        }

def process_files(files):
    """使用DST处理文件"""
    results = []
    for idx, file in enumerate(files, 1):
        if file.get('ftype') == 'file':
            print(f"正在处理文件 {idx}/{len(files)}: {file['fname']}")
            result = process_single_file(file)
            results.append(result)
    return results

def save_results(results):
    """保存结果到JSON文件"""
    if not results:
        print("没有可保存的结果")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"dst_processed_results_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'status': 'success',
                'processed_at': datetime.now().isoformat(),
                'total_files': len(results),
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到 {filename}")
        return filename
    except Exception as e:
        print(f"保存结果时出错: {str(e)}")
        return None

if __name__ == "__main__":
    # 1. 获取文件列表
    files = fetch_files()
    
    if not files:
        print("没有获取到文件，程序终止")
        exit()
    
    # 2. 使用DST处理文件
    processed_results = process_files(files)
    
    if not processed_results:
        print("没有文件被成功处理")
        exit()
    
    # 3. 保存结果
    save_results(processed_results)