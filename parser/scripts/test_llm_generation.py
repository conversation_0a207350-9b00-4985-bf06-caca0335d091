#!/usr/bin/env python3
"""
测试LLM表格生成功能
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_llm_config():
    """测试LLM配置"""
    print("=== 测试LLM配置 ===")
    try:
        from config.llm_config import LLM_CONFIG, validate_llm_config, get_random_table_topic
        print("✓ LLM配置模块导入成功")
        
        # 测试随机主题生成
        topic = get_random_table_topic()
        print(f"✓ 随机主题生成: {topic}")
        
        # 测试配置验证（预期会失败，因为没有设置API密钥）
        try:
            validate_llm_config()
            print("✓ LLM配置验证通过")
        except ValueError as e:
            print(f"✓ LLM配置验证失败（预期）: {e}")
            
    except Exception as e:
        print(f"✗ LLM配置测试失败: {e}")
        return False
    return True

def test_llm_service():
    """测试LLM服务"""
    print("\n=== 测试LLM服务 ===")
    try:
        from llm.llm_service import LLMService
        print("✓ LLM服务模块导入成功")
        
        # 测试没有配置时的行为
        try:
            config = {'api_key': '', 'endpoint': ''}
            service = LLMService(config)
            print("✗ 应该在配置不完整时失败")
            return False
        except Exception as e:
            print(f"✓ 配置不完整时正确失败: {type(e).__name__}")
            
    except Exception as e:
        print(f"✗ LLM服务测试失败: {e}")
        return False
    return True

def test_prompts():
    """测试提示词模块"""
    print("\n=== 测试提示词模块 ===")
    try:
        from llm.prompts import get_table_generation_prompt, TABLE_TEMPLATES
        print("✓ 提示词模块导入成功")
        
        # 测试表格生成提示词
        prompt = get_table_generation_prompt("员工信息表", 5, 4)
        assert "员工信息表" in prompt
        assert "5行" in prompt
        assert "4列" in prompt
        print("✓ 表格生成提示词正确")
        
        # 测试模板
        assert len(TABLE_TEMPLATES) > 0
        print(f"✓ 预定义模板数量: {len(TABLE_TEMPLATES)}")
        
    except Exception as e:
        print(f"✗ 提示词测试失败: {e}")
        return False
    return True

def test_generate_data_integration():
    """测试generate_data.py集成"""
    print("\n=== 测试generate_data.py集成 ===")
    try:
        from generate_data import init_llm_service, generate_semantic_table_data, random_table_data
        print("✓ generate_data模块导入成功")
        
        # 测试LLM服务初始化（预期失败）
        llm_service = init_llm_service()
        if llm_service is None:
            print("✓ LLM服务初始化正确失败（无配置）")
        else:
            print("✗ LLM服务不应该在无配置时成功初始化")
            return False
        
        # 测试语义表格生成（应该返回None）
        result = generate_semantic_table_data(None, "测试主题", 5, 4)
        if result is None:
            print("✓ 语义表格生成正确处理None服务")
        else:
            print("✗ 语义表格生成应该在服务为None时返回None")
            return False
        
        # 测试随机表格生成
        random_data = random_table_data(5, 8, 3, 6)
        assert len(random_data) >= 5
        assert len(random_data[0]) >= 3
        print(f"✓ 随机表格生成: {len(random_data)}行 x {len(random_data[0])}列")
        
    except Exception as e:
        print(f"✗ generate_data集成测试失败: {e}")
        return False
    return True

def test_command_line_interface():
    """测试命令行接口"""
    print("\n=== 测试命令行接口 ===")
    try:
        import subprocess
        import tempfile
        
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, 'src/generate_data.py', '--help'
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            assert '--use-llm' in result.stdout
            assert '--topics' in result.stdout
            assert '--legacy' in result.stdout
            print("✓ 命令行帮助信息正确")
        else:
            print(f"✗ 命令行帮助失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 命令行接口测试失败: {e}")
        return False
    return True

def main():
    """运行所有测试"""
    print("开始测试LLM表格生成功能...\n")
    
    tests = [
        test_llm_config,
        test_llm_service,
        test_prompts,
        test_generate_data_integration,
        test_command_line_interface
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
