#!/usr/bin/env python3
"""
过滤DST结果中的table类型chunks，并下载对应的图片
"""

import os
import sys
import json
import requests
import argparse
from urllib.parse import urlparse
from typing import Dict, List, Optional
from pathlib import Path


def setup_proxy():
    """设置代理"""
    proxies = {
        'http': 'http://127.0.0.1:18899',
        'https': 'http://127.0.0.1:18899'
    }
    return proxies


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    # 移除文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 替换非法字符
    illegal_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|', '\n', '\r', '\t']
    for char in illegal_chars:
        name_without_ext = name_without_ext.replace(char, '_')
    
    # 限制长度，避免文件名过长
    if len(name_without_ext) > 200:
        name_without_ext = name_without_ext[:200]
    
    return name_without_ext


def download_image(url: str, save_path: str, proxies: Dict = None, timeout: int = 30) -> bool:
    """
    下载图片
    :param url: 图片URL
    :param save_path: 保存路径
    :param proxies: 代理设置
    :param timeout: 超时时间
    :return: 是否成功
    """
    try:
        print(f"正在下载图片: {url}")
        print(f"保存到: {save_path}")
        
        response = requests.get(url, proxies=proxies, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存文件
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"图片下载成功: {save_path}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载图片失败 {url}: {e}")
        return False
    except Exception as e:
        print(f"保存图片失败 {save_path}: {e}")
        return False


def get_image_extension(url: str) -> str:
    """
    从URL获取图片扩展名
    :param url: 图片URL
    :return: 扩展名（包含点号）
    """
    parsed_url = urlparse(url)
    path = parsed_url.path.lower()
    
    # 常见图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']
    
    for ext in image_extensions:
        if path.endswith(ext):
            return ext
    
    # 默认使用.jpg
    return '.jpg'


def filter_and_download_table_images(json_file: str, output_dir: str = "table_images"):
    """
    过滤table类型的chunks并下载图片
    :param json_file: DST结果JSON文件路径
    :param output_dir: 图片保存目录
    """
    print(f"正在处理文件: {json_file}")
    
    # 读取JSON文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return
    
    # 检查数据格式
    if not data.get('results'):
        print("JSON文件中没有找到results数据")
        return
    
    # 设置代理
    proxies = setup_proxy()
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    total_tables = 0
    total_images = 0
    downloaded_images = 0
    
    # 遍历所有文件结果
    for file_idx, result in enumerate(data['results']):
        if result.get('status') != 'success':
            print(f"跳过失败的文件结果 {file_idx + 1}")
            continue
        
        # 获取原始文件名
        metadata = result.get('metadata', {})
        original_filename = metadata.get('original_filename', f'file_{file_idx + 1}')
        clean_filename = sanitize_filename(original_filename)
        
        print(f"\n处理文件: {original_filename}")
        
        # 获取chunks
        chunks = result.get('data', {}).get('Chunks', [])
        if not chunks:
            print(f"文件 {original_filename} 没有chunks数据")
            continue
        
        file_table_count = 0
        file_image_count = 0
        
        # 遍历所有chunks，寻找table类型
        for chunk_idx, chunk in enumerate(chunks):
            if chunk.get('label') == 'table':
                total_tables += 1
                file_table_count += 1
                
                print(f"  找到表格 chunk {chunk_idx + 1}: {chunk.get('chunk_id', 'unknown')}")
                
                # 获取该chunk的图片
                images = chunk.get('image', [])
                if not images:
                    print(f"    表格chunk没有图片")
                    continue
                
                # 下载每张图片
                for img_idx, image in enumerate(images):
                    if isinstance(image, dict):
                        image_url = image.get('url', '')
                    else:
                        # 如果image是字符串，直接作为URL
                        image_url = str(image)
                    
                    if not image_url:
                        print(f"    图片 {img_idx + 1} 没有URL")
                        continue
                    
                    total_images += 1
                    file_image_count += 1
                    
                    # 构建保存文件名：原文件名_表格序号_图片序号.扩展名
                    img_extension = get_image_extension(image_url)
                    save_filename = f"{clean_filename}_table_{file_table_count}_{img_idx + 1}{img_extension}"
                    save_path = output_path / save_filename
                    
                    # 下载图片
                    if download_image(image_url, str(save_path), proxies):
                        downloaded_images += 1
        
        print(f"  文件 {original_filename} 处理完成: {file_table_count} 个表格, {file_image_count} 张图片")
    
    # 打印总结
    print(f"\n处理完成!")
    print(f"总共找到 {total_tables} 个表格")
    print(f"总共找到 {total_images} 张图片")
    print(f"成功下载 {downloaded_images} 张图片")
    print(f"图片保存在: {output_path.absolute()}")


def main():
    parser = argparse.ArgumentParser(description='过滤DST结果中的table类型chunks并下载图片')
    parser.add_argument('json_file', help='DST结果JSON文件路径')
    parser.add_argument('-o', '--output', default='table_images', help='图片保存目录 (默认: table_images)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json_file):
        print(f"错误: 文件不存在 {args.json_file}")
        sys.exit(1)
    
    filter_and_download_table_images(args.json_file, args.output)


if __name__ == '__main__':
    main() 