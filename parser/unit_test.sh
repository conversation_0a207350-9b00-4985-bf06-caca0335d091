#!/bin/bash

# 加载公用配置
if [ -f "../config.env" ]; then
    echo "✓ 发现配置文件，加载环境变量..."
    source ../config.env
else
    echo "❌ 未找到配置文件，请确保../config.env存在"
    exit 1
fi

# 覆盖特定测试集配置
export DATASET_NAME="kingsoft"

echo "🔄 当前使用的测试集: ${DATASET_NAME}"
echo "⏱️  MonkeyOCR超时设置: ${MONKEY_OCR_TIMEOUT:-300} 秒"

# 基于测试集名称的目录结构
export DATASET_BASE_DIR="${PROJECT_ROOT_DIR}/dataset"
export DATASET_DIR="${DATASET_BASE_DIR}/${DATASET_NAME}"
export IMAGES_DIR="${DATASET_DIR}/images"

IMAGE_PATH="${IMAGES_DIR}/博腾报告书_01.png"
PROMPT="请将图片中的表格信息直接转换为标准的markdown表格格式，不要添加任何解释文字、代码块标记或其他内容，只返回纯markdown表格文本。"
OUTPUT_FILE="${VL_LLM_RESULTS_DIR}/vl_llm_result.json"

# 执行主程序
echo "开始处理图片: $IMAGE_PATH"
echo "使用提示语: \"$PROMPT\""
echo "结果将保存到: $OUTPUT_FILE"

source ${PYTHON_VENV_PATH}
export REQUESTS_CA_BUNDLE=${SSL_CERT_PATH}

# 检查并安装必要的Python依赖
echo "检查Python依赖..."
pip show markdown > /dev/null 2>&1 || {
    echo "安装markdown库..."
    pip install markdown>=3.4.0
}

python3 test_vl_parse.py "$IMAGE_PATH" --prompt "$PROMPT" --output "$OUTPUT_FILE"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "VL LLM处理完成"
    echo ""
    echo "📊 生成VL LLM结果报告..."
    
    # 生成简单的HTML报告展示VL LLM结果
    HTML_REPORT="${VL_LLM_RESULTS_DIR}/vl_llm_report.html"
    JSON_FILE="${VL_LLM_RESULTS_DIR}/vl_llm_result.json"
    MD_FILE="${VL_LLM_RESULTS_DIR}/vl_llm_result.md"
    
    if [ -f "$JSON_FILE" ] && [ -f "$MD_FILE" ]; then
        echo "生成VL LLM报告到: $HTML_REPORT"
        
        cat > "$HTML_REPORT" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>VL LLM 单元测试结果</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        .image-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .result-content {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .json-content {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VL LLM 单元测试结果</h1>
        
        <div class="section">
            <h2>📋 测试信息</h2>
            <div class="image-info">
EOF
        
        # 从JSON文件中提取信息
        python3 -c "
import json
import os
with open('$JSON_FILE', 'r', encoding='utf-8') as f:
    data = json.load(f)
print(f'<p><strong>图片路径:</strong> {data.get(\"image_path\", \"N/A\")}</p>')
print(f'<p><strong>提示语:</strong> {data.get(\"prompt\", \"N/A\")}</p>')
print(f'<p><strong>处理时间:</strong> {data.get(\"timestamp\", \"N/A\")}</p>')
print(f'<p><strong>测试集:</strong> $DATASET_NAME</p>')
" >> "$HTML_REPORT"
        
        cat >> "$HTML_REPORT" << 'EOF'
            </div>
        </div>
        
        <div class="section">
            <h2>📝 Markdown 解析结果</h2>
            <div class="result-content">
EOF
        
        # 添加渲染后的markdown内容
        if [ -f "${VL_LLM_RESULTS_DIR}/vl_llm_result.html" ]; then
            # 提取body内容
            python3 -c "
import re
with open('${VL_LLM_RESULTS_DIR}/vl_llm_result.html', 'r', encoding='utf-8') as f:
    content = f.read()
    # 提取body标签内的内容
    match = re.search(r'<body[^>]*>(.*?)</body>', content, re.DOTALL)
    if match:
        print(match.group(1))
    else:
        print(content)
" >> "$HTML_REPORT"
        else
            echo "<p>HTML渲染文件未找到</p>" >> "$HTML_REPORT"
        fi
        
        cat >> "$HTML_REPORT" << 'EOF'
            </div>
        </div>
        
        <div class="section">
            <h2>🔍 原始JSON结果</h2>
            <div class="json-content">
                <pre id="json-content"></pre>
            </div>
        </div>
    </div>
    
    <script>
        // 加载并格式化JSON内容
        fetch('./vl_llm_result.json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('json-content').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('json-content').textContent = 'Failed to load JSON data: ' + error;
            });
    </script>
</body>
</html>
EOF
        
        echo "✅ VL LLM报告已生成: $HTML_REPORT"
    else
        echo "❌ 无法生成VL LLM报告，缺少必要文件"
    fi
    
    echo "处理完成"
else
    echo "处理失败"
    exit 1
fi