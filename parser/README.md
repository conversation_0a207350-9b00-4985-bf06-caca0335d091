# 表格解析系统

一个支持多种解析器的表格解析和分析系统，能够从图片中提取表格数据并生成详细的分析报告。

## 功能特性

- **多路解析器支持**：集成KDC、MonkeyOCR、VL LLM等多种解析引擎
- **测试集管理**：支持多个测试集的独立管理和切换
- **智能数据生成**：支持LLM生成语义化表格数据
- **并行处理**：多解析器并行执行，提高处理效率
- **详细报告**：生成可视化的HTML分析报告
- **模块化架构**：清晰的代码结构，易于扩展和维护

## 项目结构

```
parser/
├── README.md                      # 项目文档
├── config.env                     # 环境配置文件
├── table_parser.sh               # 主启动脚本 ⭐
├── manage_datasets.sh             # 测试集管理工具
├── requirements_vl_llm.txt        # Python依赖
├── src/                           # 源代码目录
│   ├── main.py                    # Python主入口
│   ├── core/                      # 核心模块
│   │   ├── __init__.py
│   │   ├── pipeline.py           # 解析流程控制器
│   │   ├── parse_manager.py      # 解析器管理器
│   │   ├── file_manager.py       # 文件管理器
│   │   ├── result_manager.py     # 结果管理器
│   │   └── parsers/               # 解析器实现
│   │       ├── __init__.py
│   │       ├── base.py           # 解析器基类
│   │       ├── kdc_parser.py     # KDC解析器
│   │       ├── monkey_ocr_parser.py  # MonkeyOCR解析器
│   │       └── vl_llm_parser.py  # VL LLM解析器
│   ├── clients/                   # 外部服务客户端
│   │   ├── base_client.py
│   │   ├── kdc_client.py
│   │   ├── kdocs_client.py
│   │   ├── monkey_ocr_client.py
│   │   └── llm_client.py
│   ├── config/                    # 配置模块
│   │   └── llm_config.py
│   ├── llm/                       # LLM服务
│   │   ├── llm_service.py
│   │   └── prompts.py
│   ├── processors/                # 处理器
│   │   └── html_generator.py
│   ├── generate_data.py          # 数据生成脚本
│   └── generate_report.py        # 报告生成脚本
├── scripts/                       # 工具脚本
└── docs/                         # 文档目录
```

## 快速开始

### 重要提醒 ⚠️

**必须通过 `table_parser.sh` 脚本启动，不要直接运行Python文件！**

脚本会自动处理：
- 环境变量配置
- Python虚拟环境激活
- 依赖安装检查
- 代理设置
- SSL证书配置

### 基本用法

```bash
# 使用默认配置运行完整流程
./table_parser.sh

# 指定测试集运行
DATASET_NAME=my_test ./table_parser.sh

# 只执行解析，不生成数据和报告
./table_parser.sh --mode parse

# 只生成报告
./table_parser.sh --mode report

# 查看帮助信息
./table_parser.sh --help
```

### 高级用法

```bash
# 强制重新生成数据并运行完整流程
./table_parser.sh --generate-data --num-tables 5

# 使用指定主题生成表格
./table_parser.sh --generate-data --topics "员工表" "销售表"

# 启用调试模式
./table_parser.sh --debug

# 使用特定测试集并生成自定义数据
DATASET_NAME=sales_analysis ./table_parser.sh --generate-data --topics "销售数据" --num-tables 3
```

## 测试集管理

使用专门的测试集管理工具：

```bash
# 列出所有测试集
./manage_datasets.sh list

# 创建新测试集
./manage_datasets.sh create my_new_test

# 复制测试集
./manage_datasets.sh copy source_test target_test

# 清理测试集（保留原始数据）
./manage_datasets.sh clean my_test

# 删除测试集
./manage_datasets.sh delete old_test
```

## 配置说明

### 环境变量配置

主要配置在 `config.env` 文件中：

```bash
# 项目路径
export PROJECT_ROOT_DIR=/path/to/project
export PYTHON_VENV_PATH=/path/to/venv/bin/activate

# KS3存储配置
export KS3_AK=your_access_key
export KS3_SK=your_secret_key

# WPS配置
export WPS_AK=your_wps_ak
export WPS_SK=your_wps_sk

# LLM配置
export LLM_SERVICE_TYPE=custom
export CUSTOM_LLM_ENDPOINT=http://your-llm-endpoint

# 代理配置
export http_proxy=http://localhost:18899
export https_proxy=http://localhost:18899
```

### 支持的解析器

1. **KDC解析器** - 支持多种格式（默认、plain、kdc）
2. **MonkeyOCR解析器** - 支持HTML和LaTeX输出
3. **本地MonkeyOCR解析器** - 本地部署的MonkeyOCR服务
4. **VL LLM解析器** - 基于视觉语言模型的解析

## 工作流程

1. **环境准备** - 检查依赖、配置环境变量
2. **数据生成** - 使用LLM或随机方式生成表格图片（可选）
3. **文件处理** - 图片转PDF、上传到云端
4. **多路解析** - 并行执行多种解析器
5. **结果整合** - 汇总所有解析结果
6. **报告生成** - 生成详细的HTML分析报告

## 输出结果

### 目录结构

每个测试集都有独立的目录结构：

```
dataset/
└── {测试集名称}/
    ├── images/              # 原始图片
    ├── converted_pdfs/      # 转换的PDF文件
    ├── monkey_ocr/         # MonkeyOCR解析结果
    └── gen_data/           # 生成的数据

parse_results/
└── {测试集名称}/
    ├── parse_results_YYYYMMDD_HHMMSS.json  # 时间戳结果文件
    └── latest_results.json                  # 最新结果链接

reports/
└── {测试集名称}/
    └── report_YYYYMMDD_HHMMSS.html         # HTML分析报告
```

### 结果文件格式

解析结果JSON文件包含：

```json
{
  "timestamp": "2024_01_15_14_30_00",
  "dataset_name": "kingsoft",
  "metadata": {
    "parse_date": "2024-01-15T14:30:00",
    "version": "2.0",
    "total_files": 5
  },
  "processed_files": [...],
  "uploaded_files": [...],
  "parse_results": {
    "kdc": [...],
    "kdc_plain": [...],
    "kdc_kdc": [...],
    "monkey_ocr": [...],
    "monkey_ocr_latex": [...],
    "vl_llm": [...]
  },
  "local_monkey_results": [...]
}
```

## 扩展开发

### 添加新的解析器

1. 继承 `BaseParser` 类
2. 实现 `parse()` 和 `is_available()` 方法
3. 在 `ParseManager.create_default_parsers()` 中注册

### 添加新的客户端

1. 继承 `BaseClient` 类（如需要代理支持）
2. 实现具体的API调用逻辑
3. 在对应的解析器中使用

## 故障排除

### 常见问题

1. **ImportError**: 确保通过 `table_parser.sh` 启动
2. **SSL错误**: 检查 `SSL_CERT_PATH` 配置
3. **代理问题**: 验证代理设置和网络连接
4. **权限错误**: 确保有写入输出目录的权限

### 调试模式

启用调试模式获取详细日志：

```bash
./table_parser.sh --debug
```

### 日志文件

系统会生成 `parse.log` 日志文件，包含详细的执行信息。

## 许可证

[添加许可证信息]

## 联系方式

[添加联系方式] 