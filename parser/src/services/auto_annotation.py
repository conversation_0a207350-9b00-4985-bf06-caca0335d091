"""
自动标注服务

将生成的原始表格数据自动转换为人工标注数据
"""

import os
import json
import requests
from typing import List, Dict, Optional
from utils.table_parser import TableDataParser


class AutoAnnotationService:
    """自动标注服务"""
    
    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url.rstrip('/')
        self.table_parser = TableDataParser()
        
    def create_annotations_from_generated_data(
        self, 
        dataset_name: str, 
        gen_data_dir: str, 
        images_dir: str,
        annotator: str = "auto_generator"
    ) -> Dict[str, int]:
        """
        从生成的数据创建自动标注
        
        Args:
            dataset_name: 数据集名称
            gen_data_dir: 生成数据目录
            images_dir: 图片目录
            annotator: 标注员名称
            
        Returns:
            Dict: 包含成功和失败数量的统计
        """
        stats = {
            'success': 0,
            'failed': 0,
            'errors': []
        }
        
        try:
            # 获取图片列表
            image_files = self._get_image_files(images_dir)
            if not image_files:
                print(f"在目录 {images_dir} 中未找到图片文件")
                return stats
                
            print(f"找到 {len(image_files)} 个图片文件，开始创建自动标注...")
            
            # 确保数据集存在
            self._ensure_dataset_exists(dataset_name)
            
            # 同步图片到数据库
            self._sync_images_to_database(dataset_name)
            
            # 为每个图片创建标注
            for image_file in image_files:
                try:
                    result = self._create_annotation_for_image(
                        dataset_name, 
                        image_file, 
                        gen_data_dir, 
                        annotator
                    )
                    
                    if result:
                        stats['success'] += 1
                        print(f"✅ 成功创建标注: {image_file}")
                    else:
                        stats['failed'] += 1
                        print(f"❌ 创建标注失败: {image_file}")
                        
                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"处理图片 {image_file} 时出错: {str(e)}"
                    stats['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
            
            print(f"\n自动标注完成: 成功 {stats['success']} 个，失败 {stats['failed']} 个")
            
        except Exception as e:
            error_msg = f"自动标注服务出错: {str(e)}"
            stats['errors'].append(error_msg)
            print(f"❌ {error_msg}")
            
        return stats
    
    def _get_image_files(self, images_dir: str) -> List[str]:
        """获取图片文件列表"""
        if not os.path.exists(images_dir):
            return []
            
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'}
        image_files = []
        
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(filename)
                
        return sorted(image_files)
    
    def _ensure_dataset_exists(self, dataset_name: str) -> bool:
        """确保数据集存在"""
        try:
            # 检查数据集是否存在
            response = requests.get(f"{self.backend_url}/api/datasets/{dataset_name}")
            if response.status_code == 200:
                return True
            elif response.status_code == 404:
                # 创建数据集
                create_data = {
                    "name": dataset_name,
                    "description": f"自动生成的数据集: {dataset_name}"
                }
                response = requests.post(f"{self.backend_url}/api/datasets", json=create_data)
                if response.status_code == 200:
                    print(f"✅ 创建数据集: {dataset_name}")
                    return True
                else:
                    print(f"❌ 创建数据集失败: {response.text}")
                    return False
            else:
                print(f"❌ 检查数据集失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 数据集操作失败: {e}")
            return False
    
    def _sync_images_to_database(self, dataset_name: str) -> bool:
        """同步图片到数据库"""
        try:
            response = requests.post(f"{self.backend_url}/api/datasets/{dataset_name}/images/sync")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 同步图片到数据库: 新增 {result.get('added', 0)} 个")
                return True
            else:
                print(f"❌ 同步图片失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 同步图片失败: {e}")
            return False
    
    def _create_annotation_for_image(
        self, 
        dataset_name: str, 
        image_filename: str, 
        gen_data_dir: str, 
        annotator: str
    ) -> bool:
        """为单个图片创建标注"""
        try:
            # 解析生成的表格数据
            parsed_data = self.table_parser.parse_generated_data(gen_data_dir, image_filename)
            if not parsed_data:
                print(f"无法解析图片 {image_filename} 对应的表格数据")
                return False
            
            # 检查是否已存在标注
            if self._annotation_exists(dataset_name, image_filename):
                print(f"图片 {image_filename} 已存在标注，跳过")
                return True
            
            # 创建标注数据
            annotation_data = {
                "dataset_name": dataset_name,
                "image_filename": image_filename,
                "annotator": annotator,
                "table_structure": parsed_data['table_structure'],
                "table_content": parsed_data['table_content'],
                "annotation_type": "auto_generated",
                "status": "completed"
            }
            
            # 发送到后端API
            response = requests.post(f"{self.backend_url}/api/annotations", json=annotation_data)
            if response.status_code == 200:
                return True
            else:
                print(f"创建标注API调用失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"创建标注失败: {e}")
            return False
    
    def _annotation_exists(self, dataset_name: str, image_filename: str) -> bool:
        """检查标注是否已存在"""
        try:
            params = {
                "dataset_name": dataset_name,
                "limit": 1
            }
            response = requests.get(f"{self.backend_url}/api/annotations", params=params)
            if response.status_code == 200:
                annotations = response.json()
                for annotation in annotations:
                    if annotation.get('image_filename') == image_filename:
                        return True
            return False
            
        except Exception as e:
            print(f"检查标注是否存在失败: {e}")
            return False
    
    def batch_import_annotations(
        self, 
        dataset_name: str, 
        annotations_data: List[Dict],
        overwrite: bool = False
    ) -> Dict[str, int]:
        """
        批量导入标注数据
        
        Args:
            dataset_name: 数据集名称
            annotations_data: 标注数据列表
            overwrite: 是否覆盖已存在的标注
            
        Returns:
            Dict: 导入统计
        """
        stats = {
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'errors': []
        }
        
        try:
            for annotation in annotations_data:
                try:
                    # 检查是否已存在
                    image_filename = annotation.get('image_filename')
                    if not overwrite and self._annotation_exists(dataset_name, image_filename):
                        stats['skipped'] += 1
                        continue
                    
                    # 创建标注
                    annotation['dataset_name'] = dataset_name
                    response = requests.post(f"{self.backend_url}/api/annotations", json=annotation)
                    
                    if response.status_code == 200:
                        stats['success'] += 1
                    else:
                        stats['failed'] += 1
                        stats['errors'].append(f"创建标注失败 {image_filename}: {response.text}")
                        
                except Exception as e:
                    stats['failed'] += 1
                    stats['errors'].append(f"处理标注数据失败: {str(e)}")
            
        except Exception as e:
            stats['errors'].append(f"批量导入失败: {str(e)}")
            
        return stats
