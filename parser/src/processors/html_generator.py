# utils/html_generator.py
import json
import base64
from typing import List, Dict
import os
import re
from bs4 import BeautifulSoup
from collections import Counter
import re


def convert_markdown_table_to_html(markdown_text: str) -> str:
    """将markdown表格转换为HTML表格"""
    if not markdown_text or not markdown_text.strip():
        return "<div style='color:gray;'>无表格内容</div>"
    
    try:
        lines = markdown_text.strip().split('\n')
        table_lines = []
        in_table = False
        
        for line in lines:
            line = line.strip()
            if line.startswith('|') and line.endswith('|'):
                # 跳过分隔行（如 | --- | --- |）
                if not line.replace('|', '').replace('-', '').replace(' ', '') == '':
                    table_lines.append(line)
                    in_table = True
            elif in_table:
                # 如果已经在表格中但当前行不是表格行，表格结束
                break
        
        if not table_lines:
            return "<div style='color:gray;'>无有效表格内容</div>"
        
        # 解析所有行的单元格，找出最大列数
        all_rows = []
        max_cols = 0
        
        for line in table_lines:
            # 移除首尾的|，然后按|分割
            cells = line[1:-1].split('|')
            cells = [cell.strip() for cell in cells]
            all_rows.append(cells)
            max_cols = max(max_cols, len(cells))
        
        # 补齐所有行到相同列数
        for row in all_rows:
            while len(row) < max_cols:
                row.append('')
        
        # 构建HTML表格
        html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"]
        
        for i, cells in enumerate(all_rows):
            if i == 0:
                # 第一行作为表头
                html.append("<thead><tr>")
                for cell in cells:
                    html.append(f"<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>{cell}</th>")
                html.append("</tr></thead><tbody>")
            else:
                # 其他行作为数据行
                html.append("<tr>")
                for cell in cells:
                    html.append(f"<td style='border: 1px solid #ddd; padding: 8px;'>{cell}</td>")
                html.append("</tr>")
        
        html.append("</tbody></table>")
        return ''.join(html)
        
    except Exception as e:
        return f"<div style='color:red;'>表格解析失败: {e}</div>"


def get_image_base64(image_path: str) -> str:
    """将图片转换为base64编码"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"读取图片失败: {str(e)}")
        return ""

def load_vl_llm_results_from_parse_data(parse_data):
    """从解析结果数据中加载VL LLM结果，返回文件名到结果的映射"""
    vl_llm_map = {}
    try:
        vl_llm_results = parse_data.get("vl_llm_results", [])

        for vl_llm_result in vl_llm_results:
            filename = vl_llm_result.get("filename", "")
            original_image = vl_llm_result.get("original_image", "")
            result = vl_llm_result.get("result")

            if original_image:
                # 从原始图片名提取base_name
                base_name = os.path.splitext(original_image)[0]
                vl_llm_map[base_name] = {
                    "result": result,
                    "image_path": original_image,
                    "filename": filename
                }

        print(f"从解析结果中加载了 {len(vl_llm_map)} 个VL LLM结果")

    except Exception as e:
        print(f"从解析结果中加载VL LLM结果失败: {e}")

    return vl_llm_map

def load_vl_llm_results():
    """加载所有VL LLM解析结果，返回文件名到结果的映射（向后兼容）"""
    vl_llm_map = {}
    try:
        # 使用新的VL LLM结果路径
        dataset_name = os.getenv("DATASET_NAME", "default_test")
        vl_llm_base_dir = os.getenv("VL_LLM_RESULTS_BASE_DIR", os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'vl_llm_results')))
        vl_llm_dir = os.path.join(vl_llm_base_dir, dataset_name)

        if os.path.exists(vl_llm_dir):
            for filename in os.listdir(vl_llm_dir):
                if filename.endswith('_vl_llm.json'):
                    # 从文件名提取原始图片名
                    base_name = filename.replace('_vl_llm.json', '')
                    file_path = os.path.join(vl_llm_dir, filename)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            vl_llm_map[base_name] = data
                    except Exception as e:
                        print(f"加载VL LLM结果文件失败 {filename}: {e}")
    except Exception as e:
        print(f"加载VL LLM结果目录失败: {e}")

    return vl_llm_map

def generate_html_report(files: List[Dict], results: List[Dict], uploaded_files: List[Dict], monkey_ocr_results: List[Dict], monkey_ocr_results_v2: List[Dict], kdc_plain_results: List[Dict] = None, kdc_kdc_results: List[Dict] = None, monkey_ocr_local_results: List[Dict] = None, parse_data: Dict = None) -> str:
    """生成HTML报告，主循环以 uploaded_files（本地图片）为主，保证每一行严格对应本地图片"""
    html = """
    <html>
    <head>
        <title>WPS File Processing Results</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            
            /* 主表格样式 */
            #main-table {
                border-collapse: collapse;
                width: 100%;
                max-width: none;
                margin: 0;
                table-layout: fixed;
            }
            #main-table > thead > tr > th,
            #main-table > tbody > tr > td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
                vertical-align: top;
                overflow: hidden;
                position: relative;
            }

            /* 合并单元格样式 */
            #main-table > tbody > tr > td[rowspan] {
                vertical-align: middle;
                text-align: center;
            }
            #main-table > thead > tr > th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            #main-table > tbody > tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            
            /* 固定列宽设置 */
            #main-table > thead > tr > th:nth-child(1),
            #main-table > tbody > tr > td:nth-child(1) { width: 60px; }      /* 序号 */
            #main-table > thead > tr > th:nth-child(2),
            #main-table > tbody > tr > td:nth-child(2) { width: 250px; }     /* 数据信息 */
            #main-table > thead > tr > th:nth-child(3),
            #main-table > tbody > tr > td:nth-child(3) { width: 400px; }     /* 原始文本 */
            #main-table > thead > tr > th:nth-child(4),
            #main-table > tbody > tr > td:nth-child(4) { width: 550px; }     /* 渲染结果 */
            #main-table > thead > tr > th:nth-child(5),
            #main-table > tbody > tr > td:nth-child(5) { width: 250px; }     /* 准确率报告 */
            
            /* 单元格内容容器样式 */
            .cell-content {
                height: 100%;
                min-height: 100px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                padding: 8px;
                box-sizing: border-box;
                width: 100%;
                word-wrap: break-word;
                word-break: break-all;
            }
            
            /* 非合并单元格的内容容器 */
            tr:not([class*="case-first-row"]) .cell-content,
            td:not([rowspan]) .cell-content {
                max-height: 350px !important;
                height: 350px !important;
                overflow-y: auto;
                overflow-x: auto;
                display: block;
                min-height: 350px !important;
                box-sizing: border-box;
            }
            
            /* pre标签内容滚动 */
            #main-table pre {
                max-height: 300px;
                overflow-y: auto;
                overflow-x: auto;
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-size: 12px;
            }
            
            pre { white-space: pre-wrap; word-wrap: break-word; }
            .image-preview { 
                max-width: 75px; 
                max-height: 65px; 
                cursor: pointer; 
                border: 1px solid #ddd;
                border-radius: 4px;
                transition: transform 0.2s ease;
            }
            .image-preview:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            }
            .image-container { 
                text-align: center;
                padding: 4px;
                background: #fff;
                border-radius: 3px;
                border: 1px solid #dee2e6;
                margin: 2px 0;
                height: 90px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .image-name { 
                font-size: 10px; 
                color: #666; 
                margin-top: 4px;
                font-family: monospace;
                word-break: break-all;
                text-align: center;
                line-height: 1.2;
                max-height: 24px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .summary-row { background-color: #e6f3ff !important; font-weight: bold; }
            
            /* MonkeyOCR结果样式 */
            .monkey-ocr-result {
                height: 300px !important;
                max-height: 300px !important;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                box-sizing: border-box;
                overflow: hidden;
            }
            .monkey-ocr-result iframe {
                width: 100% !important;
                height: 280px !important;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
            }
            
            /* MonkeyOCR Local结果样式 */
            .monkey-ocr-local-result {
                height: 300px !important;
                max-height: 300px !important;
                padding: 10px;
                background-color: #f0f9ff;
                border: 1px solid #bae6fd;
                border-radius: 4px;
                box-sizing: border-box;
                overflow: auto;
            }
            .monkey-ocr-local-result table {
                border-collapse: collapse;
                width: 100%;
                margin: 0;
            }
            .monkey-ocr-local-result th,
            .monkey-ocr-local-result td {
                border: 1px solid #ddd;
                padding: 6px;
                text-align: left;
                vertical-align: top;
            }
            .monkey-ocr-local-result th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .monkey-ocr-local-result pre {
                margin: 0;
                padding: 8px;
                background-color: #fff;
                border: 1px solid #e9ecef;
                border-radius: 3px;
                font-size: 12px;
                line-height: 1.4;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            
            /* 添加markdown渲染样式 */
            .rendered-markdown {
                padding: 10px;
                background-color: #fff;
                border: 1px solid #eee;
                border-radius: 4px;
            }
            .rendered-markdown table {
                border-collapse: collapse;
                margin: 10px 0;
            }
            .rendered-markdown th,
            .rendered-markdown td {
                border: 1px solid #ddd;
                padding: 6px;
            }
            .rendered-markdown img {
                max-width: 100%;
                height: auto;
            }
            
            /* 模态框样式 */
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.9);
            }
            .modal-content {
                margin: auto;
                display: block;
                max-width: 90%;
                max-height: 90%;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
            .close {
                position: absolute;
                right: 25px;
                top: 10px;
                color: #f1f1f1;
                font-size: 40px;
                font-weight: bold;
                cursor: pointer;
            }
            
            /* 未命中单元格表格样式 */
            .missed-detail-table {
                max-height: 180px;
                overflow-y: auto;
                display: block;
            }
            .missed-detail-table table {
                border-collapse: collapse;
                width: 100%;
            }
            .missed-detail-table th,
            .missed-detail-table td {
                border: 1px solid #ddd;
                padding: 4px;
                font-size: inherit;
            }
            
            /* 标注表格样式 */
            .gen-table-html table {
                border-collapse: collapse;
                width: 100%;
                height: 100%;
                table-layout: fixed;
            }
            .gen-table-html th,
            .gen-table-html td {
                border: 1px solid #ddd;
                padding: 6px;
                vertical-align: top;
                min-height: 30px;
                height: auto;
            }
            .gen-table-html tbody {
                height: 100%;
            }
            .gen-table-html tr {
                height: auto;
                min-height: 30px;
            }
            
            /* 确保标注表格在flex容器中正确展开 */
            .table-section .gen-table-html > table {
                flex: 1 1 auto;
                min-height: 0;
            }
            .table-section .gen-table-html > div {
                flex: 1 1 auto;
                display: flex;
                flex-direction: column;
            }
            .table-section .gen-table-html > div > table {
                flex: 1 1 auto;
                height: 100%;
            }
            
            /* 用例边框样式 - 每个用例包含3行或4行 */
            /* 使用子选择器限制只应用到主表格的直接子单元格 */
            #main-table > tbody > tr.case-first-row > td {
                border-top: 3px solid #333 !important;
                border-left: 3px solid #333 !important;
                border-right: 3px solid #333 !important;
            }
            #main-table > tbody > tr.case-last-row > td {
                border-bottom: 3px solid #333 !important;
                border-left: 3px solid #333 !important;
                border-right: 3px solid #333 !important;
            }
            #main-table > tbody > tr.case-middle-row > td {
                border-left: 3px solid #333 !important;
                border-right: 3px solid #333 !important;
            }
            
            /* VL LLM结果样式 */
            .vl-llm-result {
                max-height: 300px !important;
                height: 300px !important;
                overflow-y: auto !important;
                overflow-x: auto !important;
                padding: 10px;
                background-color: #fff5f5;
                border: 1px solid #ffccd5;
                border-radius: 4px;
                box-sizing: border-box;
            }
            .vl-llm-result table {
                border-collapse: collapse;
                width: 100%;
                margin: 0;
            }
            .vl-llm-result th,
            .vl-llm-result td {
                border: 1px solid #ddd;
                padding: 6px;
                text-align: left;
                vertical-align: top;
            }
            .vl-llm-result th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            /* 单元格内容样式已在上方定义 */
            
            /* 主表格单元格样式 */
            #main-table td {
                vertical-align: top;
                padding: 0;
                height: auto;
            }
            
            /* 合并单元格特殊处理 */
            #main-table td[rowspan] {
                padding: 0 !important;
                vertical-align: top !important;
                height: 800px !important;
                max-height: 800px !important;
            }
            
            #main-table td[rowspan] .cell-content {
                height: 100% !important;
                width: 100% !important;
                max-height: 800px !important;
                justify-content: flex-start;
                align-items: stretch;
                display: flex !important;
                flex-direction: column !important;
                overflow: hidden !important;
                padding: 8px !important;
                box-sizing: border-box !important;
                margin: 0 !important;
            }
            
            /* 数据信息部分样式 */
            .data-info-section {
                display: flex !important;
                flex-direction: column !important;
                height: 100% !important;
                width: 100% !important;
                flex: 1 1 auto !important;
                gap: 10px !important;
                padding: 0 !important;
                margin: 0 !important;
                box-sizing: border-box !important;
            }
            
            .filename-section {
                flex: 0 0 60px !important;
                height: 60px !important;
                min-height: 60px !important;
                max-height: 60px !important;
                padding: 6px !important;
                background: #f8f9fa !important;
                border-radius: 4px !important;
                border: 1px solid #e9ecef !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }
            
            .filename-content {
                font-size: 12px;
                padding: 4px 6px;
                background: #fff;
                border-radius: 3px;
                border: 1px solid #dee2e6;
                word-break: break-all;
                font-family: monospace;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                height: 100%;
                box-sizing: border-box;
            }
            
            .image-section {
                flex: 0 0 110px !important;
                height: 110px !important;
                min-height: 110px !important;
                max-height: 110px !important;
                padding: 6px !important;
                background: #f8f9fa !important;
                border-radius: 4px !important;
                border: 1px solid #e9ecef !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }
            
            .table-section {
                flex: 1 1 auto !important;
                padding: 6px !important;
                background: #f8f9fa !important;
                border-radius: 4px !important;
                border: 1px solid #e9ecef !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }
            
            .table-section .gen-table-html {
                height: 100% !important;
                min-height: 100% !important;
                max-height: 100% !important;
                width: 100% !important;
                overflow-y: auto !important;
                overflow-x: auto !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 3px !important;
                background: #fff !important;
                padding: 4px !important;
                display: flex !important;
                flex-direction: column !important;
                box-sizing: border-box !important;
                margin: 0 !important;
            }
        </style>
    </head>
    <body>
        <h1>WPS File Processing Results</h1>
        <div id=\"imageModal\" class=\"modal\">
            <span class=\"close\">&times;</span>
            <img class=\"modal-content\" id=\"modalImage\">
        </div>
        <table id=\"main-table\">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>数据信息</th>
                    <th>原始文本</th>
                    <th>渲染结果</th>
                    <th>准确率报告</th>
                </tr>
            </thead>
            <tbody>
    """

    # 创建 fname 到各类结果的映射
    fname_to_result = {f.get("fname"): r for f, r in zip(files, results)}
    monkey_ocr_map = {r["filename"]: r["result"] for r in monkey_ocr_results if isinstance(r, dict) and "filename" in r}
    monkey_ocr_map_v2 = {r["filename"]: r["result"] for r in monkey_ocr_results_v2 if isinstance(r, dict) and "filename" in r}
    
    # 添加新的KDC解析结果映射
    kdc_plain_map = {}
    kdc_kdc_map = {}
    if kdc_plain_results and len(kdc_plain_results) == len(files):
        kdc_plain_map = {f.get("fname"): r for f, r in zip(files, kdc_plain_results)}
    if kdc_kdc_results and len(kdc_kdc_results) == len(files):
        kdc_kdc_map = {f.get("fname"): r for f, r in zip(files, kdc_kdc_results)}
    
    # 加载所有VL LLM解析结果
    if parse_data:
        vl_llm_map = load_vl_llm_results_from_parse_data(parse_data)
    else:
        vl_llm_map = load_vl_llm_results()  # 向后兼容

    # 用于计算平均准确率和表格检测率
    kdc_accuracies = []
    monkey_accuracies = []
    monkey_accuracies_v2 = []
    kdc_plain_accuracies = []
    kdc_kdc_accuracies = []
    vl_llm_accuracies = []
    
    # 用于统计表格检测情况
    kdc_detected = []
    monkey_detected = []
    monkey_detected_v2 = []
    kdc_plain_detected = []
    kdc_kdc_detected = []
    vl_llm_detected = []

    # 存储所有行的HTML
    rows_html = ""

    for i, upfile in enumerate(uploaded_files, 1):
        file_name = upfile.get("fname", "")
        original_image = upfile.get("original_image", "")
        result = fname_to_result.get(file_name)
        monkey_result = monkey_ocr_map.get(file_name)
        monkey_result_v2 = monkey_ocr_map_v2.get(file_name)
        kdc_plain_result = kdc_plain_map.get(file_name)
        kdc_kdc_result = kdc_kdc_map.get(file_name)
        
        # 根据原始图片名查找对应的VL LLM结果
        original_base_name = os.path.splitext(original_image)[0]  # 移除扩展名
        vl_llm_result = vl_llm_map.get(original_base_name)

        # 获取原始图片的base64编码 - 使用环境变量支持测试集隔离
        dataset_name = os.getenv("DATASET_NAME", "default_test")
        images_base_dir = os.getenv("IMAGES_BASE_DIR", os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'dataset')))
        images_dir = os.path.join(images_base_dir, dataset_name, 'images')
        image_path = os.path.join(images_dir, original_image)
        image_base64 = get_image_base64(image_path)
        image_html = ""
        if image_base64:
            image_html = f"""
                <div class="image-container">
                    <img class="image-preview" src="data:image/png;base64,{image_base64}" 
                         alt="{original_image}" onclick="openModal(this.src)">
                    <div class="image-name">{original_image}</div>
                </div>
            """
        
        # 新增：渲染gen_data下的html表格 - 使用环境变量支持测试集隔离
        gen_data_dir = os.path.join(images_base_dir, dataset_name, 'gen_data')
        gen_html = ""
        gen_html_path = os.path.join(gen_data_dir, f"{original_base_name}.html")
        if os.path.exists(gen_html_path):
            try:
                with open(gen_html_path, 'r', encoding='utf-8') as f:
                    gen_html = f.read()
            except Exception as e:
                gen_html = f"<div style='color:red'>读取失败: {e}</div>"
        else:
            gen_html = "<div style='color:gray'>无标注表格</div>"
        
        # 提取KDC markdown内容
        markdown_content = ""
        if isinstance(result, dict):
            try:
                data = result.get("data", [])
                if isinstance(data, list) and len(data) > 0:
                    markdown_content = data[0].get("markdown", "")
                elif isinstance(data, dict):
                    markdown_content = data.get("markdown", "")
            except (AttributeError):
                markdown_content = "Unable to parse markdown content"
        

        
        # 解析标注表格为二维list
        gt_matrix = html_table_to_matrix(gen_html)
        # 检查是否有标注表格
        has_gt_table = len(gt_matrix) > 0 and any(len(row) > 0 for row in gt_matrix)
        
        # 解析KDC markdown为html再转二维list
        kdc_html = markdown_content
        kdc_matrix = html_table_to_matrix(kdc_html)
        
        # 解析KDC plain结果
        kdc_plain_content = ""
        kdc_plain_matrix = []
        if kdc_plain_result and isinstance(kdc_plain_result, dict):
            try:
                data = kdc_plain_result.get("data", [])
                if isinstance(data, list) and len(data) > 0:
                    kdc_plain_content = data[0].get("plain", "")
                elif isinstance(data, dict):
                    kdc_plain_content = data.get("plain", "")
                # 对于plain格式，尝试解析为表格（plain是纯文本，可能需要特殊处理）
                if kdc_plain_content:
                    kdc_plain_matrix = parse_plain_to_matrix(kdc_plain_content)
            except (AttributeError):
                kdc_plain_content = "Unable to parse plain content"
        
        # 解析KDC kdc结果
        kdc_kdc_content = ""
        kdc_kdc_matrix = []
        kdc_kdc_doc_data = None  # 保存原始doc数据对象
        if kdc_kdc_result and isinstance(kdc_kdc_result, dict):
            try:
                data = kdc_kdc_result.get("data", [])
                if isinstance(data, list) and len(data) > 0:
                    doc_data = data[0].get("doc", {})
                    if doc_data:
                        # 保存原始doc数据对象
                        kdc_kdc_doc_data = doc_data
                        # 从KDC文档结构中提取文本内容
                        kdc_kdc_content = extract_text_from_kdc_doc(doc_data)
                        kdc_kdc_matrix = parse_kdc_doc_to_matrix(doc_data)
                elif isinstance(data, dict):
                    doc_data = data.get("doc", {})
                    if doc_data:
                        kdc_kdc_doc_data = doc_data
                        kdc_kdc_content = extract_text_from_kdc_doc(doc_data)
                        kdc_kdc_matrix = parse_kdc_doc_to_matrix(doc_data)
            except (AttributeError):
                kdc_kdc_content = "Unable to parse kdc content"
                kdc_kdc_doc_data = None
        
        # 解析MonkeyOCR html为二维list
        monkey_matrix = []
        if monkey_result and isinstance(monkey_result, dict) and monkey_result.get('html') and not monkey_result.get('is_timeout'):
            monkey_matrix = html_table_to_matrix(monkey_result['html'])
        # 解析MonkeyOCR2 html为二维list
        monkey_matrix_v2 = []
        if monkey_result_v2 and isinstance(monkey_result_v2, dict) and monkey_result_v2.get('html') and not monkey_result_v2.get('is_timeout'):
            monkey_matrix_v2 = html_table_to_matrix(monkey_result_v2['html'])
        
        # 解析VL LLM markdown为二维list
        vl_llm_matrix = []
        if vl_llm_result and isinstance(vl_llm_result, dict):
            result_data = vl_llm_result.get('result', {})
            if isinstance(result_data, dict):
                choices = result_data.get('choices', [])
                if choices and len(choices) > 0:
                    message = choices[0].get('message', {})
                    content = message.get('content', '')
                    if content:
                        vl_llm_matrix = html_table_to_matrix(content)
        
        # 判断是否检测到表格（解析出有效的表格结构）
        kdc_has_table = len(kdc_matrix) > 0 and any(len(row) > 0 for row in kdc_matrix)
        kdc_plain_has_table = len(kdc_plain_matrix) > 0 and any(len(row) > 0 for row in kdc_plain_matrix)
        kdc_kdc_has_table = len(kdc_kdc_matrix) > 0 and any(len(row) > 0 for row in kdc_kdc_matrix)
        monkey_has_table = len(monkey_matrix) > 0 and any(len(row) > 0 for row in monkey_matrix)
        monkey_has_table_v2 = len(monkey_matrix_v2) > 0 and any(len(row) > 0 for row in monkey_matrix_v2)
        vl_llm_has_table = len(vl_llm_matrix) > 0 and any(len(row) > 0 for row in vl_llm_matrix)
        
        # 计算准确率（只有标注表格且检测到表格的情况才计算）
        if has_gt_table:
            kdc_acc = calc_acc(gt_matrix, kdc_matrix) if kdc_has_table else 0.0
            kdc_plain_acc = calc_acc(gt_matrix, kdc_plain_matrix) if kdc_plain_has_table else 0.0
            kdc_kdc_acc = calc_acc(gt_matrix, kdc_kdc_matrix) if kdc_kdc_has_table else 0.0
            monkey_acc = calc_acc(gt_matrix, monkey_matrix) if monkey_has_table else 0.0
            monkey_acc_v2 = calc_acc(gt_matrix, monkey_matrix_v2) if monkey_has_table_v2 else 0.0
            vl_llm_acc = calc_acc(gt_matrix, vl_llm_matrix) if vl_llm_has_table else 0.0
        else:
            # 没有标注表格的case，准确率为None，表示用"-"显示
            kdc_acc = None
            kdc_plain_acc = None
            kdc_kdc_acc = None
            monkey_acc = None
            monkey_acc_v2 = None
            vl_llm_acc = None
        
        # 收集准确率和检测情况用于计算平均值
        kdc_detected.append(kdc_has_table)
        kdc_plain_detected.append(kdc_plain_has_table)
        kdc_kdc_detected.append(kdc_kdc_has_table)
        monkey_detected.append(monkey_has_table)
        monkey_detected_v2.append(monkey_has_table_v2)
        vl_llm_detected.append(vl_llm_has_table)
        
        # 只有标注表格且检测到表格时才计入准确率统计
        if has_gt_table and kdc_has_table:
            kdc_accuracies.append(kdc_acc)
        if has_gt_table and kdc_plain_has_table:
            kdc_plain_accuracies.append(kdc_plain_acc)
        if has_gt_table and kdc_kdc_has_table:
            kdc_kdc_accuracies.append(kdc_kdc_acc)
        if has_gt_table and monkey_has_table:
            monkey_accuracies.append(monkey_acc)
        if has_gt_table and monkey_has_table_v2:
            monkey_accuracies_v2.append(monkey_acc_v2)
        if has_gt_table and vl_llm_has_table:
            vl_llm_accuracies.append(vl_llm_acc)
        # 详情
        kdc_missed = diff_cells(gt_matrix, kdc_matrix) if kdc_acc is not None and kdc_acc < 1.0 else []
        kdc_plain_missed = diff_cells(gt_matrix, kdc_plain_matrix) if kdc_plain_acc is not None and kdc_plain_acc < 1.0 else []
        kdc_kdc_missed = diff_cells(gt_matrix, kdc_kdc_matrix) if kdc_kdc_acc is not None and kdc_kdc_acc < 1.0 else []
        monkey_missed = diff_cells(gt_matrix, monkey_matrix) if monkey_acc is not None and monkey_acc < 1.0 else []
        monkey_missed_v2 = diff_cells(gt_matrix, monkey_matrix_v2) if monkey_acc_v2 is not None and monkey_acc_v2 < 1.0 else []
        vl_llm_missed = diff_cells(gt_matrix, vl_llm_matrix) if vl_llm_acc is not None and vl_llm_acc < 1.0 else []
        
        # 生成KDC报告
        if kdc_has_table and has_gt_table:
            # 只有当有标注表格且检测到表格时才显示准确率
            kdc_report = f"<b>是否解析表格：</b>✓ 是<br><b>准确率：</b>{kdc_acc:.2%}"
            if kdc_missed:
                kdc_report += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in kdc_missed]) +
                    "</tbody></table></div>")
        else:
            # 没有检测到表格或没有标注表格时显示'-'，"否"显示为红色加粗
            table_status = "✓ 是" if kdc_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
            kdc_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
        
        # 生成KDC Plain报告
        if kdc_plain_has_table and has_gt_table:
            kdc_plain_report = f"<b>是否解析表格：</b>✓ 是<br><b>准确率：</b>{kdc_plain_acc:.2%}"
            if kdc_plain_missed:
                kdc_plain_report += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in kdc_plain_missed]) +
                    "</tbody></table></div>")
        else:
            table_status = "✓ 是" if kdc_plain_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
            kdc_plain_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
        
        # 生成KDC KDC报告
        if kdc_kdc_has_table and has_gt_table:
            kdc_kdc_report = f"<b>是否解析表格：</b>✓ 是<br><b>准确率：</b>{kdc_kdc_acc:.2%}"
            if kdc_kdc_missed:
                kdc_kdc_report += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in kdc_kdc_missed]) +
                    "</tbody></table></div>")
        else:
            table_status = "✓ 是" if kdc_kdc_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
            kdc_kdc_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
        
        # MonkeyOCR (table prompt) 报告
        if monkey_result and isinstance(monkey_result, dict) and monkey_result.get('is_timeout'):
            processing_time = monkey_result.get('processing_time', 0)
            monkey_report = f"<b>是否解析表格：</b><span style='color: red; font-weight: bold;'>✗ 否（超时）</span><br><span style='color: red;'>⏰ 超时 ({processing_time:.1f}秒)</span>"
        else:
            if monkey_acc is not None:
                table_status = "✓ 是" if monkey_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                monkey_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>{monkey_acc:.2%}"
            else:
                table_status = "✓ 是" if monkey_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                monkey_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
            if monkey_missed:
                monkey_report += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in monkey_missed]) +
                    "</tbody></table></div>")

        # MonkeyOCR (parse) 报告
        if monkey_result_v2 and isinstance(monkey_result_v2, dict) and monkey_result_v2.get('is_timeout'):
            processing_time = monkey_result_v2.get('processing_time', 0)
            monkey_report_v2 = f"<b>是否解析表格：</b><span style='color: red; font-weight: bold;'>✗ 否（超时）</span><br><span style='color: red;'>⏰ 超时 ({processing_time:.1f}秒)</span>"
        else:
            if monkey_acc_v2 is not None:
                table_status = "✓ 是" if monkey_has_table_v2 else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                monkey_report_v2 = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>{monkey_acc_v2:.2%}"
            else:
                table_status = "✓ 是" if monkey_has_table_v2 else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                monkey_report_v2 = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
            if monkey_missed_v2:
                monkey_report_v2 += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in monkey_missed_v2]) +
                    "</tbody></table></div>")
        
        # 创建数据信息列内容（将在4行中合并显示） - 添加强制内联样式确保占满整个td
        data_info_html = f"""
            <div class='data-info-section' style='height: 100% !important; width: 100% !important; display: flex !important; flex-direction: column !important; gap: 10px !important; margin: 0 !important; padding: 0 !important; box-sizing: border-box !important;'>
                <div class='filename-section' style='flex: 0 0 60px !important; height: 60px !important; min-height: 60px !important; max-height: 60px !important; padding: 6px !important; background: #f8f9fa !important; border-radius: 4px !important; border: 1px solid #e9ecef !important; overflow: hidden !important; box-sizing: border-box !important;'>
                    <b>文件名：</b><br>
                    <div class='filename-content' style='height: 100%; box-sizing: border-box;'>{file_name}</div>
                </div>
                <div class='image-section' style='flex: 0 0 110px !important; height: 110px !important; min-height: 110px !important; max-height: 110px !important; padding: 6px !important; background: #f8f9fa !important; border-radius: 4px !important; border: 1px solid #e9ecef !important; overflow: hidden !important; box-sizing: border-box !important;'>
                    <b>原始图片：</b><br>
                    {image_html}
                </div>
                <div class='table-section' style='flex: 1 1 auto !important; padding: 6px !important; background: #f8f9fa !important; border-radius: 4px !important; border: 1px solid #e9ecef !important; overflow: hidden !important; box-sizing: border-box !important;'>
                    <b>标注表格：</b><br>
                    <div class='gen-table-html' style='height: 100% !important; width: 100% !important; min-height: 100% !important; max-height: 100% !important; display: flex !important; flex-direction: column !important; overflow: auto !important; border: 1px solid #dee2e6 !important; border-radius: 3px !important; background: #fff !important; padding: 4px !important; box-sizing: border-box !important; margin: 0 !important;'>{gen_html}</div>
                </div>
            </div>
        """

        # 准备KDC的原始文本和渲染结果
        kdc_raw_text = ""
        kdc_rendered = ""
        if markdown_content:
            # 将markdown内容进行转义显示
            escaped_markdown = markdown_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
            kdc_raw_text = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;'>{escaped_markdown}</pre>"
            
            # 直接渲染KDC markdown为HTML表格
            try:
                # 使用服务器端markdown渲染
                import markdown
                md = markdown.Markdown(extensions=['tables', 'extra'])
                rendered_html = md.convert(markdown_content)
                kdc_rendered = f"<div class='rendered-markdown'>{rendered_html}</div>"
            except ImportError:
                # 如果没有markdown库，使用简单的表格解析
                table_html = convert_markdown_table_to_html(markdown_content)
                kdc_rendered = f"<div class='rendered-markdown'>{table_html}</div>"
        else:
            kdc_raw_text = "No KDC result available"
            kdc_rendered = "No KDC result available"

        # 准备KDC Plain的原始文本和渲染结果
        kdc_plain_raw_text = ""
        kdc_plain_rendered = ""
        if kdc_plain_content:
            escaped_plain = kdc_plain_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
            kdc_plain_raw_text = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;'>{escaped_plain}</pre>"
            
            # 对于plain格式，直接显示文本，不使用markdown渲染
            kdc_plain_rendered = f"<div class='rendered-markdown'><pre style='white-space:pre-wrap;word-wrap:break-word;padding:10px;background:#f9f9f9;border:1px solid #ddd;border-radius:4px;'>{escaped_plain}</pre></div>"
        else:
            kdc_plain_raw_text = "No KDC Plain result available"
            kdc_plain_rendered = "No KDC Plain result available"

        # 准备KDC KDC的原始文本和渲染结果
        kdc_kdc_raw_text = ""
        kdc_kdc_rendered = ""
        if kdc_kdc_doc_data:
            # 原始文本显示完整的JSON数据，添加滚动条
            formatted_json = json.dumps(kdc_kdc_doc_data, ensure_ascii=False, indent=2)
            escaped_json = formatted_json.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
            kdc_kdc_raw_text = f"""<div style='max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa;'>
                <pre style='white-space:pre-wrap;word-wrap:break-word;margin:0;padding:10px;font-size:11px;'>{escaped_json}</pre>
            </div>"""
            
            # 渲染结果显示解析后的HTML表格
            try:
                # 重新解析doc数据来生成表格
                data = kdc_kdc_result.get("data", [])
                if isinstance(data, list) and len(data) > 0:
                    doc_data = data[0].get("doc", {})
                elif isinstance(data, dict):
                    doc_data = data.get("doc", {})
                else:
                    doc_data = {}
                
                if doc_data:
                    rendered_canvas = render_kdc_doc_to_canvas(doc_data)
                    kdc_kdc_rendered = f"<div class='rendered-markdown'>{rendered_canvas}</div>"
                else:
                    kdc_kdc_rendered = "No KDC document data to render"
            except Exception as e:
                kdc_kdc_rendered = f"Error rendering KDC table: {str(e)}"
        else:
            kdc_kdc_raw_text = "No KDC KDC result available"
            kdc_kdc_rendered = "No KDC KDC result available"

        # 准备MonkeyOCR的原始文本和渲染结果
        monkey_raw_text = ""
        monkey_rendered = ""
        if monkey_result and isinstance(monkey_result, dict):
            raw_html_text = monkey_result.get('html', '无')
            
            # 检查是否超时或失败
            if monkey_result.get('is_timeout'):
                processing_time = monkey_result.get('processing_time', 0)
                timeout_msg = f"MonkeyOCR处理超时 ({processing_time:.1f}秒)"
                escaped_html = timeout_msg.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                monkey_raw_text = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;color:red;'>{escaped_html}</pre>"
                monkey_rendered = f"<div class='monkey-ocr-result' style='background-color:#ffe6e6;border-color:#ff9999;color:red;'>{timeout_msg}</div>"
            else:
                escaped_html = raw_html_text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                monkey_raw_text = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;'>{escaped_html}</pre>"
                # 使用iframe隔离MonkeyOCR的HTML内容，防止影响外层table结构
                safe_html = raw_html_text.replace('"', '&quot;').replace("'", "&#x27;")
                monkey_rendered = f"""<div class='monkey-ocr-result'>
                    <iframe srcdoc="{safe_html}" 
                            style="width:100%;height:280px;border:1px solid #ddd;border-radius:3px;"
                            sandbox=""></iframe>
                </div>"""
        else:
            monkey_raw_text = "No MonkeyOCR result available"
            monkey_rendered = "No MonkeyOCR result available"

        # 准备MonkeyOCR2的原始文本和渲染结果
        monkey_raw_text_v2 = ""
        monkey_rendered_v2 = ""
        if monkey_result_v2 and isinstance(monkey_result_v2, dict):
            raw_html_text_v2 = monkey_result_v2.get('html', '无')
            
            # 检查是否超时或失败
            if monkey_result_v2.get('is_timeout'):
                processing_time = monkey_result_v2.get('processing_time', 0)
                timeout_msg = f"MonkeyOCR处理超时 ({processing_time:.1f}秒)"
                escaped_html_v2 = timeout_msg.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                monkey_raw_text_v2 = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;color:red;'>{escaped_html_v2}</pre>"
                monkey_rendered_v2 = f"<div class='monkey-ocr-result' style='background-color:#ffe6e6;border-color:#ff9999;color:red;'>{timeout_msg}</div>"
            else:
                escaped_html_v2 = raw_html_text_v2.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                monkey_raw_text_v2 = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;'>{escaped_html_v2}</pre>"
                # 使用iframe隔离MonkeyOCR2的HTML内容，防止影响外层table结构
                safe_html_v2 = raw_html_text_v2.replace('"', '&quot;').replace("'", "&#x27;")
                monkey_rendered_v2 = f"""<div class='monkey-ocr-result'>
                    <iframe srcdoc="{safe_html_v2}" 
                            style="width:100%;height:280px;border:1px solid #ddd;border-radius:3px;"
                            sandbox=""></iframe>
                </div>"""
        else:
            monkey_raw_text_v2 = "No MonkeyOCR2 result available"
            monkey_rendered_v2 = "No MonkeyOCR2 result available"

        # 准备VL LLM的原始文本和渲染结果
        vl_llm_raw_text = ""
        vl_llm_rendered = ""
        vl_llm_report = "N/A"
        
        if vl_llm_result and isinstance(vl_llm_result, dict):
            # 提取LLM结果中的markdown内容
            result_data = vl_llm_result.get('result', {})
            if isinstance(result_data, dict):
                choices = result_data.get('choices', [])
                if choices and len(choices) > 0:
                    message = choices[0].get('message', {})
                    content = message.get('content', '')
                    if content:
                        # 清理markdown内容：移除代码块标记
                        cleaned_content = content
                        if content.startswith('```markdown'):
                            cleaned_content = content.replace('```markdown', '').replace('```', '').strip()
                        elif content.startswith('```'):
                            cleaned_content = content.replace('```', '').strip()
                        
                        # 将markdown内容进行转义显示
                        escaped_content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                        vl_llm_raw_text = f"<pre style='white-space:pre-wrap;word-wrap:break-word;margin:2px 0;'>{escaped_content}</pre>"
                        
                        # 直接渲染markdown为HTML表格
                        try:
                            # 使用服务器端markdown渲染，避免依赖客户端JavaScript
                            import markdown
                            md = markdown.Markdown(extensions=['tables', 'extra'])
                            rendered_html = md.convert(cleaned_content)
                            vl_llm_rendered = f"<div class='vl-llm-result'>{rendered_html}</div>"
                        except ImportError:
                            # 如果没有markdown库，使用简单的表格解析
                            table_html = convert_markdown_table_to_html(cleaned_content)
                            vl_llm_rendered = f"<div class='vl-llm-result'>{table_html}</div>"
        
        # 生成VL LLM报告
        if vl_llm_result:
            if vl_llm_acc is not None:
                table_status = "✓ 是" if vl_llm_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                vl_llm_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>{vl_llm_acc:.2%}"
            else:
                table_status = "✓ 是" if vl_llm_has_table else "<span style='color: red; font-weight: bold;'>✗ 否</span>"
                vl_llm_report = f"<b>是否解析表格：</b>{table_status}<br><b>准确率：</b>-"
            if vl_llm_missed:
                vl_llm_report += ("<br><b>未命中单元格：</b>" +
                    "<div class='missed-detail-table'><table><thead><tr><th>内容</th><th>行</th><th>列</th></tr></thead><tbody>" +
                    ''.join([f"<tr><td>{cell}</td><td>{row+1}</td><td>{col+1}</td></tr>" for cell, row, col in vl_llm_missed]) +
                    "</tbody></table></div>")
        else:
            vl_llm_report = "<b>是否解析表格：</b><span style='color: red; font-weight: bold;'>✗ 否（无结果）</span><br><b>准确率：</b>-"
        
        if not vl_llm_raw_text:
            vl_llm_raw_text = "No VL LLM result available"
            vl_llm_rendered = "No VL LLM result available"
        
        # 总是显示6行，包括新的两路KDC解析策略
        total_rows = 6
        
        # 生成带边框的用例组：MonkeyOCR（table prompt）、MonkeyOCR（parse）、KDC Plain、KDC KDC、KDC Markdown、VL LLM
        rows_html += f"""
            <tr class='case-first-row'>
                <td rowspan="{total_rows}"><div class='cell-content'>{i}</div></td>
                <td rowspan="{total_rows}" style='padding: 0 !important; vertical-align: top !important; height: 1200px !important; max-height: 1200px !important;'><div class='cell-content' style='height: 100% !important; width: 100% !important; max-height: 1200px !important; display: flex !important; flex-direction: column !important; padding: 8px !important; box-sizing: border-box !important; margin: 0 !important; overflow: hidden !important;'>{data_info_html}</div></td>
                <td><div class='cell-content'><b>MonkeyOCR（table prompt）:</b><br>{monkey_raw_text}</div></td>
                <td><div class='cell-content'>{monkey_rendered}</div></td>
                <td><div class='cell-content'>{monkey_report}</div></td>
            </tr>
            <tr class='case-middle-row'>
                <td><div class='cell-content'><b>MonkeyOCR（parse）:</b><br>{monkey_raw_text_v2}</div></td>
                <td><div class='cell-content'>{monkey_rendered_v2}</div></td>
                <td><div class='cell-content'>{monkey_report_v2}</div></td>
            </tr>
            <tr class='case-middle-row'>
                <td><div class='cell-content'><b>KDC Plain:</b><br>{kdc_plain_raw_text}</div></td>
                <td><div class='cell-content'>{kdc_plain_rendered}</div></td>
                <td><div class='cell-content'>{kdc_plain_report}</div></td>
            </tr>
            <tr class='case-middle-row'>
                <td><div class='cell-content'><b>KDC KDC:</b><br>{kdc_kdc_raw_text}</div></td>
                <td><div class='cell-content'>{kdc_kdc_rendered}</div></td>
                <td><div class='cell-content'>{kdc_kdc_report}</div></td>
            </tr>
            <tr class='case-middle-row'>
                <td><div class='cell-content'><b>KDC Markdown:</b><br>{kdc_raw_text}</div></td>
                <td><div class='cell-content'>{kdc_rendered}</div></td>
                <td><div class='cell-content'>{kdc_report}</div></td>
            </tr>
            <tr class='case-last-row'>
                <td><div class='cell-content'><b>VL LLM解析:</b><br>{vl_llm_raw_text}</div></td>
                <td><div class='cell-content'>{vl_llm_rendered}</div></td>
                <td><div class='cell-content'>{vl_llm_report}</div></td>
            </tr>
        """

    # 计算平均准确率和表格检测率
    total_samples = len(uploaded_files)
    
    # 计算表格检测率
    kdc_detection_count = sum(kdc_detected)
    kdc_plain_detection_count = sum(kdc_plain_detected)
    kdc_kdc_detection_count = sum(kdc_kdc_detected)
    monkey_detection_count = sum(monkey_detected)
    monkey_detection_count_v2 = sum(monkey_detected_v2)
    vl_llm_detection_count = sum(vl_llm_detected)
    
    kdc_detection_rate = f"{kdc_detection_count/total_samples:.1%}（{kdc_detection_count}/{total_samples}）"
    kdc_plain_detection_rate = f"{kdc_plain_detection_count/total_samples:.1%}（{kdc_plain_detection_count}/{total_samples}）"
    kdc_kdc_detection_rate = f"{kdc_kdc_detection_count/total_samples:.1%}（{kdc_kdc_detection_count}/{total_samples}）"
    monkey_detection_rate = f"{monkey_detection_count/total_samples:.1%}（{monkey_detection_count}/{total_samples}）"
    monkey_detection_rate_v2 = f"{monkey_detection_count_v2/total_samples:.1%}（{monkey_detection_count_v2}/{total_samples}）"
    vl_llm_detection_rate = f"{vl_llm_detection_count/total_samples:.1%}（{vl_llm_detection_count}/{total_samples}）"
    
    # 计算平均准确率（只对有标注表格且检测到表格的case计算）
    kdc_acc_count = len(kdc_accuracies)
    kdc_plain_acc_count = len(kdc_plain_accuracies)
    kdc_kdc_acc_count = len(kdc_kdc_accuracies)
    monkey_acc_count = len(monkey_accuracies)
    monkey_acc_count_v2 = len(monkey_accuracies_v2)
    vl_llm_acc_count = len(vl_llm_accuracies)
    
    avg_kdc_acc = sum(kdc_accuracies) / kdc_acc_count if kdc_accuracies else 0
    avg_kdc_plain_acc = sum(kdc_plain_accuracies) / kdc_plain_acc_count if kdc_plain_accuracies else 0
    avg_kdc_kdc_acc = sum(kdc_kdc_accuracies) / kdc_kdc_acc_count if kdc_kdc_accuracies else 0
    avg_monkey_acc = sum(monkey_accuracies) / monkey_acc_count if monkey_accuracies else 0
    avg_monkey_acc_v2 = sum(monkey_accuracies_v2) / monkey_acc_count_v2 if monkey_accuracies_v2 else 0
    avg_vl_llm_acc = sum(vl_llm_accuracies) / vl_llm_acc_count if vl_llm_accuracies else 0
    
    # 在表格开头插入汇总行
    summary_row = f"""
        <tr class=\"summary-row\">
            <td><div class='cell-content'>汇总</div></td>
            <td><div class='cell-content'><b>总样本数：</b>{total_samples}个</div></td>
            <td colspan=\"2\"><div class='cell-content'>-</div></td>
            <td><div class='cell-content'>
                <div style='margin-bottom:8px;padding-bottom:8px;'>
                    <b>MonkeyOCR（table prompt）:</b><br>
                    <b>表格检测率：</b>{monkey_detection_rate}<br>
                    <b>平均准确率：</b>{avg_monkey_acc:.2%}（{monkey_acc_count}样本）
                </div>
                <div style='border-top:2px solid #ddd;padding-top:8px;margin-bottom:8px;padding-bottom:8px;'>
                    <b>MonkeyOCR（parse）:</b><br>
                    <b>表格检测率：</b>{monkey_detection_rate_v2}<br>
                    <b>平均准确率：</b>{avg_monkey_acc_v2:.2%}（{monkey_acc_count_v2}样本）
                </div>
                <div style='border-top:2px solid #ddd;padding-top:8px;margin-bottom:8px;padding-bottom:8px;'>
                    <b>KDC Plain:</b><br>
                    <b>表格检测率：</b>{kdc_plain_detection_rate}<br>
                    <b>平均准确率：</b>{avg_kdc_plain_acc:.2%}（{kdc_plain_acc_count}样本）
                </div>
                <div style='border-top:2px solid #ddd;padding-top:8px;margin-bottom:8px;padding-bottom:8px;'>
                    <b>KDC KDC:</b><br>
                    <b>表格检测率：</b>{kdc_kdc_detection_rate}<br>
                    <b>平均准确率：</b>{avg_kdc_kdc_acc:.2%}（{kdc_kdc_acc_count}样本）
                </div>
                <div style='border-top:2px solid #ddd;padding-top:8px;margin-bottom:8px;padding-bottom:8px;'>
                    <b>KDC Markdown:</b><br>
                    <b>表格检测率：</b>{kdc_detection_rate}<br>
                    <b>平均准确率：</b>{avg_kdc_acc:.2%}（{kdc_acc_count}样本）
                </div>
                <div style='border-top:2px solid #ddd;padding-top:8px;'>
                    <b>VL LLM:</b><br>
                    <b>表格检测率：</b>{vl_llm_detection_rate}<br>
                    <b>平均准确率：</b>{avg_vl_llm_acc:.2%}（{vl_llm_acc_count}样本）
                </div>
            </div></td>
        </tr>
    """
    
    # 将所有行添加到HTML中
    html += summary_row + rows_html

    # 添加JavaScript代码
    html += """
            </tbody>
        </table>

        <script>
            // 获取模态框元素
            var modal = document.getElementById("imageModal");
            var modalImg = document.getElementById("modalImage");
            var closeBtn = document.getElementsByClassName("close")[0];

            // 打开模态框
            function openModal(imgSrc) {
                modal.style.display = "block";
                modalImg.src = imgSrc;
            }

            // 点击关闭按钮关闭模态框
            closeBtn.onclick = function() {
                modal.style.display = "none";
            }

            // 点击模态框背景关闭模态框
            modal.onclick = function(event) {
                if (event.target === modal) {
                    modal.style.display = "none";
                }
            }

            // 按ESC键关闭模态框
            document.addEventListener('keydown', function(event) {
                if (event.key === "Escape") {
                    modal.style.display = "none";
                }
            });

            // 渲染所有gen-table-html中的表格样式
            document.addEventListener('DOMContentLoaded', function() {
                const genContainers = document.querySelectorAll('.gen-table-html');
                genContainers.forEach(container => {
                    // 强制设置容器样式
                    container.style.height = '100%';
                    container.style.minHeight = '100%';
                    container.style.display = 'flex';
                    container.style.flexDirection = 'column';
                    container.style.overflow = 'auto';
                    
                    const tables = container.querySelectorAll('table');
                    tables.forEach(table => {
                        table.style.borderCollapse = 'collapse';
                        table.style.width = '100%';
                        table.style.height = '100%';
                        table.style.tableLayout = 'fixed';
                        table.style.flex = '1 1 auto';
                        
                        const rows = table.querySelectorAll('tr');
                        const rowCount = rows.length;
                        
                        if (rowCount > 0) {
                            // 计算每行应该占用的高度 - 基于800px总高度的分配
                            const containerHeight = container.getBoundingClientRect().height;
                            // 800px总高度 - 文件名60px - 图片110px - padding等 ≈ 610px可用于表格
                            const availableHeight = Math.max(500, Math.min(610, containerHeight - 40));
                            const rowHeight = Math.max(30, Math.floor(availableHeight / rowCount));
                            
                            // 设置每行的高度
                            rows.forEach((row, index) => {
                                row.style.height = rowHeight + 'px';
                                row.style.minHeight = rowHeight + 'px';
                                
                                // 设置单元格样式
                                row.querySelectorAll('td, th').forEach(cell => {
                                    cell.style.border = '1px solid #ddd';
                                    cell.style.padding = '6px';
                                    cell.style.verticalAlign = 'top';
                                    cell.style.height = (rowHeight - 2) + 'px'; // 减去border
                                    cell.style.minHeight = (rowHeight - 2) + 'px';
                                    cell.style.boxSizing = 'border-box';
                                });
                            });
                        }
                    });
                    
                    // 如果没有表格，添加占位内容
                    if (tables.length === 0) {
                        container.style.display = 'flex';
                        container.style.alignItems = 'center';
                        container.style.justifyContent = 'center';
                    }
                });
            });
        </script>
    </body>
    </html>
    """
    return html

def markdown_table_to_matrix(markdown_text):
    """将markdown表格转换为二维列表"""
    try:
        lines = markdown_text.strip().split('\n')
        matrix = []
        
        for line in lines:
            line = line.strip()
            # 跳过分隔行（如 | --- | --- |）
            if not line or line.replace('|', '').replace('-', '').replace(' ', '') == '':
                continue
            
            # 解析表格行
            if line.startswith('|') and line.endswith('|'):
                # 移除首尾的|，然后按|分割
                cells = line[1:-1].split('|')
                processed_cells = []
                for cell in cells:
                    processed_cell = cell.strip().replace('\xa0', '').replace('\n', '').replace('\r', '').replace(' ', '').lower()
                    processed_cells.append(processed_cell)
                if processed_cells:  # 只添加非空行
                    matrix.append(processed_cells)
        
        return matrix
    except Exception as e:
        return []

def html_table_to_matrix(html):
    try:
        # 首先尝试解析HTML表格
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table')
        if table:
            matrix = []
            for row in table.find_all('tr'):
                # 只取<td>，忽略<th>
                cells = row.find_all('td')
                if not cells:
                    continue
                matrix.append([
                    cell.get_text(separator='', strip=True)
                    .replace('\xa0', '')
                    .replace('\n', '')
                    .replace('\r', '')
                    .replace(' ', '')
                    .lower()
                    for cell in cells
                ])
            return matrix
        else:
            # 如果没有找到HTML表格，尝试解析markdown表格
            return markdown_table_to_matrix(html)
    except Exception as e:
        return []

def calc_acc(gt, pred):
    # 全表最大匹配
    gt_flat = [cell for row in gt for cell in row]
    pred_flat = [cell for row in pred for cell in row]
    if not gt_flat or not pred_flat:
        return 0.0
    gt_counter = Counter(gt_flat)
    pred_counter = Counter(pred_flat)
    hit = sum((gt_counter & pred_counter).values())
    total = len(gt_flat)
    return hit / total if total else 0.0

def diff_cells(gt, pred):
    # 返回未命中的单元格详情（内容、行、列）
    gt_map = {}
    for i, row in enumerate(gt):
        for j, cell in enumerate(row):
            gt_map.setdefault(cell, []).append((i, j))
    pred_flat = [cell for row in pred for cell in row]
    gt_flat = [cell for row in gt for cell in row]
    gt_counter = Counter(gt_flat)
    pred_counter = Counter(pred_flat)
    # 命中内容
    hit_counter = gt_counter & pred_counter
    missed = []
    for cell, count in (gt_counter - hit_counter).items():
        for _ in range(count):
            pos = gt_map[cell].pop(0) if gt_map[cell] else (-1, -1)
            missed.append((cell, pos[0], pos[1]))
    return missed

def parse_plain_to_matrix(plain_text):
    """将plain格式的纯文本解析为表格矩阵"""
    try:
        if not plain_text or not plain_text.strip():
            return []
        
        lines = plain_text.strip().split('\n')
        # 过滤空行
        lines = [line.strip() for line in lines if line.strip()]
        
        if not lines:
            return []
        
        # 对于plain格式，尝试检测表格结构
        # 简单策略：将连续的非标题行组织成表格
        matrix = []
        current_row = []
        
        for i, line in enumerate(lines):
            # 如果是标题行（通常较短，且不包含数字）
            if len(line) < 20 and not any(char.isdigit() for char in line):
                # 如果有当前行数据，保存它
                if current_row:
                    matrix.append(current_row)
                    current_row = []
                # 开始新的列（标题）
                current_row = [line.replace('\xa0', '').replace('\n', '').replace('\r', '').replace(' ', '').lower()]
            else:
                # 数据行
                if current_row:
                    current_row.append(line.replace('\xa0', '').replace('\n', '').replace('\r', '').replace(' ', '').lower())
                else:
                    # 如果没有当前行，创建新行
                    current_row = [line.replace('\xa0', '').replace('\n', '').replace('\r', '').replace(' ', '').lower()]
        
        # 保存最后一行
        if current_row:
            matrix.append(current_row)
        
        # 转置矩阵以获得正确的行列结构
        if matrix:
            max_cols = max(len(row) for row in matrix)
            # 补齐行
            for row in matrix:
                while len(row) < max_cols:
                    row.append('')
            
            # 转置
            transposed = []
            for col_idx in range(max_cols):
                row = []
                for row_idx in range(len(matrix)):
                    if col_idx < len(matrix[row_idx]):
                        row.append(matrix[row_idx][col_idx])
                    else:
                        row.append('')
                if any(cell for cell in row):  # 只保留非空行
                    transposed.append(row)
            return transposed
        
        return matrix
    except Exception as e:
        return []

def extract_text_from_kdc_doc(doc_data):
    """从KDC文档结构中提取文本内容"""
    try:
        if not doc_data or not isinstance(doc_data, dict):
            return ""
        
        def extract_text_from_block(block):
            """递归提取块中的文本"""
            texts = []
            
            if isinstance(block, dict):
                # 处理blocks字段（包含textbox的地方）
                if 'blocks' in block and isinstance(block['blocks'], list):
                    for sub_block in block['blocks']:
                        if isinstance(sub_block, dict) and 'textbox' in sub_block:
                            textbox = sub_block['textbox']
                            if isinstance(textbox, dict) and 'blocks' in textbox:
                                for tb_block in textbox['blocks']:
                                    if isinstance(tb_block, dict) and 'para' in tb_block:
                                        para = tb_block['para']
                                        if isinstance(para, dict) and 'runs' in para:
                                            for run in para['runs']:
                                                if isinstance(run, dict) and 'text' in run:
                                                    texts.append(run['text'])
                
                # 提取直接的textbox中的文本（备用路径）
                if 'textbox' in block:
                    textbox = block['textbox']
                    if isinstance(textbox, dict) and 'blocks' in textbox:
                        for sub_block in textbox['blocks']:
                            if isinstance(sub_block, dict) and 'para' in sub_block:
                                para = sub_block['para']
                                if isinstance(para, dict) and 'runs' in para:
                                    for run in para['runs']:
                                        if isinstance(run, dict) and 'text' in run:
                                            texts.append(run['text'])
                
                # 递归处理子块
                if 'children' in block and isinstance(block['children'], list):
                    for child in block['children']:
                        texts.extend(extract_text_from_block(child))
            
            return texts
        
        # 提取所有文本
        all_texts = []
        tree = doc_data.get('tree', {})
        if isinstance(tree, dict) and 'children' in tree:
            for child in tree['children']:
                all_texts.extend(extract_text_from_block(child))
        
        return '\n'.join(all_texts)
    except Exception as e:
        return f"Error extracting text: {str(e)}"

def parse_kdc_doc_to_matrix(doc_data):
    """从KDC文档结构中解析表格矩阵"""
    try:
        if not doc_data or not isinstance(doc_data, dict):
            return []
        
        # 收集所有文本块及其位置信息
        text_blocks = []
        
        def collect_text_blocks(block, level=0):
            """递归收集文本块"""
            if isinstance(block, dict):
                # 处理blocks字段（包含textbox、table等类型）
                if 'blocks' in block and isinstance(block['blocks'], list):
                    for sub_block in block['blocks']:
                        if isinstance(sub_block, dict):
                            # 处理textbox类型的block
                            if 'textbox' in sub_block and 'bounding_box' in sub_block:
                                bbox = sub_block['bounding_box']
                                textbox = sub_block['textbox']
                                if isinstance(textbox, dict) and 'blocks' in textbox:
                                    for tb_block in textbox['blocks']:
                                        if isinstance(tb_block, dict) and 'para' in tb_block:
                                            para = tb_block['para']
                                            if isinstance(para, dict) and 'runs' in para:
                                                text_content = ""
                                                for run in para['runs']:
                                                    if isinstance(run, dict) and 'text' in run:
                                                        text_content += run['text']
                                                if text_content.strip():
                                                    text_blocks.append({
                                                        'text': text_content.strip(),
                                                        'x1': bbox.get('x1', 0),
                                                        'y1': bbox.get('y1', 0),
                                                        'x2': bbox.get('x2', 0),
                                                        'y2': bbox.get('y2', 0),
                                                        'level': level
                                                    })
                            
                            # 处理table类型的block
                            elif 'table' in sub_block:
                                table = sub_block['table']
                                if isinstance(table, dict) and 'rows' in table:
                                    for row in table['rows']:
                                        if isinstance(row, dict) and 'cells' in row:
                                            for cell in row['cells']:
                                                if isinstance(cell, dict) and 'blocks' in cell:
                                                    # 递归处理cell中的blocks
                                                    for cell_block in cell['blocks']:
                                                        collect_text_blocks(cell_block, level + 2)
                            
                            # 处理component类型的block（如图片）
                            elif 'component' in sub_block:
                                # 图片等组件暂时跳过，不处理文本
                                pass
                            
                            # 递归处理其他可能的嵌套结构
                            else:
                                collect_text_blocks(sub_block, level + 1)
                
                # 处理直接的textbox（备用路径）
                if 'textbox' in block and 'bounding_box' in block:
                    bbox = block['bounding_box']
                    textbox = block['textbox']
                    if isinstance(textbox, dict) and 'blocks' in textbox:
                        for sub_block in textbox['blocks']:
                            if isinstance(sub_block, dict) and 'para' in sub_block:
                                para = sub_block['para']
                                if isinstance(para, dict) and 'runs' in para:
                                    text_content = ""
                                    for run in para['runs']:
                                        if isinstance(run, dict) and 'text' in run:
                                            text_content += run['text']
                                    if text_content.strip():
                                        text_blocks.append({
                                            'text': text_content.strip(),
                                            'x1': bbox.get('x1', 0),
                                            'y1': bbox.get('y1', 0),
                                            'x2': bbox.get('x2', 0),
                                            'y2': bbox.get('y2', 0),
                                            'level': level
                                        })
                
                # 递归处理子块
                if 'children' in block and isinstance(block['children'], list):
                    for child in block['children']:
                        collect_text_blocks(child, level + 1)
        
        # 收集所有文本块
        tree = doc_data.get('tree', {})
        if isinstance(tree, dict) and 'children' in tree:
            for child in tree['children']:
                collect_text_blocks(child, level=0)
        
        if not text_blocks:
            return []
        
        # 按位置排序：先按Y坐标（行），再按X坐标（列）
        text_blocks.sort(key=lambda x: (x['y1'], x['x1']))
        
        # 尝试组织成表格结构
        matrix = []
        current_row = []
        current_y = None
        tolerance = 100  # Y坐标容差
        
        for block in text_blocks:
            text = block['text'].replace('\xa0', '').replace('\n', '').replace('\r', '').replace(' ', '').lower()
            if not text:
                continue
                
            # 如果Y坐标差异较大，开始新行
            if current_y is None or abs(block['y1'] - current_y) > tolerance:
                if current_row:
                    matrix.append(current_row)
                current_row = [text]
                current_y = block['y1']
            else:
                current_row.append(text)
        
        # 添加最后一行
        if current_row:
            matrix.append(current_row)
        
        # 只保留非空矩阵
        matrix = [row for row in matrix if any(cell for cell in row)]
        
        return matrix
    except Exception as e:
        return []

def render_kdc_doc_to_canvas(doc_data):
    """KDC Canvas渲染函数已删除 - 现在使用analyzer前端组件来处理KDC渲染"""
    return "KDC rendering moved to analyzer frontend component"

def render_kdc_doc_to_html_table(doc_data):
    """从KDC文档结构中渲染HTML表格（简化版，主要用于备用）"""
    try:
        if not doc_data or not isinstance(doc_data, dict):
            return "No KDC document data"
        
        # 收集所有文本块及其位置信息（保留原始文本，不转小写）
        text_blocks = []
        
        def collect_text_blocks_for_render(block, level=0):
            """递归收集文本块用于渲染"""
            if isinstance(block, dict):
                # 处理blocks字段（包含textbox、table等类型）
                if 'blocks' in block and isinstance(block['blocks'], list):
                    for sub_block in block['blocks']:
                        if isinstance(sub_block, dict):
                            # 处理textbox类型的block
                            if 'textbox' in sub_block and 'bounding_box' in sub_block:
                                bbox = sub_block['bounding_box']
                                textbox = sub_block['textbox']
                                if isinstance(textbox, dict) and 'blocks' in textbox:
                                    for tb_block in textbox['blocks']:
                                        if isinstance(tb_block, dict) and 'para' in tb_block:
                                            para = tb_block['para']
                                            if isinstance(para, dict) and 'runs' in para:
                                                text_content = ""
                                                for run in para['runs']:
                                                    if isinstance(run, dict) and 'text' in run:
                                                        text_content += run['text']
                                                if text_content.strip():
                                                    text_blocks.append({
                                                        'text': text_content.strip(),
                                                        'x1': bbox.get('x1', 0),
                                                        'y1': bbox.get('y1', 0),
                                                        'x2': bbox.get('x2', 0),
                                                        'y2': bbox.get('y2', 0),
                                                        'level': level
                                                    })
                            
                            # 处理table类型的block
                            elif 'table' in sub_block:
                                table = sub_block['table']
                                if isinstance(table, dict) and 'rows' in table:
                                    for row in table['rows']:
                                        if isinstance(row, dict) and 'cells' in row:
                                            for cell in row['cells']:
                                                if isinstance(cell, dict) and 'blocks' in cell:
                                                    # 递归处理cell中的blocks
                                                    for cell_block in cell['blocks']:
                                                        collect_text_blocks_for_render(cell_block, level + 2)
                            
                            # 处理component类型的block（如图片）
                            elif 'component' in sub_block:
                                # 图片等组件暂时跳过，不处理文本
                                pass
                            
                            # 递归处理其他可能的嵌套结构
                            else:
                                collect_text_blocks_for_render(sub_block, level + 1)
                
                # 处理直接的textbox（备用路径）
                if 'textbox' in block and 'bounding_box' in block:
                    bbox = block['bounding_box']
                    textbox = block['textbox']
                    if isinstance(textbox, dict) and 'blocks' in textbox:
                        for sub_block in textbox['blocks']:
                            if isinstance(sub_block, dict) and 'para' in sub_block:
                                para = sub_block['para']
                                if isinstance(para, dict) and 'runs' in para:
                                    text_content = ""
                                    for run in para['runs']:
                                        if isinstance(run, dict) and 'text' in run:
                                            text_content += run['text']
                                    if text_content.strip():
                                        text_blocks.append({
                                            'text': text_content.strip(),
                                            'x1': bbox.get('x1', 0),
                                            'y1': bbox.get('y1', 0),
                                            'x2': bbox.get('x2', 0),
                                            'y2': bbox.get('y2', 0),
                                            'level': level
                                        })
                
                # 递归处理子块
                if 'children' in block and isinstance(block['children'], list):
                    for child in block['children']:
                        collect_text_blocks_for_render(child, level + 1)
        
        # 收集所有文本块
        tree = doc_data.get('tree', {})
        if isinstance(tree, dict) and 'children' in tree:
            for child in tree['children']:
                collect_text_blocks_for_render(child, level=0)
        
        if not text_blocks:
            return "No text blocks found in KDC document"
        
        # 按位置排序：先按Y坐标（行），再按X坐标（列）
        text_blocks.sort(key=lambda x: (x['y1'], x['x1']))
        
        # 组织成表格结构（保留原始文本）
        table_rows = []
        current_row = []
        current_y = None
        tolerance = 100  # Y坐标容差
        
        for block in text_blocks:
            text = block['text']
            if not text.strip():
                continue
                
            # 如果Y坐标差异较大，开始新行
            if current_y is None or abs(block['y1'] - current_y) > tolerance:
                if current_row:
                    table_rows.append(current_row)
                current_row = [text]
                current_y = block['y1']
            else:
                current_row.append(text)
        
        # 添加最后一行
        if current_row:
            table_rows.append(current_row)
        
        # 生成HTML表格
        if not table_rows:
            return "No table structure found"
        
        # 标准化行长度（以最长行为准）
        max_cols = max(len(row) for row in table_rows) if table_rows else 0
        for row in table_rows:
            while len(row) < max_cols:
                row.append("")
        
        # 生成HTML
        html = "<table border='1' style='border-collapse: collapse; width: 100%;'>"
        
        # 第一行作为表头
        if table_rows:
            html += "<thead><tr>"
            for cell in table_rows[0]:
                escaped_cell = cell.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                html += f"<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>{escaped_cell}</th>"
            html += "</tr></thead>"
        
        # 数据行
        if len(table_rows) > 1:
            html += "<tbody>"
            for row in table_rows[1:]:
                html += "<tr>"
                for cell in row:
                    escaped_cell = cell.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                    html += f"<td style='border: 1px solid #ddd; padding: 8px;'>{escaped_cell}</td>"
                html += "</tr>"
            html += "</tbody>"
        
        html += "</table>"
        return html
        
    except Exception as e:
        return f"Error rendering KDC table: {str(e)}"
