"""
文件管理器

负责处理文件上传、转换、管理等操作
"""
import os
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import logging

from clients.kdocs_client import KdocsClient

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""
    
    def __init__(self, kdocs_client: KdocsClient = None):
        self.kdocs_client = kdocs_client
        self.logger = logging.getLogger(__name__)
    
    def convert_image_to_pdf(self, image_path: str, output_dir: str) -> str:
        """
        使用ImageMagick将图片转换为PDF
        
        Args:
            image_path: 图片路径
            output_dir: 输出目录
            
        Returns:
            PDF文件路径
        """
        output_path = os.path.join(output_dir, f"{Path(image_path).stem}.pdf")
        cmd = ["convert", image_path, output_path]
        
        try:
            subprocess.run(cmd, check=True)
            self.logger.info(f"图片转换成功: {image_path} -> {output_path}")
            return output_path
        except subprocess.CalledProcessError as e:
            self.logger.error(f"图片转换失败: {e}")
            raise
    
    def get_image_files(self, images_dir: str) -> List[str]:
        """
        获取目录下的所有图片文件
        
        Args:
            images_dir: 图片目录
            
        Returns:
            图片文件名列表（已排序）
        """
        if not os.path.exists(images_dir):
            self.logger.warning(f"图片目录不存在: {images_dir}")
            return []
        
        image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp')
        image_files = [
            f for f in os.listdir(images_dir) 
            if f.lower().endswith(image_extensions)
        ]
        
        # 排序确保一致性
        image_files.sort()
        self.logger.info(f"找到 {len(image_files)} 个图片文件")
        return image_files
    
    def process_images_to_pdfs(self, images_dir: str, pdf_dir: str) -> List[Dict[str, str]]:
        """
        批量处理图片转PDF
        
        Args:
            images_dir: 图片目录
            pdf_dir: PDF输出目录
            
        Returns:
            处理结果列表，包含原始图片和PDF文件信息
        """
        os.makedirs(pdf_dir, exist_ok=True)
        
        image_files = self.get_image_files(images_dir)
        processed_files = []
        
        for image_file in image_files:
            image_path = os.path.join(images_dir, image_file)
            
            try:
                pdf_path = self.convert_image_to_pdf(image_path, pdf_dir)
                processed_files.append({
                    "original_image": image_file,
                    "image_path": image_path,
                    "pdf_path": pdf_path,
                    "pdf_filename": os.path.basename(pdf_path)
                })
                self.logger.info(f"处理完成: {image_file}")
            except Exception as e:
                self.logger.error(f"处理失败 {image_file}: {e}")
                continue
        
        self.logger.info(f"批量处理完成，成功处理 {len(processed_files)} 个文件")
        return processed_files
    
    def create_or_get_test_folder(self, group_id: str, parent_id: str, 
                                 test_name: str, company_id: str) -> Dict[str, Any]:
        """
        创建或获取测试集文件夹
        
        Args:
            group_id: 组ID
            parent_id: 父文件夹ID
            test_name: 测试集名称
            company_id: 公司ID
            
        Returns:
            文件夹信息
        """
        if not self.kdocs_client:
            raise ValueError("KDocs客户端未初始化")
        
        return self.kdocs_client.get_or_create_test_folder(
            group_id=group_id,
            parent_id=parent_id,
            test_name=test_name,
            company_id=company_id
        )
    
    def upload_files(self, files: List[Dict[str, str]], group_id: str, 
                    parent_id: str, company_id: str) -> List[Dict[str, Any]]:
        """
        批量上传文件到KDocs
        
        Args:
            files: 文件信息列表
            group_id: 组ID
            parent_id: 父文件夹ID
            company_id: 公司ID
            
        Returns:
            上传结果列表
        """
        if not self.kdocs_client:
            raise ValueError("KDocs客户端未初始化")
        
        uploaded_files = []
        
        for file_info in files:
            pdf_path = file_info["pdf_path"]
            original_image = file_info["original_image"]
            
            try:
                upload_result = self.kdocs_client.upload_file(
                    local_file_path=pdf_path,
                    group_id=group_id,
                    parent_id=parent_id,
                    company_id=company_id
                )
                
                # 检查是否因为文件名重复而跳过
                if upload_result.get("result") == "skipped":
                    self.logger.info(f"文件已存在，跳过上传: {pdf_path}")
                    uploaded_files.append({
                        "id": None,  # 文件ID将在后续从文件列表中获取
                        "fname": os.path.basename(pdf_path),
                        "original_image": original_image,
                        "skipped": True
                    })
                else:
                    self.logger.info(f"文件上传成功: {pdf_path}")
                    uploaded_files.append({
                        "id": upload_result.get("id"),
                        "fname": os.path.basename(pdf_path),
                        "original_image": original_image,
                        "skipped": False
                    })
                    
            except Exception as e:
                self.logger.error(f"文件上传失败 {pdf_path}: {e}")
                continue
        
        self.logger.info(f"批量上传完成，成功上传 {len(uploaded_files)} 个文件")
        return uploaded_files
    
    def list_folder_files(self, group_id: str, parent_id: str, 
                         company_id: str, count: int = 100) -> List[Dict[str, Any]]:
        """
        列出文件夹中的文件
        
        Args:
            group_id: 组ID
            parent_id: 文件夹ID
            company_id: 公司ID
            count: 最大文件数量
            
        Returns:
            文件列表
        """
        if not self.kdocs_client:
            raise ValueError("KDocs客户端未初始化")
        
        files = self.kdocs_client.list_files(
            group_id=group_id,
            parent_id=parent_id,
            company_id=company_id,
            count=count
        )
        
        # 按文件名排序，确保处理顺序一致
        files = sorted(files, key=lambda x: x.get("fname", ""))
        self.logger.info(f"获取到 {len(files)} 个文件")
        return files
    
    def delete_folder_files(self, files: List[Dict[str, Any]], 
                           group_id: str, company_id: str):
        """
        删除文件夹中的文件
        
        Args:
            files: 文件列表
            group_id: 组ID
            company_id: 公司ID
        """
        if not self.kdocs_client:
            raise ValueError("KDocs客户端未初始化")
        
        for file in files:
            file_id = file.get("id")
            if file_id:
                try:
                    self.kdocs_client.delete_file(
                        group_id=group_id,
                        file_id=str(file_id),
                        company_id=company_id
                    )
                    self.logger.info(f"文件删除成功: {file.get('fname', '')} (ID: {file_id})")
                except Exception as e:
                    self.logger.error(f"删除文件失败 {file_id}: {str(e)}")
                    continue
        
        self.logger.info("文件清理完成")
    
    def update_skipped_file_ids(self, uploaded_files: List[Dict[str, Any]], 
                               files: List[Dict[str, Any]]):
        """
        更新跳过的文件的ID
        
        Args:
            uploaded_files: 上传文件列表
            files: 服务器文件列表
        """
        for file in files:
            for uploaded_file in uploaded_files:
                if (uploaded_file["fname"] == file["fname"] and 
                    uploaded_file["id"] is None):
                    uploaded_file["id"] = file["id"]
                    break
        
        self.logger.info("文件ID更新完成") 