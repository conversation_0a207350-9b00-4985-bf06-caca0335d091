"""
KDC解析器
"""
from typing import Dict, Any, List, Tuple
from .base import BaseParser
from clients.kdc_client import KdcClient
import math


def calculate_kdc_features(doc_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算KDC解析结果的特征信息

    Args:
        doc_data: KDC解析结果中的doc数据

    Returns:
        特征信息字典，包含bbox数量和位置指标
    """
    features = {
        "bbox_count": 0,
        "bbox_position_metrics": {
            "avg_x": 0,
            "avg_y": 0,
            "avg_width": 0,
            "avg_height": 0,
            "total_area": 0,
            "bbox_density": 0  # bbox数量/页面面积
        },
        "bbox_types": {
            "textbox": 0,
            "table": 0,
            "component": 0,
            "table_cell": 0
        },
        "table_detection": {
            "has_table": False,  # 是否检测到表格
            "table_count": 0,    # 表格数量
            "table_areas": []    # 表格区域信息
        },
        "textbox_count_distribution": {
            "cell_textbox_counts": {},  # {textbox_count: cell_count}
            "total_cells": 0,
            "max_textboxes_per_cell": 0,
            "avg_textboxes_per_cell": 0,
            "outlier_cells": []  # 包含异常多文本框的单元格信息
        }
    }

    if not doc_data or not isinstance(doc_data, dict):
        return features

    bboxes = []
    page_width = 0
    page_height = 0

    def extract_bboxes_recursive(data, level=0):
        """递归提取所有bounding_box信息"""
        nonlocal page_width, page_height

        if isinstance(data, dict):
            # 提取页面尺寸信息
            if "page_size" in data:
                page_size = data["page_size"]
                if isinstance(page_size, dict):
                    page_width = max(page_width, page_size.get("width", 0))
                    page_height = max(page_height, page_size.get("height", 0))

            # 处理bounding_box
            if "bounding_box" in data:
                bbox = data["bounding_box"]
                if isinstance(bbox, dict) and all(k in bbox for k in ["x1", "y1", "x2", "y2"]):
                    x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
                    width = x2 - x1
                    height = y2 - y1

                    if width > 0 and height > 0:  # 只统计有效的bbox
                        bbox_info = {
                            "x1": x1, "y1": y1, "x2": x2, "y2": y2,
                            "width": width, "height": height,
                            "area": width * height,
                            "center_x": (x1 + x2) / 2,
                            "center_y": (y1 + y2) / 2
                        }

                        # 确定bbox类型
                        bbox_type = "unknown"
                        if "textbox" in data:
                            bbox_type = "textbox"
                        elif "table" in data:
                            bbox_type = "table"
                        elif "component" in data:
                            bbox_type = "component"
                        elif "col_span" in data or "row_span" in data:  # 表格单元格
                            bbox_type = "table_cell"

                        bbox_info["type"] = bbox_type
                        bboxes.append(bbox_info)

            # 递归处理所有子项
            for key, value in data.items():
                if key not in ["bounding_box"]:  # 避免重复处理
                    extract_bboxes_recursive(value, level + 1)

        elif isinstance(data, list):
            for item in data:
                extract_bboxes_recursive(item, level)

    # 开始递归提取
    extract_bboxes_recursive(doc_data)

    # 计算特征
    if bboxes:
        features["bbox_count"] = len(bboxes)

        # 计算位置指标
        total_x = sum(bbox["center_x"] for bbox in bboxes)
        total_y = sum(bbox["center_y"] for bbox in bboxes)
        total_width = sum(bbox["width"] for bbox in bboxes)
        total_height = sum(bbox["height"] for bbox in bboxes)
        total_area = sum(bbox["area"] for bbox in bboxes)

        features["bbox_position_metrics"]["avg_x"] = round(total_x / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_y"] = round(total_y / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_width"] = round(total_width / len(bboxes), 2)
        features["bbox_position_metrics"]["avg_height"] = round(total_height / len(bboxes), 2)
        features["bbox_position_metrics"]["total_area"] = total_area

        # 计算bbox密度（如果有页面尺寸信息）
        if page_width > 0 and page_height > 0:
            page_area = page_width * page_height
            features["bbox_position_metrics"]["bbox_density"] = round(len(bboxes) / page_area * 1000000, 4)  # 每百万像素的bbox数量

        # 统计各类型bbox数量
        table_bboxes = []
        for bbox in bboxes:
            bbox_type = bbox["type"]
            if bbox_type in features["bbox_types"]:
                features["bbox_types"][bbox_type] += 1

            # 收集表格类型的bbox
            if bbox_type == "table":
                table_bboxes.append(bbox)

        # 计算表格检测特征
        features["table_detection"]["table_count"] = len(table_bboxes)
        features["table_detection"]["has_table"] = len(table_bboxes) > 0

        # 记录表格区域信息
        for table_bbox in table_bboxes:
            table_area = {
                "x1": table_bbox["x1"],
                "y1": table_bbox["y1"],
                "x2": table_bbox["x2"],
                "y2": table_bbox["y2"],
                "width": table_bbox["width"],
                "height": table_bbox["height"],
                "area": table_bbox["area"]
            }
            features["table_detection"]["table_areas"].append(table_area)

    # 计算表格单元格文本框数量分布
    textbox_distribution = calculate_textbox_distribution(doc_data)
    features["textbox_count_distribution"] = textbox_distribution

    return features


def calculate_textbox_distribution(doc_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算表格单元格中文本框数量的分布统计

    Args:
        doc_data: KDC解析结果中的doc数据

    Returns:
        文本框数量分布统计信息
    """
    distribution = {
        "cell_textbox_counts": {},  # {textbox_count: cell_count}
        "total_cells": 0,
        "max_textboxes_per_cell": 0,
        "avg_textboxes_per_cell": 0,
        "outlier_cells": []  # 包含异常多文本框的单元格信息
    }

    if not doc_data or not isinstance(doc_data, dict):
        return distribution

    cell_textbox_counts = []  # 存储每个单元格的文本框数量

    def extract_table_cells_recursive(data, level=0):
        """递归提取所有表格单元格信息"""
        if isinstance(data, dict):
            # 处理表格结构
            if "table" in data:
                table = data["table"]
                if isinstance(table, dict) and "rows" in table:
                    rows = table["rows"]
                    if isinstance(rows, list):
                        for row in rows:
                            if isinstance(row, dict) and "cells" in row:
                                cells = row["cells"]
                                if isinstance(cells, list):
                                    for cell in cells:
                                        if isinstance(cell, dict):
                                            # 统计当前单元格中的文本框数量
                                            textbox_count = count_textboxes_in_cell(cell)
                                            cell_textbox_counts.append(textbox_count)

            # 递归处理所有子项
            for key, value in data.items():
                extract_table_cells_recursive(value, level + 1)

        elif isinstance(data, list):
            for item in data:
                extract_table_cells_recursive(item, level)

    def count_textboxes_in_cell(cell: Dict[str, Any]) -> int:
        """统计单元格中的文本框数量"""
        textbox_count = 0

        if "blocks" in cell and isinstance(cell["blocks"], list):
            for block in cell["blocks"]:
                if isinstance(block, dict) and "textbox" in block:
                    textbox_count += 1

        return textbox_count

    # 开始递归提取
    extract_table_cells_recursive(doc_data)

    # 计算分布统计
    if cell_textbox_counts:
        distribution["total_cells"] = len(cell_textbox_counts)
        distribution["max_textboxes_per_cell"] = max(cell_textbox_counts)
        distribution["avg_textboxes_per_cell"] = round(sum(cell_textbox_counts) / len(cell_textbox_counts), 2)

        # 统计每种文本框数量对应的单元格数量
        for count in cell_textbox_counts:
            if count in distribution["cell_textbox_counts"]:
                distribution["cell_textbox_counts"][count] += 1
            else:
                distribution["cell_textbox_counts"][count] = 1

        # 检测异常单元格（文本框数量明显高于平均值）
        avg_count = distribution["avg_textboxes_per_cell"]
        threshold = avg_count * 2  # 超过平均值2倍的认为是异常

        outlier_counts = [count for count in cell_textbox_counts if count > threshold and count > 1]
        if outlier_counts:
            distribution["outlier_cells"] = [
                {
                    "textbox_count": count,
                    "is_outlier": True,
                    "threshold": threshold
                }
                for count in set(outlier_counts)  # 去重
            ]

    return distribution


class KdcParser(BaseParser):
    """KDC解析器"""
    
    def __init__(self, ak: str, sk: str, company_id: str, 
                 default_cookie: str = None, parse_format: str = "markdown"):
        parser_name = f"kdc_{parse_format}"
        
        super().__init__(parser_name)
        self.ak = ak
        self.sk = sk
        self.company_id = company_id
        self.default_cookie = default_cookie
        self.parse_format = parse_format
        self._client = None
    
    @property
    def client(self) -> KdcClient:
        """懒加载KDC客户端"""
        if self._client is None:
            self._client = KdcClient(
                ak=self.ak,
                sk=self.sk,
                default_cookie=self.default_cookie
            )
        return self._client
    
    def is_available(self) -> bool:
        """检查KDC解析器是否可用"""
        try:
            # 这里可以添加更复杂的可用性检查
            return bool(self.ak and self.sk and self.company_id)
        except Exception:
            return False
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用KDC解析文件
        
        Args:
            file_info: 文件信息
            
        Returns:
            解析结果
        """
        file_id = file_info.get("file_id")
        filename = file_info.get("filename")
        
        if not file_id or not filename:
            raise ValueError("缺少必要的文件信息：file_id或filename")
        
        # 调用KDC API
        result = self.client.parse_document(
            company_id=self.company_id,
            file_id=str(file_id),
            filename=filename,
            format=self.parse_format if self.parse_format else None
        )
        
        return {"result": result}


class KdcPlainParser(KdcParser):
    """KDC Plain格式解析器"""
    
    def __init__(self, ak: str, sk: str, company_id: str, default_cookie: str = None):
        super().__init__(ak, sk, company_id, default_cookie, "plain")


class KdcKdcParser(KdcParser):
    """KDC KDC格式解析器"""

    def __init__(self, ak: str, sk: str, company_id: str, default_cookie: str = None):
        super().__init__(ak, sk, company_id, default_cookie, "kdc")

    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用KDC解析文件并计算特征信息

        Args:
            file_info: 文件信息

        Returns:
            解析结果，包含特征信息
        """
        # 调用父类的解析方法
        result = super().parse(file_info)

        # 计算KDC特征信息
        features = {}
        try:
            if result and "result" in result:
                kdc_result = result["result"]
                if isinstance(kdc_result, dict) and "data" in kdc_result:
                    data_array = kdc_result["data"]
                    if isinstance(data_array, list) and len(data_array) > 0:
                        doc_data = data_array[0].get("doc", {})
                        if doc_data:
                            features = calculate_kdc_features(doc_data)
                            self.logger.info(f"计算KDC特征完成: bbox数量={features.get('bbox_count', 0)}")
        except Exception as e:
            self.logger.warning(f"计算KDC特征时出错: {e}")
            features = {}

        # 将特征信息添加到结果中
        if features:
            result["features"] = features

        return result