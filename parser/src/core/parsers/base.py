"""
解析器基类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """解析器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析文件
        
        Args:
            file_info: 文件信息，包含文件路径、ID等
            
        Returns:
            解析结果字典
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        检查解析器是否可用
        
        Returns:
            是否可用
        """
        pass
    
    def pre_process(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理，子类可以重写
        
        Args:
            file_info: 文件信息
            
        Returns:
            处理后的文件信息
        """
        return file_info
    
    def post_process(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        后处理，子类can重写
        
        Args:
            result: 解析结果
            
        Returns:
            处理后的结果
        """
        return result
    
    def parse_with_error_handling(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        带错误处理的解析方法
        
        Args:
            file_info: 文件信息
            
        Returns:
            解析结果
        """
        try:
            # 预处理
            processed_file_info = self.pre_process(file_info)
            
            # 检查可用性
            if not self.is_available():
                return self._create_error_result(
                    file_info, 
                    f"解析器 {self.name} 不可用"
                )
            
            # 执行解析
            self.logger.info(f"开始解析文件: {file_info.get('filename', 'Unknown')}")
            result = self.parse(processed_file_info)
            
            # 后处理
            final_result = self.post_process(result)

            # 检查解析器是否已经设置了success状态
            # 如果解析器内部的result包含success字段且为False，则使用该状态
            parser_success = True
            if isinstance(final_result.get("result"), dict):
                inner_success = final_result["result"].get("success")
                if inner_success is False:
                    parser_success = False
            elif final_result.get("success") is False:
                parser_success = False

            # 添加解析器信息
            final_result.update({
                "parser_name": self.name,
                "success": parser_success,
                "filename": file_info.get("filename"),
                "file_id": file_info.get("file_id"),
                "original_image": file_info.get("original_image")
            })
            
            self.logger.info(f"解析完成: {file_info.get('filename', 'Unknown')}")
            return final_result
            
        except Exception as e:
            self.logger.error(f"解析失败 {file_info.get('filename', 'Unknown')}: {str(e)}")
            return self._create_error_result(file_info, str(e))
    
    def _create_error_result(self, file_info: Dict[str, Any], error_msg: str) -> Dict[str, Any]:
        """
        创建错误结果
        
        Args:
            file_info: 文件信息
            error_msg: 错误消息
            
        Returns:
            错误结果字典
        """
        return {
            "parser_name": self.name,
            "success": False,
            "error": error_msg,
            "filename": file_info.get("filename"),
            "file_id": file_info.get("file_id"),
            "original_image": file_info.get("original_image"),
            "result": None
        } 