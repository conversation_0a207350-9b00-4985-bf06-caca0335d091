"""
VL LLM解析器
"""
import os
from typing import Dict, Any
from .base import BaseParser


class VlLlmParser(BaseParser):
    """VL LLM解析器"""
    
    def __init__(self, prompt: str = None):
        super().__init__("vl_llm")
        self.prompt = prompt or "请将图片中的表格信息直接转换为标准的markdown表格格式，不要添加任何解释文字、代码块标记或其他内容，只返回纯markdown表格文本。"
        self._processor = None
        self._import_error = None
    
    @property
    def processor(self):
        """懒加载VL LLM处理器"""
        if self._processor is None and self._import_error is None:
            try:
                # 动态导入，避免循环导入
                import sys
                import os
                scripts_path = os.path.join(os.path.dirname(__file__), '..', '..', 'scripts')
                if scripts_path not in sys.path:
                    sys.path.append(scripts_path)
                
                from .test_vl_parse import ImageProcessor
                self._processor = ImageProcessor()
                self.logger.info("VL LLM处理器加载成功")
            except ImportError as e:
                self._import_error = f"无法导入VL LLM模块: {str(e)}"
                self.logger.warning(self._import_error)
            except Exception as e:
                self._import_error = f"VL LLM处理器初始化失败: {str(e)}"
                self.logger.error(self._import_error)
        
        if self._import_error:
            raise ImportError(self._import_error)
        
        return self._processor
    
    def is_available(self) -> bool:
        """检查VL LLM解析器是否可用"""
        try:
            # 尝试加载处理器以检查可用性
            _ = self.processor
            return True
        except Exception as e:
            self.logger.debug(f"VL LLM解析器不可用: {str(e)}")
            return False
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用VL LLM解析图片
        
        Args:
            file_info: 文件信息，需要包含original_image和image_path
            
        Returns:
            解析结果
        """
        original_image = file_info.get("original_image")
        image_path = file_info.get("image_path")
        
        if not original_image or not image_path:
            raise ValueError("缺少必要的图片信息：original_image或image_path")
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 检查处理器是否可用
        if not self.is_available():
            raise RuntimeError("VL LLM处理器不可用")
        
        # 调用VL LLM处理器
        result = self.processor.process_image(image_path, self.prompt)

        # 检查处理结果
        if result is None:
            # 处理失败，返回失败状态
            return {"result": {
                "success": False,
                "error": "VL LLM处理失败",
                "content": None
            }}

        # 处理成功
        return {"result": {
            "success": True,
            "error": None,
            "content": result
        }}