"""
MonkeyOCR解析器
"""
import os
import glob
import subprocess
import uuid
from typing import Dict, Any
from pathlib import Path
from .base import BaseParser
from clients.monkey_ocr_client import MonkeyOCRClient


class MonkeyOcrParser(BaseParser):
    """MonkeyOCR解析器 - HTML格式 (table模式)"""
    
    def __init__(self, prompt: str = None, fn_index: int = 4, trigger_id: int = 11):
        super().__init__("monkey_ocr")
        # 使用table模式的配置：fn_index=4，具体prompt
        self.prompt = prompt or "This is the image of a table. Please output the table in html format."
        self.fn_index = fn_index
        self.trigger_id = trigger_id
        self._client = None
    
    @property
    def client(self) -> MonkeyOCRClient:
        """懒加载MonkeyOCR客户端"""
        if self._client is None:
            self._client = MonkeyOCRClient()
        return self._client
    
    def is_available(self) -> bool:
        """检查MonkeyOCR解析器是否可用"""
        try:
            # 可以添加服务可用性检查
            return True
        except Exception:
            return False
    
    def _upload_and_register_file(self, image_path: str) -> tuple:
        """
        上传并注册文件到MonkeyOCR服务
        
        Returns:
            tuple: (是否成功, session_hash)
        """
        # 1. 上传文件
        success, uploaded_path, upload_id = self.client.upload_file(image_path)
        if not success:
            self.logger.error(f"上传文件失败: {image_path}")
            return False, None
        
        self.logger.info(f"文件上传成功: {uploaded_path}")
        
        # 2. 等待上传完成
        if not self.client.wait_upload_completion(upload_id):
            self.logger.error("等待上传完成超时")
            return False, None
        
        # 3. 注册文件到会话
        session_hash = str(uuid.uuid4())[:8]
        if not self.client.register_file_to_session(uploaded_path, image_path, session_hash):
            self.logger.error("注册文件到会话失败")
            return False, None
        
        self.logger.info(f"文件注册成功，session: {session_hash}")
        return True, session_hash
    
    def _submit_and_get_result(self, session_hash: str) -> Dict[str, Any]:
        """
        提交处理请求并获取结果
        
        Args:
            session_hash: 会话哈希
            
        Returns:
            Dict: 处理结果
        """
        # 1. 提交处理请求
        if not self.client.submit_processing_request(session_hash, self.prompt, self.fn_index, self.trigger_id):
            self.logger.error("提交处理请求失败")
            return {
                'processed_image_url': None,
                'text_content': ['MonkeyOCR处理请求提交失败'],
                'html': 'MonkeyOCR处理请求提交失败',
                'is_timeout': False,
                'processing_time': 0,
                'success': False,
                'error_type': 'request_failed'
            }
        
        # 2. 获取处理结果
        result = self.client.get_processing_result(session_hash)
        if result is None:
            self.logger.error("获取处理结果失败")
            return {
                'processed_image_url': None,
                'text_content': ['MonkeyOCR获取结果失败'],
                'html': 'MonkeyOCR获取结果失败',
                'is_timeout': False,
                'processing_time': 0
            }
        
        return result
    
    def _download_processed_image(self, result: Dict[str, Any], original_image: str) -> Dict[str, Any]:
        """
        下载处理后的图片
        
        Args:
            result: 处理结果
            original_image: 原始图片名
            
        Returns:
            Dict: 更新后的结果
        """
        if result.get('processed_image_url'):
            monkey_ocr_dir = os.getenv("MONKEY_OCR_DIR")
            if monkey_ocr_dir:
                name_without_ext = os.path.splitext(original_image)[0]
                save_path = os.path.join(monkey_ocr_dir, f"{name_without_ext}_{self.name}.webp")
                
                if self.client.download_processed_image(result['processed_image_url'], save_path):
                    result['local_image_path'] = save_path
                    self.logger.info(f"MonkeyOCR处理后的图片已保存: {save_path}")
                else:
                    self.logger.warning(f"下载MonkeyOCR处理后的图片失败: {result['processed_image_url']}")
        
        return result
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用MonkeyOCR解析图片
        
        Args:
            file_info: 文件信息，需要包含original_image和image_path
            
        Returns:
            解析结果
        """
        original_image = file_info.get("original_image")
        image_path = file_info.get("image_path")
        
        if not original_image or not image_path:
            raise ValueError("缺少必要的图片信息：original_image或image_path")
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        self.logger.info(f"开始MonkeyOCR解析: {original_image}")
        
        # 1. 上传并注册文件
        success, session_hash = self._upload_and_register_file(image_path)
        if not success:
            return {"result": {
                'processed_image_url': None,
                'text_content': ['MonkeyOCR文件上传失败'],
                'html': 'MonkeyOCR文件上传失败',
                'is_timeout': False,
                'processing_time': 0,
                'success': False,
                'error_type': 'upload_failed'
            }}
        
        # 2. 提交处理并获取结果
        result = self._submit_and_get_result(session_hash)
        
        # 3. 检查结果并设置成功状态
        if result.get('is_timeout') or 'MonkeyOCR处理超时' in result.get('html', ''):
            result['success'] = False
            result['error_type'] = 'timeout'
        elif 'MonkeyOCR处理失败' in result.get('html', '') or 'MonkeyOCR获取结果失败' in result.get('html', ''):
            result['success'] = False  
            result['error_type'] = 'processing_failed'
        elif 'MonkeyOCR处理请求提交失败' in result.get('html', ''):
            result['success'] = False
            result['error_type'] = 'request_failed'
        elif not result.get('html') or result.get('html') == 'No MonkeyOCR result available':
            result['success'] = False
            result['error_type'] = 'no_result'
        else:
            result['success'] = True
            result['error_type'] = None
        
        # 4. 下载处理后的图片
        result = self._download_processed_image(result, original_image)
        
        self.logger.info(f"MonkeyOCR解析完成: {original_image}, 耗时: {result.get('processing_time', 0):.1f}秒")
        
        return {"result": result}


class MonkeyOcrParseParser(MonkeyOcrParser):
    """MonkeyOCR Parse解析器 - parse模式（无prompt）"""
    
    def __init__(self):
        # 根据curls.txt配置：fn_index=3，trigger_id=10，无prompt（data=[null,null]）
        super().__init__(
            prompt=None,  # parse模式不使用prompt
            fn_index=3,
            trigger_id=10
        )
        self.name = "monkey_ocr_latex"  # 保持向后兼容的名称
        self.prompt = None  # 确保prompt为None
    
    def _submit_and_get_result(self, session_hash: str) -> Dict[str, Any]:
        """
        提交处理请求并获取结果 - parse模式特殊处理
        
        Args:
            session_hash: 会话哈希
            
        Returns:
            Dict: 处理结果
        """
        # 1. 提交处理请求 - parse模式使用null prompt
        if not self.client.submit_processing_request(session_hash, None, self.fn_index, self.trigger_id):
            self.logger.error("提交处理请求失败")
            return {
                'processed_image_url': None,
                'text_content': ['MonkeyOCR处理请求提交失败'],
                'html': 'MonkeyOCR处理请求提交失败',
                'is_timeout': False,
                'processing_time': 0,
                'success': False,
                'error_type': 'request_failed'
            }
        
        # 2. 获取处理结果
        result = self.client.get_processing_result(session_hash)
        if result is None:
            self.logger.error("获取处理结果失败")
            return {
                'processed_image_url': None,
                'text_content': ['MonkeyOCR获取结果失败'],
                'html': 'MonkeyOCR获取结果失败',
                'is_timeout': False,
                'processing_time': 0
            }
        
        return result


# 为了向后兼容，保留原有的类名
class MonkeyOcrLatexParser(MonkeyOcrParseParser):
    """MonkeyOCR LaTeX解析器 - 实际上是parse模式"""
    pass


class LocalMonkeyOcrParser(BaseParser):
    """本地MonkeyOCR解析器"""
    
    def __init__(self):
        super().__init__("monkey_ocr_local")
        self.config_file = "/data/projects/kingsoft/personal/workspace/tablerag/monkeyocr/config.yaml"
        self.parse_script = "/data/opensrc/MonkeyOCR/parse.py"
    
    def is_available(self) -> bool:
        """检查本地MonkeyOCR是否可用"""
        try:
            return (os.path.exists(self.config_file) and 
                    os.path.exists(self.parse_script))
        except Exception:
            return False
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用本地MonkeyOCR解析
        
        Note: 本地MonkeyOCR需要批量处理整个目录，不是单个文件
        所以这里需要特殊处理
        """
        # 对于本地MonkeyOCR，我们可能需要整个图片目录
        images_dir = file_info.get("images_dir") or os.getenv("IMAGES_DIR")
        monkey_ocr_dir = file_info.get("monkey_ocr_dir") or os.getenv("MONKEY_OCR_DIR")
        
        if not images_dir or not monkey_ocr_dir:
            # 如果是单个文件的调用，我们创建临时目录处理
            original_image = file_info.get("original_image")
            image_path = file_info.get("image_path")
            
            if not original_image or not image_path:
                raise ValueError("缺少必要的图片信息：original_image或image_path")
            
            # 为单个文件创建临时目录结构
            import tempfile
            import shutil
            
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_images_dir = os.path.join(temp_dir, "images")
                temp_output_dir = os.path.join(temp_dir, "output")
                os.makedirs(temp_images_dir, exist_ok=True)
                os.makedirs(temp_output_dir, exist_ok=True)
                
                # 复制图片到临时目录
                temp_image_path = os.path.join(temp_images_dir, original_image)
                shutil.copy2(image_path, temp_image_path)
                
                # 执行解析
                success = self._run_local_parsing(temp_images_dir, temp_output_dir)
                if success:
                    results = self._load_local_results(temp_output_dir)
                    # 找到对应的结果
                    for result in results:
                        if result.get("original_image") == original_image:
                            return {"result": result.get("result")}
                
                return {"result": {
                    "html": "本地MonkeyOCR解析失败",
                    "markdown": "",
                    "latex": "",
                    "is_timeout": False,
                    "processing_time": 0,
                    "type": "error",
                    "success": False,
                    "error_type": "parsing_failed"
                }}
        else:
            # 原有的批量处理逻辑
            success = self._run_local_parsing(images_dir, monkey_ocr_dir)
            
            if success:
                results = self._load_local_results(monkey_ocr_dir)
                return {"results": results, "success": True}
            else:
                return {"results": [], "success": False}
    
    def _run_local_parsing(self, images_dir: str, monkey_ocr_dir: str) -> bool:
        """执行本地MonkeyOCR解析"""
        if not os.path.exists(images_dir) or not os.listdir(images_dir):
            self.logger.warning("没有找到图片文件，跳过本地MonkeyOCR解析")
            return False
        
        self.logger.info("开始本地MonkeyOCR解析...")
        
        input_path = f"{images_dir}/"
        output_path = f"{monkey_ocr_dir}/"
        
        try:
            cmd = [
                "python", self.parse_script,
                input_path,
                "-o", output_path,
                "-t", "table",
                "-c", self.config_file
            ]
            
            self.logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            self.logger.info("MonkeyOCR 输出:")
            self.logger.info(f"STDOUT: {result.stdout}")
            if result.stderr:
                self.logger.info(f"STDERR: {result.stderr}")
            
            # 检查解析结果
            if os.path.exists(monkey_ocr_dir) and os.listdir(monkey_ocr_dir):
                self.logger.info("本地MonkeyOCR解析完成")
                return True
            else:
                self.logger.warning("本地MonkeyOCR解析可能失败，输出目录为空")
                return False
                
        except subprocess.CalledProcessError as e:
            self.logger.error(f"本地MonkeyOCR解析失败: {e}")
            self.logger.error(f"stdout: {e.stdout}")
            self.logger.error(f"stderr: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"本地MonkeyOCR解析异常: {e}")
            return False
    
    def _load_local_results(self, monkey_ocr_dir: str) -> list:
        """加载本地MonkeyOCR解析结果"""
        results = []
        
        try:
            # 查找所有的结果文件
            result_files = glob.glob(os.path.join(monkey_ocr_dir, "*", "*_table_result.*"))
            self.logger.info(f"找到 {len(result_files)} 个结果文件")
            
            for result_file in result_files:
                try:
                    # 从文件路径提取文件名
                    dir_name = os.path.basename(os.path.dirname(result_file))
                    original_image = dir_name
                    pdf_filename = f"{Path(original_image).stem}.pdf"
                    
                    # 读取结果内容
                    with open(result_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查其他格式文件
                    html_file = result_file.replace("_table_result.md", "_table_result.html")
                    latex_file = result_file.replace("_table_result.md", "_table_result.tex")
                    
                    html_content = ""
                    latex_content = ""
                    
                    if os.path.exists(html_file):
                        with open(html_file, 'r', encoding='utf-8') as f:
                            html_content = f.read()
                    
                    if os.path.exists(latex_file):
                        with open(latex_file, 'r', encoding='utf-8') as f:
                            latex_content = f.read()
                    
                    # 智能检测内容格式
                    detected_type = self._detect_content_type(content)
                    
                    # 构造结果对象
                    result = {
                        "html": html_content if html_content else content if detected_type == "html" else "",
                        "markdown": content,
                        "latex": latex_content if latex_content else content if detected_type == "latex" else "",
                        "is_timeout": False,
                        "processing_time": 0,
                        "type": detected_type,
                        "success": True if content.strip() else False,
                        "error_type": None if content.strip() else "empty_result"
                    }
                    
                    results.append({
                        "filename": pdf_filename,
                        "result": result,
                        "source_file": result_file,
                        "original_image": original_image
                    })
                    
                    self.logger.info(f"加载本地MonkeyOCR结果: {original_image}")
                    
                except Exception as e:
                    self.logger.error(f"读取本地MonkeyOCR结果文件失败 {result_file}: {e}")
            
            self.logger.info(f"总共加载了 {len(results)} 个本地MonkeyOCR结果")
            
        except Exception as e:
            self.logger.error(f"处理本地MonkeyOCR结果失败: {e}")
        
        return results
    
    def _detect_content_type(self, content: str) -> str:
        """检测内容类型"""
        if not content.strip():
            return "empty"
        
        content = content.strip()
        if content.startswith('\\begin{tabular}') or '\\begin{tabular}' in content:
            return "latex"
        elif content.startswith('<table') or '<table' in content:
            return "html"
        else:
            return "markdown" 