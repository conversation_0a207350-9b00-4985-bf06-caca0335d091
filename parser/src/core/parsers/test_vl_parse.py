#!/usr/bin/env python3
# coding:utf-8
import os
import sys
import argparse
import tempfile
import datetime
import re
from typing import Optional
from PIL import Image
from clients.ks3_client import KS3Client
from clients.llm_client import LLMClient

# 尝试导入markdown库
try:
    import markdown
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False
    print("Warning: markdown library not found. HTML rendering will be disabled.")

# 初始化日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImageProcessor:
    def __init__(self):
        # 初始化KS3客户端
        self.ks3 = KS3Client()
        self.ks3.init(
            host=os.getenv('KS3_HOST', 'your-ks3-host'),
            ak=os.getenv('KS3_AK', 'your-access-key'),
            sk=os.getenv('KS3_SK', 'your-secret-key'),
            bucket=os.getenv('KS3_BUCKET', 'your-bucket-name')
        )
        
        # 初始化LLM客户端
        self.llm = LLMClient()
        
        # 压缩目标大小 (500KB)
        self.max_size_bytes = 500 * 1024

    def extract_markdown_content(self, llm_response: dict) -> str:
        """
        从LLM响应中提取markdown内容
        """
        try:
            if not isinstance(llm_response, dict):
                return ""
            
            choices = llm_response.get('choices', [])
            if not choices:
                return ""
            
            content = choices[0].get('message', {}).get('content', '')
            if not content:
                return ""
            
            # 如果内容被```markdown```包围，提取其中的内容
            markdown_pattern = r'```markdown\s*\n(.*?)\n```'
            match = re.search(markdown_pattern, content, re.DOTALL)
            if match:
                return match.group(1).strip()
            
            # 如果没有找到markdown代码块，可能是直接的markdown内容
            # 移除可能的前缀说明文字
            lines = content.split('\n')
            markdown_lines = []
            found_table = False
            
            for line in lines:
                # 检测是否是表格行
                if '|' in line and not line.strip().startswith('#'):
                    found_table = True
                    markdown_lines.append(line)
                elif found_table and (line.strip().startswith('#') or line.strip().startswith('|') or line.strip() == ''):
                    markdown_lines.append(line)
                elif found_table and line.strip() and not any(keyword in line.lower() for keyword in ['解析说明', '希望', '需求', '表格部分', '检验项目部分']):
                    markdown_lines.append(line)
                elif found_table and any(keyword in line.lower() for keyword in ['解析说明', '希望', '需求']):
                    # 遇到解析说明就停止
                    break
            
            if markdown_lines:
                return '\n'.join(markdown_lines).strip()
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"提取markdown内容失败: {e}")
            return ""

    def render_markdown_to_html(self, markdown_content: str) -> str:
        """
        将markdown内容渲染为HTML
        """
        if not MARKDOWN_AVAILABLE:
            logger.warning("markdown库不可用，返回原始内容")
            return f"<pre>{markdown_content}</pre>"
        
        try:
            # 配置markdown扩展
            md = markdown.Markdown(extensions=['tables', 'fenced_code'])
            html_content = md.convert(markdown_content)
            
            # 添加基本的CSS样式
            styled_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>VL LLM 解析结果</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        line-height: 1.6;
                    }}
                    table {{
                        border-collapse: collapse;
                        width: 100%;
                        margin: 20px 0;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }}
                    th {{
                        background-color: #f2f2f2;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{
                        background-color: #f9f9f9;
                    }}
                    h1, h2, h3, h4, h5, h6 {{
                        color: #333;
                        margin-top: 25px;
                    }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            return styled_html
            
        except Exception as e:
            logger.error(f"渲染markdown失败: {e}")
            return f"<pre>{markdown_content}</pre>"

    def compress_image(self, image_path: str) -> str:
        """
        压缩图片到500K以内
        
        Args:
            image_path: 原始图片路径
            
        Returns:
            压缩后的图片路径
        """
        try:
            # 检查原始文件大小
            original_size = os.path.getsize(image_path)
            logger.info(f"原始图片大小: {original_size / 1024:.1f}KB")
            
            if original_size <= self.max_size_bytes:
                logger.info("图片大小已符合要求，无需压缩")
                return image_path
            
            # 打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果是RGBA或其他模式）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(
                    suffix='.jpg', 
                    delete=False, 
                    dir=os.path.dirname(image_path)
                )
                temp_path = temp_file.name
                temp_file.close()
                
                # 二分法查找合适的质量参数
                quality_min, quality_max = 10, 95
                best_quality = 95
                
                while quality_min <= quality_max:
                    quality = (quality_min + quality_max) // 2
                    
                    # 尝试保存
                    img.save(temp_path, format='JPEG', quality=quality, optimize=True)
                    
                    # 检查文件大小
                    current_size = os.path.getsize(temp_path)
                    
                    if current_size <= self.max_size_bytes:
                        best_quality = quality
                        quality_min = quality + 1
                    else:
                        quality_max = quality - 1
                
                # 使用最佳质量保存最终文件
                img.save(temp_path, format='JPEG', quality=best_quality, optimize=True)
                
                final_size = os.path.getsize(temp_path)
                logger.info(f"图片压缩完成: {original_size / 1024:.1f}KB -> {final_size / 1024:.1f}KB (质量: {best_quality})")
                
                return temp_path
                
        except Exception as e:
            logger.error(f"图片压缩失败: {e}", exc_info=True)
            return image_path

    def process_image(self, image_path: str, prompt: str, timeout: int = 3600) -> Optional[str]:
        """
        处理图片的完整流程:
        1. 压缩图片（如果需要）
        2. 上传图片到KS3
        3. 生成分享链接
        4. 调用LLM API处理图片
        
        Args:
            image_path: 本地图片路径
            prompt: 给LLM的提示语
            timeout: 分享链接有效期(秒)
            
        Returns:
            LLM的处理结果
        """
        compressed_path = None
        try:
            # 1. 压缩图片（如果需要）
            compressed_path = self.compress_image(image_path)
            
            # 2. 上传图片到KS3
            ks3_path = f"llm_images/{os.path.basename(image_path)}"
            if not self.ks3.upload_from_file(ks3_path, compressed_path):
                logger.error(f"上传图片失败: {compressed_path}")
                return None
            
            # 3. 生成分享链接
            image_url = self.ks3.generate_url(ks3_path, timeout)
            if not image_url:
                logger.error(f"生成图片分享链接失败: {ks3_path}")
                return None
            logger.info(f"图片分享链接生成成功: {image_url}")
            
            # 4. 调用LLM处理图片
            result = self.llm.chat_completion(image_url, prompt)
            logger.info(f"LLM处理结果: {result}")
            
            return result
        
        except Exception as e:
            logger.error(f"处理图片失败: {e}", exc_info=True)
            return None
        
        finally:
            # 清理临时文件
            if compressed_path and compressed_path != image_path and os.path.exists(compressed_path):
                try:
                    os.unlink(compressed_path)
                    logger.info(f"清理临时压缩文件: {compressed_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="图片处理流程: 上传到KS3并通过LLM处理")
    parser.add_argument('image_path', help='本地图片路径')
    parser.add_argument('--prompt', default='请描述这张图片的内容', help='给LLM的提示语')
    parser.add_argument('--timeout', type=int, default=3600, help='分享链接有效期(秒)')
    parser.add_argument('--output', help='保存结果到指定文件路径(JSON格式)')
    
    args = parser.parse_args()
    
    processor = ImageProcessor()
    result = processor.process_image(args.image_path, args.prompt, args.timeout)
    
    if result:
        print("处理成功，结果如下:")
        print(result)
        
        # 如果指定了输出文件，保存结果
        if args.output:
            try:
                import json
                save_data = {
                    "image_path": args.image_path,
                    "prompt": args.prompt,
                    "result": result,
                    "timestamp": datetime.datetime.now().isoformat()
                }
                
                output_dir = os.path.dirname(args.output)
                os.makedirs(output_dir, exist_ok=True)
                
                # 保存JSON结果
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)
                logger.info(f"JSON结果已保存到: {args.output}")
                
                # 提取markdown内容并渲染为HTML
                markdown_content = processor.extract_markdown_content(result)
                if markdown_content:
                    html_content = processor.render_markdown_to_html(markdown_content)
                    
                    # 生成HTML文件路径
                    base_name = os.path.splitext(os.path.basename(args.output))[0]
                    html_file = os.path.join(output_dir, f"{base_name}.html")
                    
                    with open(html_file, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    logger.info(f"HTML结果已保存到: {html_file}")
                    
                    # 同时保存纯markdown文件
                    md_file = os.path.join(output_dir, f"{base_name}.md")
                    with open(md_file, 'w', encoding='utf-8') as f:
                        f.write(markdown_content)
                    logger.info(f"Markdown结果已保存到: {md_file}")
                else:
                    logger.warning("未能提取到markdown内容")
                    
            except Exception as e:
                logger.error(f"保存结果失败: {e}")
        
        sys.exit(0)
    else:
        print("处理失败，请查看日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
