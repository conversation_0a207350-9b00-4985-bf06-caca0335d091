"""
解析流程控制器

统一控制整个解析流程的执行
"""
import os
from typing import Dict, Any, List
import logging

from .parse_manager import ParseManager
from .file_manager import FileManager
from .result_manager import ResultManager
from clients.kdocs_client import KdocsClient

logger = logging.getLogger(__name__)


class ParsePipeline:
    """解析流程控制器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 从环境变量读取配置
        self.dataset_name = os.getenv("DATASET_NAME", "default_test")
        self.kdocs_config = self._load_kdocs_config()
        
        # 初始化各个管理器
        self.kdocs_client = KdocsClient(default_cookie=self.kdocs_config["cookie"])
        self.file_manager = FileManager(kdocs_client=self.kdocs_client)
        self.parse_manager = ParseManager().create_default_parsers()
        self.result_manager = ResultManager(self.dataset_name)
        
        self.logger.info(f"解析流程初始化完成，当前测试集: {self.dataset_name}")
    
    def _load_kdocs_config(self) -> Dict[str, str]:
        """加载KDocs配置"""
        return {
            "cookie": "weboffice_device_id=aa2aa9b695c44bbc775892c590ad33b1; wps_endcloud=1; userInNewLayout=true; cid=41000207; uid=1562054758; wps_sid=V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066; xsr-diffversion=3; Hm_lvt_cb2fa0997df4ff8e739c666ee2487fd9=1741673783,1743144033,1743392329; weboffice_cdn=21; swi_acc_redirect_limit=0; visitorid=1151692900; csrf=Ae7XAstwSD33ahE6RSSXm6h6BF847N6n; wpsua=V1BTVUEvMS4wICh3ZWIta2RvY3M6Q2hyb21lXzEzNi4wLjAuMDsgbWFjOk9TIFg7IEQ4UzNtYlMxUkRhamVKMFpNeW1YX2c9PTpUV0ZqYVc1MGIzTm9JQ0E9KSBNYWNpbnRvc2gv; HWWAFSESTIME=1749096088613; HWWAFSESID=82ef41218ab2762d6b0; userid=1562054758; woa_corp_id=41000207; appcdn=volcengine-kdocs-cache.wpscdn.cn; env=prod-1; region=hwy",
            "group_id": "2255465142",
            "company_id": "41000207",
            "root_parent_id": "************"
        }
    
    def run_full_pipeline(self) -> str:
        """
        运行完整的解析流程
        
        Returns:
            结果文件路径
        """
        try:
            self.logger.info("🚀 开始执行完整解析流程")
            
            # 初始化解析会话
            session_file = self.result_manager.init_session_results({
                "dataset_name": self.dataset_name,
                "parser_config": {
                    "enabled_parsers": self.parse_manager.get_enabled_parsers(),
                    "execution_mode": self.parse_manager.get_execution_mode()
                }
            })
            
            # 1. 准备云端环境
            self.logger.info("1️⃣ 准备云端环境...")
            test_folder_info = self._prepare_cloud_environment()
            
            # 2. 处理本地文件
            self.logger.info("2️⃣ 处理本地文件...")
            processed_files = self._process_local_files()
            self.result_manager.set_processed_files(processed_files)
            
            # 3. 上传文件到云端
            self.logger.info("3️⃣ 上传文件到云端...")
            uploaded_files = self._upload_files(processed_files, test_folder_info)
            self.result_manager.set_uploaded_files(uploaded_files)
            
            # 4. 获取云端文件列表
            self.logger.info("4️⃣ 获取云端文件列表...")
            cloud_files = self._get_cloud_files(test_folder_info, uploaded_files)
            
            # 5. 执行多路解析（增量保存结果）
            self.logger.info("5️⃣ 执行多路解析...")
            self._run_multi_parser_analysis_incremental(cloud_files)
            
            # 6. 完成会话并保存最终结果
            self.logger.info("6️⃣ 完成解析会话...")
            final_result_file = self.result_manager.finalize_session()
            
            self.logger.info(f"✅ 解析流程完成，结果已保存到: {final_result_file}")
            return final_result_file
            
        except Exception as e:
            self.logger.error(f"❌ 解析流程失败: {type(e).__name__}: {str(e)}")
            # 如果有活跃的会话，也尝试保存当前进度
            try:
                self.result_manager.finalize_session()
            except:
                pass
            raise
    
    def _prepare_cloud_environment(self) -> Dict[str, Any]:
        """准备云端环境"""
        # 创建或获取测试集文件夹
        test_folder_result = self.file_manager.create_or_get_test_folder(
            group_id=self.kdocs_config["group_id"],
            parent_id=self.kdocs_config["root_parent_id"],
            test_name=self.dataset_name,
            company_id=self.kdocs_config["company_id"]
        )
        
        test_folder = test_folder_result["folder"]
        test_parent_id = str(test_folder["id"])
        self.logger.info(f"✅ 测试集云端文件夹: {test_folder['fname']} (ID: {test_parent_id})")
        
        # 清理现有文件
        existing_files = self.file_manager.list_folder_files(
            group_id=self.kdocs_config["group_id"],
            parent_id=test_parent_id,
            company_id=self.kdocs_config["company_id"]
        )
        
        if existing_files:
            self.logger.info(f"发现 {len(existing_files)} 个现有文件，开始清理...")
            self.file_manager.delete_folder_files(
                files=existing_files,
                group_id=self.kdocs_config["group_id"],
                company_id=self.kdocs_config["company_id"]
            )
        
        return {
            "folder": test_folder,
            "parent_id": test_parent_id
        }
    
    def _process_local_files(self) -> List[Dict[str, str]]:
        """处理本地文件"""
        images_dir = os.getenv("IMAGES_DIR")
        pdf_dir = os.getenv("PDF_DIR")
        
        if not images_dir or not pdf_dir:
            raise ValueError("缺少必要的目录环境变量：IMAGES_DIR或PDF_DIR")
        
        return self.file_manager.process_images_to_pdfs(images_dir, pdf_dir)
    
    def _upload_files(self, processed_files: List[Dict[str, str]], 
                     test_folder_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """上传文件到云端"""
        return self.file_manager.upload_files(
            files=processed_files,
            group_id=self.kdocs_config["group_id"],
            parent_id=test_folder_info["parent_id"],
            company_id=self.kdocs_config["company_id"]
        )
    
    def _get_cloud_files(self, test_folder_info: Dict[str, Any], 
                        uploaded_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取云端文件列表"""
        files = self.file_manager.list_folder_files(
            group_id=self.kdocs_config["group_id"],
            parent_id=test_folder_info["parent_id"],
            company_id=self.kdocs_config["company_id"]
        )
        
        # 更新跳过的文件的ID
        self.file_manager.update_skipped_file_ids(uploaded_files, files)
        
        return files
    
    def _run_multi_parser_analysis_incremental(self, cloud_files: List[Dict[str, Any]]):
        """执行多路解析分析（增量保存结果）"""
        # 获取启用的解析器和执行模式
        enabled_parsers = self.parse_manager.get_available_enabled_parsers()
        execution_mode = self.parse_manager.get_execution_mode()
        
        if not enabled_parsers:
            self.logger.warning("没有启用的解析器")
            return
        
        self.logger.info(f"使用解析器: {enabled_parsers}")
        self.logger.info(f"执行模式: {execution_mode}")
        
        # 更新总文件数
        total_files = len(cloud_files)
        self.result_manager.update_progress(total_count=total_files)
        
        images_dir = os.getenv("IMAGES_DIR")
        
        for file_index, file in enumerate(cloud_files, 1):
            # 获取原始图片信息
            original_image = self._get_original_image_for_file(file)
            if not original_image:
                self.logger.warning(f"跳过文件 {file['fname']}，没有对应的原始图片")
                continue
            
            # 构造文件信息
            file_info = {
                "filename": file["fname"],
                "file_id": str(file["id"]),
                "original_image": original_image,
                "image_path": os.path.join(images_dir, original_image) if images_dir else None,
                # 为local monkey ocr提供额外信息
                "images_dir": images_dir,
                "monkey_ocr_dir": os.getenv("MONKEY_OCR_DIR")
            }
            
            # 更新当前处理进度
            self.result_manager.update_progress(
                current_file=file["fname"],
                current_parser="starting"
            )
            
            # 使用多个解析器解析
            self.logger.info(f"📄 解析文件 ({file_index}/{total_files}): {file['fname']}")
            file_results = self.parse_manager.parse_with_multiple_parsers(
                file_info=file_info
            )
            
            # 立即保存该文件的解析结果
            self.result_manager.add_file_results(file_info, file_results)
            
            # 显示当前文件的解析统计
            success_count = len([r for r in file_results.values() if r.get('success', False)])
            self.logger.info(f"✅ 文件 {file['fname']} 解析完成: {success_count}/{len(file_results)} 个解析器成功")
        
        # 显示最终解析统计
        parse_results = self.result_manager._current_session_data.get("parse_results", {})
        self.logger.info("🎯 最终解析统计:")
        for parser_name, results in parse_results.items():
            success_count = len([r for r in results if r.get('success', False)])
            self.logger.info(f"  {parser_name}: {success_count}/{len(results)} 成功")
    
    def _has_original_image(self, file: Dict[str, Any], uploaded_files: List[Dict[str, Any]]) -> bool:
        """检查文件是否有对应的原始图片"""
        return any(uf["fname"] == file["fname"] for uf in uploaded_files)
    
    def _get_original_image_for_file(self, file: Dict[str, Any]) -> str:
        """获取文件对应的原始图片名"""
        # 这里需要根据实际的文件名映射逻辑来实现
        # 假设PDF文件名与图片文件名的基础名相同
        from pathlib import Path
        base_name = Path(file["fname"]).stem
        
        # 尝试找到对应的图片文件
        images_dir = os.getenv("IMAGES_DIR")
        if images_dir and os.path.exists(images_dir):
            image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp')
            for ext in image_extensions:
                potential_image = f"{base_name}{ext}"
                if os.path.exists(os.path.join(images_dir, potential_image)):
                    return potential_image
        
        return None 