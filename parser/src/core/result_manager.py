"""
结果管理器

负责解析结果的保存、加载和管理
"""
import os
import json
import datetime
import threading
import shutil
from typing import Dict, Any, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ResultManager:
    """结果管理器"""
    
    def __init__(self, dataset_name: str = None):
        self.dataset_name = dataset_name or os.getenv("DATASET_NAME", "default_test")
        self.logger = logging.getLogger(__name__)
        
        # 从环境变量获取路径配置
        self.parse_results_base_dir = os.getenv("PARSE_RESULTS_BASE_DIR", 
                                               os.path.join("..", "parse_results"))
        self.parse_results_dir = os.path.join(self.parse_results_base_dir, self.dataset_name)
        
        # 确保目录存在
        os.makedirs(self.parse_results_dir, exist_ok=True)
        
        # 用于线程安全的文件锁
        self._file_lock = threading.Lock()
        
        # 当前会话的结果数据
        self._current_session_data = None
        self._current_session_file = None
    
    def init_session_results(self, metadata: Dict[str, Any] = None) -> str:
        """
        初始化新的解析会话结果文件
        
        Args:
            metadata: 会话元数据
            
        Returns:
            会话结果文件路径
        """
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        
        # 初始化会话数据结构
        self._current_session_data = {
            "timestamp": timestamp,
            "dataset_name": self.dataset_name,
            "metadata": {
                "parse_date": datetime.datetime.now().isoformat(),
                "version": "2.0",  # 重构后的版本
                "total_files": 0,
                "completed_files": 0,
                "status": "in_progress",
                **(metadata or {})
            },
            "processed_files": [],
            "uploaded_files": [],
            "parse_results": {},
            "progress": {
                "current_file": "",
                "current_parser": "",
                "completed_count": 0,
                "total_count": 0,
                "start_time": datetime.datetime.now().isoformat()
            }
        }
        
        # 确定文件路径
        self._current_session_file = os.path.join(self.parse_results_dir, f"parse_results_{timestamp}.json")
        
        # 初始保存
        self._save_current_session()

        # 初始化时也创建latest_results.json，让前端从一开始就能看到进度
        self._update_latest_results_realtime()

        self.logger.info(f"初始化新的解析会话: {self._current_session_file}")
        return self._current_session_file
    
    def update_session_metadata(self, **kwargs):
        """更新会话元数据"""
        if self._current_session_data:
            self._current_session_data["metadata"].update(kwargs)
            self._save_current_session()
    
    def update_progress(self, current_file: str = None, current_parser: str = None, 
                       completed_count: int = None, total_count: int = None):
        """
        更新解析进度
        
        Args:
            current_file: 当前处理的文件
            current_parser: 当前解析器
            completed_count: 已完成文件数
            total_count: 总文件数
        """
        if not self._current_session_data:
            return
        
        progress = self._current_session_data["progress"]
        if current_file is not None:
            progress["current_file"] = current_file
        if current_parser is not None:
            progress["current_parser"] = current_parser
        if completed_count is not None:
            progress["completed_count"] = completed_count
            self._current_session_data["metadata"]["completed_files"] = completed_count
        if total_count is not None:
            progress["total_count"] = total_count
            self._current_session_data["metadata"]["total_files"] = total_count
        
        self._save_current_session()

        # 进度更新时也实时更新latest_results.json
        self._update_latest_results_realtime()
    
    def add_file_results(self, file_info: Dict[str, Any], parse_results: Dict[str, Dict[str, Any]]):
        """
        添加单个文件的解析结果
        
        Args:
            file_info: 文件信息
            parse_results: 该文件的所有解析器结果
        """
        if not self._current_session_data:
            self.logger.warning("未初始化会话，无法添加文件结果")
            return
        
        filename = file_info.get("filename", "unknown")
        self.logger.info(f"添加文件解析结果: {filename}")
        
        # 按解析器类型组织结果
        session_results = self._current_session_data["parse_results"]
        
        for parser_name, result in parse_results.items():
            if parser_name not in session_results:
                session_results[parser_name] = []
            session_results[parser_name].append(result)
        
        # 更新进度
        current_completed = self._current_session_data["progress"]["completed_count"] + 1
        self.update_progress(
            current_file=filename,
            current_parser="completed",
            completed_count=current_completed
        )
        
        self.logger.info(f"文件 {filename} 解析结果已保存 ({current_completed}/{self._current_session_data['progress']['total_count']})")

        # 实时更新latest_results.json，让前端可以实时获取最新进度
        self._update_latest_results_realtime()

    def _update_latest_results_realtime(self):
        """
        实时更新latest_results.json文件
        每次有新的解析结果时都会调用，让前端可以实时获取最新进度
        """
        if not self._current_session_data:
            return

        latest_filename = os.path.join(self.parse_results_dir, "latest_results.json")

        try:
            with self._file_lock:
                with open(latest_filename, "w", encoding="utf-8") as f:
                    json.dump(self._current_session_data, f, ensure_ascii=False, indent=2, default=str)
            self.logger.debug(f"实时更新latest_results.json: {latest_filename}")
        except Exception as e:
            self.logger.error(f"实时更新latest_results.json失败: {e}")

    def set_processed_files(self, processed_files: List[Dict[str, Any]]):
        """设置处理过的文件列表"""
        if self._current_session_data:
            self._current_session_data["processed_files"] = processed_files
            self._save_current_session()
    
    def set_uploaded_files(self, uploaded_files: List[Dict[str, Any]]):
        """设置上传的文件列表"""
        if self._current_session_data:
            self._current_session_data["uploaded_files"] = uploaded_files
            self._save_current_session()
    
    def finalize_session(self) -> str:
        """
        完成当前会话并保存最终结果
        
        Returns:
            最终结果文件路径
        """
        if not self._current_session_data or not self._current_session_file:
            self.logger.warning("没有活跃的会话可以完成")
            return None
        
        # 更新状态为完成
        self._current_session_data["metadata"]["status"] = "completed"
        self._current_session_data["progress"]["end_time"] = datetime.datetime.now().isoformat()
        
        # 最终保存
        self._save_current_session()
        
        # 更新最新结果文件
        latest_filename = os.path.join(self.parse_results_dir, "latest_results.json")
        
        # 如果存在旧的latest_results.json，先备份
        if os.path.exists(latest_filename):
            try:
                backup_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = os.path.join(self.parse_results_dir, f"latest_results_backup_{backup_time}.json")
                shutil.copy2(latest_filename, backup_filename)
                self.logger.info(f"已备份旧的latest_results.json到: {backup_filename}")
            except Exception as e:
                self.logger.warning(f"备份旧latest_results.json失败: {e}")
        
        # 保存新的latest_results.json
        try:
            with self._file_lock:
                with open(latest_filename, "w", encoding="utf-8") as f:
                    json.dump(self._current_session_data, f, ensure_ascii=False, indent=2, default=str)
            self.logger.info(f"已更新latest_results.json: {latest_filename}")
        except Exception as e:
            self.logger.error(f"更新latest_results.json失败: {e}")
        
        session_file = self._current_session_file
        self.logger.info(f"解析会话已完成: {session_file}")
        
        # 清理当前会话状态
        self._current_session_data = None
        self._current_session_file = None
        
        return session_file
    
    def _save_current_session(self):
        """保存当前会话数据到文件"""
        if not self._current_session_data or not self._current_session_file:
            return
        
        try:
            with self._file_lock:
                with open(self._current_session_file, "w", encoding="utf-8") as f:
                    json.dump(self._current_session_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存会话数据失败: {e}")
    
    def save_parse_results(self, results_data: Dict[str, Any]) -> str:
        """
        保存解析结果到JSON文件（兼容旧接口）
        
        Args:
            results_data: 解析结果数据
            
        Returns:
            保存的文件路径
        """
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        
        # 添加元数据
        complete_data = {
            "timestamp": timestamp,
            "dataset_name": self.dataset_name,
            "metadata": {
                "parse_date": datetime.datetime.now().isoformat(),
                "version": "2.0",  # 重构后的版本
                "total_files": len(results_data.get("processed_files", [])),
                "status": "completed"
            },
            **results_data
        }
        
        # 保存到时间戳文件
        result_filename = os.path.join(self.parse_results_dir, f"parse_results_{timestamp}.json")
        with open(result_filename, "w", encoding="utf-8") as f:
            json.dump(complete_data, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"解析结果已保存到: {result_filename}")
        
        # 同时保存一个最新结果的文件
        latest_filename = os.path.join(self.parse_results_dir, "latest_results.json")
        with open(latest_filename, "w", encoding="utf-8") as f:
            json.dump(complete_data, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"最新解析结果已保存到: {latest_filename}")
        return result_filename
    
    def load_latest_results(self) -> Dict[str, Any]:
        """
        加载最新的解析结果
        
        Returns:
            解析结果数据
        """
        latest_filename = os.path.join(self.parse_results_dir, "latest_results.json")
        
        if not os.path.exists(latest_filename):
            self.logger.warning(f"最新结果文件不存在: {latest_filename}")
            return {}
        
        try:
            with open(latest_filename, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 兼容性检查和升级
            data = self._upgrade_legacy_format(data)
            
            self.logger.info(f"成功加载最新解析结果: {latest_filename}")
            return data
        except Exception as e:
            self.logger.error(f"加载最新解析结果失败: {e}")
            return {}
    
    def _upgrade_legacy_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        升级旧格式的结果数据到新格式
        
        Args:
            data: 原始数据
            
        Returns:
            升级后的数据
        """
        # 检查是否需要升级
        if not isinstance(data, dict):
            return data
        
        # 确保有metadata字段
        if "metadata" not in data:
            data["metadata"] = {}
        
        metadata = data["metadata"]
        
        # 确保有必要的metadata字段
        if "version" not in metadata:
            metadata["version"] = "1.0"  # 标记为旧版本
        
        if "status" not in metadata:
            metadata["status"] = "completed"  # 旧格式默认认为是完成的
        
        if "parse_date" not in metadata:
            metadata["parse_date"] = datetime.datetime.now().isoformat()
        
        if "total_files" not in metadata:
            # 尝试从其他字段推断文件数量
            processed_files = data.get("processed_files", [])
            uploaded_files = data.get("uploaded_files", [])
            metadata["total_files"] = max(len(processed_files), len(uploaded_files))
        
        if "completed_files" not in metadata:
            metadata["completed_files"] = metadata["total_files"]
        
        # 确保有progress字段
        if "progress" not in data:
            data["progress"] = {
                "current_file": "",
                "current_parser": "completed",
                "completed_count": metadata["total_files"],
                "total_count": metadata["total_files"],
                "start_time": metadata.get("parse_date", datetime.datetime.now().isoformat()),
                "end_time": metadata.get("parse_date", datetime.datetime.now().isoformat())
            }
        
        # 升级解析结果的命名（兼容旧的kdc命名）
        if "parse_results" in data:
            parse_results = data["parse_results"]
            
            # 如果有旧的kdc结果但没有新的kdc_markdown，进行映射
            if "kdc" in parse_results and "kdc_markdown" not in parse_results:
                parse_results["kdc_markdown"] = parse_results["kdc"]
                self.logger.info("已将旧的 'kdc' 结果映射到 'kdc_markdown'")
        
        # 确保dataset_name字段
        if "dataset_name" not in data:
            data["dataset_name"] = self.dataset_name
        
        # 确保timestamp字段
        if "timestamp" not in data:
            # 尝试从文件名或parse_date提取时间戳
            parse_date = metadata.get("parse_date", "")
            if parse_date:
                try:
                    dt = datetime.datetime.fromisoformat(parse_date.replace('Z', '+00:00'))
                    data["timestamp"] = dt.strftime("%Y_%m_%d_%H_%M_%S")
                except:
                    data["timestamp"] = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
            else:
                data["timestamp"] = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        
        # 如果数据被升级过，标记版本为2.0
        if metadata.get("version") == "1.0":
            metadata["version"] = "2.0"
            self.logger.info("已将结果数据格式从 v1.0 升级到 v2.0")
        
        return data
    
    def load_results_by_timestamp(self, timestamp: str) -> Dict[str, Any]:
        """
        根据时间戳加载解析结果
        
        Args:
            timestamp: 时间戳
            
        Returns:
            解析结果数据
        """
        result_filename = os.path.join(self.parse_results_dir, f"parse_results_{timestamp}.json")
        
        if not os.path.exists(result_filename):
            self.logger.warning(f"指定时间戳的结果文件不存在: {result_filename}")
            return {}
        
        try:
            with open(result_filename, "r", encoding="utf-8") as f:
                data = json.load(f)
            self.logger.info(f"成功加载解析结果: {result_filename}")
            return data
        except Exception as e:
            self.logger.error(f"加载解析结果失败: {e}")
            return {}
    
    def list_available_results(self) -> List[Dict[str, str]]:
        """
        列出所有可用的解析结果

        Returns:
            结果文件信息列表
        """
        if not os.path.exists(self.parse_results_dir):
            return []

        results = []
        for filename in os.listdir(self.parse_results_dir):
            if filename.startswith("parse_results_") and filename.endswith(".json"):
                # 提取时间戳
                timestamp = filename.replace("parse_results_", "").replace(".json", "")
                filepath = os.path.join(self.parse_results_dir, filename)

                # 获取文件修改时间
                mtime = os.path.getmtime(filepath)
                modified_time = datetime.datetime.fromtimestamp(mtime).isoformat()

                results.append({
                    "timestamp": timestamp,
                    "filename": filename,
                    "filepath": filepath,
                    "modified_time": modified_time
                })

        # 按时间戳排序（最新的在前）
        results.sort(key=lambda x: x["timestamp"], reverse=True)
        return results

    def create_latest_results_from_most_recent(self) -> str:
        """
        从最新的解析结果文件创建latest_results.json
        用于修复缺失的latest_results.json文件

        Returns:
            创建的latest_results.json文件路径
        """
        # 获取最新的结果文件
        available_results = self.list_available_results()
        if not available_results:
            self.logger.warning(f"没有找到任何解析结果文件在: {self.parse_results_dir}")
            return None

        most_recent = available_results[0]
        most_recent_file = most_recent["filepath"]

        self.logger.info(f"使用最新结果文件创建latest_results.json: {most_recent_file}")

        try:
            # 加载最新结果文件
            with open(most_recent_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 如果状态是in_progress，更新为completed
            if data.get("metadata", {}).get("status") == "in_progress":
                data["metadata"]["status"] = "completed"
                data["progress"]["end_time"] = datetime.datetime.now().isoformat()
                self.logger.info("已将状态从 'in_progress' 更新为 'completed'")

            # 创建latest_results.json
            latest_filename = os.path.join(self.parse_results_dir, "latest_results.json")

            # 如果存在旧的latest_results.json，先备份
            if os.path.exists(latest_filename):
                backup_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = os.path.join(self.parse_results_dir, f"latest_results_backup_{backup_time}.json")
                shutil.copy2(latest_filename, backup_filename)
                self.logger.info(f"已备份旧的latest_results.json到: {backup_filename}")

            # 保存新的latest_results.json
            with open(latest_filename, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"已创建latest_results.json: {latest_filename}")
            return latest_filename

        except Exception as e:
            self.logger.error(f"创建latest_results.json失败: {e}")
            return None