"""
表格数据解析器

用于将HTML表格数据转换为标注系统所需的格式
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup
import pandas as pd


class TableDataParser:
    """表格数据解析器"""
    
    def __init__(self):
        self.soup = None
        
    def parse_html_table(self, html_content: str) -> Optional[Dict]:
        """
        解析HTML表格内容
        
        Args:
            html_content: HTML内容
            
        Returns:
            Dict: 包含表格结构和内容的字典
        """
        try:
            self.soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找表格
            table = self.soup.find('table')
            if not table:
                print("未找到表格元素")
                return None
                
            # 解析表格数据
            table_data = self._extract_table_data(table)
            if not table_data:
                return None
                
            # 生成表格结构信息
            structure = self._generate_table_structure(table_data)
            
            # 生成HTML格式内容
            html_content = self._generate_html_table(table_data)

            return {
                'table_structure': json.dumps(structure, ensure_ascii=False),
                'table_content': html_content,
                'raw_data': table_data
            }
            
        except Exception as e:
            print(f"解析HTML表格失败: {e}")
            return None
    
    def _extract_table_data(self, table) -> Optional[List[List[str]]]:
        """提取表格数据"""
        try:
            rows = []
            
            # 处理表头
            thead = table.find('thead')
            if thead:
                header_rows = thead.find_all('tr')
                for tr in header_rows:
                    row_data = []
                    for cell in tr.find_all(['th', 'td']):
                        cell_text = cell.get_text(strip=True)
                        row_data.append(cell_text)
                    if row_data:
                        rows.append(row_data)
            
            # 处理表体
            tbody = table.find('tbody')
            if tbody:
                body_rows = tbody.find_all('tr')
            else:
                # 如果没有tbody，直接查找所有tr
                body_rows = table.find_all('tr')
                # 如果已经处理过表头，跳过表头行
                if thead:
                    header_count = len(thead.find_all('tr'))
                    body_rows = body_rows[header_count:]
            
            for tr in body_rows:
                row_data = []
                for cell in tr.find_all(['th', 'td']):
                    cell_text = cell.get_text(strip=True)
                    row_data.append(cell_text)
                if row_data:
                    rows.append(row_data)
            
            # 如果没有找到thead，假设第一行是表头
            if not thead and rows:
                # 检查第一行是否可能是表头（通常表头单元格较短且不包含数字）
                first_row = rows[0]
                is_header = any(
                    len(cell) <= 10 and not re.search(r'\d{2,}', cell) 
                    for cell in first_row if cell.strip()
                )
                if not is_header and len(rows) > 1:
                    # 如果第一行不像表头，尝试从HTML结构推断
                    first_tr = table.find('tr')
                    if first_tr and first_tr.find('th'):
                        is_header = True
                
            return rows if rows else None
            
        except Exception as e:
            print(f"提取表格数据失败: {e}")
            return None
    
    def _generate_table_structure(self, table_data: List[List[str]]) -> Dict:
        """生成表格结构信息"""
        if not table_data:
            return {}
            
        rows = len(table_data)
        cols = max(len(row) for row in table_data) if table_data else 0
        
        # 分析表头
        headers = table_data[0] if table_data else []
        
        # 分析数据类型
        data_types = []
        if len(table_data) > 1:
            for col_idx in range(cols):
                col_data = []
                for row_idx in range(1, rows):
                    if col_idx < len(table_data[row_idx]):
                        col_data.append(table_data[row_idx][col_idx])
                
                # 推断数据类型
                data_type = self._infer_data_type(col_data)
                data_types.append(data_type)
        
        return {
            'rows': rows,
            'cols': cols,
            'headers': headers,
            'data_types': data_types,
            'has_header': True,  # 假设第一行是表头
            'merged_cells': [],  # 暂不处理合并单元格
            'table_type': 'data_table'
        }
    
    def _infer_data_type(self, col_data: List[str]) -> str:
        """推断列数据类型"""
        if not col_data:
            return 'string'
            
        # 统计不同类型的数量
        numeric_count = 0
        date_count = 0
        
        for value in col_data:
            value = value.strip()
            if not value:
                continue
                
            # 检查是否为数字
            if re.match(r'^-?\d+\.?\d*$', value):
                numeric_count += 1
            # 检查是否为日期
            elif re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', value):
                date_count += 1
            # 检查是否为百分比
            elif value.endswith('%'):
                numeric_count += 1
        
        total_count = len([v for v in col_data if v.strip()])
        if total_count == 0:
            return 'string'
            
        # 如果超过70%是数字，认为是数字类型
        if numeric_count / total_count > 0.7:
            return 'number'
        # 如果超过50%是日期，认为是日期类型
        elif date_count / total_count > 0.5:
            return 'date'
        else:
            return 'string'
    
    def _generate_html_table(self, table_data: List[List[str]]) -> str:
        """生成HTML格式的表格"""
        if not table_data:
            return "<table></table>"

        html_lines = ['<table>']

        # 处理表头
        if table_data:
            headers = table_data[0]
            html_lines.append('  <thead>')
            html_lines.append('    <tr>')
            for header in headers:
                # 转义HTML特殊字符
                escaped_header = self._escape_html(header)
                html_lines.append(f'      <th>{escaped_header}</th>')
            html_lines.append('    </tr>')
            html_lines.append('  </thead>')

        # 处理数据行
        if len(table_data) > 1:
            html_lines.append('  <tbody>')
            for row in table_data[1:]:
                html_lines.append('    <tr>')
                # 确保行长度与表头一致
                padded_row = row + [''] * (len(headers) - len(row))
                for cell in padded_row[:len(headers)]:
                    # 转义HTML特殊字符
                    escaped_cell = self._escape_html(cell)
                    html_lines.append(f'      <td>{escaped_cell}</td>')
                html_lines.append('    </tr>')
            html_lines.append('  </tbody>')

        html_lines.append('</table>')
        return '\n'.join(html_lines)

    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        if not text:
            return ""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def parse_generated_data(self, gen_data_dir: str, image_filename: str) -> Optional[Dict]:
        """
        解析生成的表格数据
        
        Args:
            gen_data_dir: 生成数据目录
            image_filename: 图片文件名
            
        Returns:
            Dict: 解析结果
        """
        import os
        
        # 根据图片文件名推断HTML文件名
        base_name = os.path.splitext(image_filename)[0]
        html_filename = f"{base_name}.html"
        html_path = os.path.join(gen_data_dir, html_filename)
        
        if not os.path.exists(html_path):
            print(f"HTML文件不存在: {html_path}")
            return None
            
        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            return self.parse_html_table(html_content)
            
        except Exception as e:
            print(f"读取HTML文件失败: {e}")
            return None
