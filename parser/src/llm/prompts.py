"""
表格生成相关的提示词模板
"""

def get_table_generation_prompt(topic: str, rows: int, cols: int) -> str:
    """
    生成表格数据的提示词
    
    Args:
        topic: 表格主题
        rows: 行数
        cols: 列数
        
    Returns:
        str: 提示词
    """
    prompt = f"""
请根据主题"{topic}"生成一个{rows}行{cols}列的表格数据。

要求：
1. 表格内容必须与主题相关，具有实际意义和语义
2. 数据要真实可信，符合实际业务场景
3. 第一行作为表头，包含有意义的列名
4. 数据类型要多样化，包括文本、数字、日期等
5. 数据之间要有逻辑关联性
6. 避免使用无意义的随机字符

重要：请直接返回JSON格式，不要包含任何其他文本、解释或标记。

JSON结构：
{{
    "topic": "表格主题",
    "headers": ["列名1", "列名2", "列名3", "列名4"],
    "data": [
        ["数据1-1", "数据1-2", "数据1-3", "数据1-4"],
        ["数据2-1", "数据2-2", "数据2-3", "数据2-4"],
        ["数据3-1", "数据3-2", "数据3-3", "数据3-4"],
        ["数据4-1", "数据4-2", "数据4-3", "数据4-4"]
    ],
    "description": "表格内容的简要描述"
}}

约束条件：
- headers数组长度必须等于{cols}
- data数组长度必须等于{rows-1}（不包括表头）
- 每个data子数组长度必须等于{cols}
- 所有数据都应该是字符串格式
- 确保数据的真实性和一致性

请直接返回符合上述格式的JSON数据，不要添加任何额外的文本或标记："""
    return prompt

def get_table_topic_generation_prompt() -> str:
    """
    生成表格主题的提示词
    
    Returns:
        str: 提示词
    """
    prompt = """
请生成一个具有实际业务意义的表格主题。

要求：
1. 主题要具体明确，不要过于宽泛
2. 适合制作成结构化表格
3. 在实际工作或生活中常见
4. 包含多种数据类型（文本、数字、日期等）

请从以下领域中选择一个：
- 商业管理（销售、财务、人事、库存等）
- 教育培训（学生、课程、成绩、设备等）
- 医疗健康（患者、药品、设备、检查等）
- 生产制造（生产、质量、设备、原料等）
- 政府机构（人口、预算、设施、政策等）
- 科研学术（实验、项目、经费、论文等）

请直接返回一个简洁的主题名称，例如："员工绩效考核表"
"""
    return prompt

def get_table_validation_prompt(table_data: dict) -> str:
    """
    验证表格数据的提示词
    
    Args:
        table_data: 表格数据
        
    Returns:
        str: 提示词
    """
    prompt = f"""
请验证以下表格数据的质量和合理性：

表格数据：
{table_data}

请检查：
1. 数据格式是否正确
2. 列名是否有意义且与主题相关
3. 数据内容是否真实可信
4. 数据类型是否多样化
5. 数据之间是否有逻辑关联

请以JSON格式返回验证结果：
```json
{{
    "is_valid": true/false,
    "score": 0-100,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"]
}}
```
"""
    return prompt

# 预定义的表格模板
TABLE_TEMPLATES = {
    "员工信息表": {
        "headers": ["员工编号", "姓名", "部门", "职位", "入职日期", "薪资"],
        "data_types": ["string", "string", "string", "string", "date", "number"]
    },
    "销售记录表": {
        "headers": ["订单号", "客户名称", "产品名称", "数量", "单价", "总金额", "销售日期"],
        "data_types": ["string", "string", "string", "number", "number", "number", "date"]
    },
    "学生成绩表": {
        "headers": ["学号", "姓名", "班级", "语文", "数学", "英语", "总分"],
        "data_types": ["string", "string", "string", "number", "number", "number", "number"]
    },
    "库存管理表": {
        "headers": ["商品编码", "商品名称", "分类", "库存数量", "单价", "供应商"],
        "data_types": ["string", "string", "string", "number", "number", "string"]
    },
    "项目进度表": {
        "headers": ["项目编号", "项目名称", "负责人", "开始日期", "预计完成", "进度", "状态"],
        "data_types": ["string", "string", "string", "date", "date", "percentage", "string"]
    }
}

def get_template_based_prompt(template_name: str, rows: int) -> str:
    """
    基于模板生成表格数据的提示词
    
    Args:
        template_name: 模板名称
        rows: 数据行数
        
    Returns:
        str: 提示词
    """
    if template_name not in TABLE_TEMPLATES:
        raise ValueError(f"Unknown template: {template_name}")
    
    template = TABLE_TEMPLATES[template_name]
    headers = template["headers"]
    data_types = template["data_types"]
    
    prompt = f"""
请根据"{template_name}"模板生成{rows}行真实的表格数据。

表头：{headers}
数据类型：{data_types}

要求：
1. 生成的数据要真实可信，符合实际业务场景
2. 数据类型要符合指定要求
3. 数据之间要有逻辑关联性
4. 避免重复和无意义的数据

请以JSON格式返回：
```json
{{
    "topic": "{template_name}",
    "headers": {headers},
    "data": [
        // {rows}行数据
    ],
    "description": "表格内容描述"
}}
```
"""
    return prompt
