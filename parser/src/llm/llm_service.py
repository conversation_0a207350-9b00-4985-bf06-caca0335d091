import os
import json
import time
import requests
from typing import Optional, Dict, Any, List
from datetime import datetime

try:
    from openai import AzureOpenAI, OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Warning: OpenAI library not installed. LLM features will be disabled.")


class LLMService:
    """
    LLM服务类，负责与语言模型交互
    支持Azure OpenAI和自定义vLLM服务
    """
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.service_type = config.get('service_type', 'azure')  # 'azure' 或 'custom'
        self.retry_count = config.get('retry_count', 3)
        self.retry_delay = config.get('retry_delay', 1)
        self.default_model_config = config.get('model_config', {})

        # 设置代理
        self.proxies = None
        if config.get('use_proxy', False):
            http_proxy = os.getenv('http_proxy') or os.getenv('HTTP_PROXY')
            https_proxy = os.getenv('https_proxy') or os.getenv('HTTPS_PROXY')
            if http_proxy or https_proxy:
                self.proxies = {
                    'http': http_proxy,
                    'https': https_proxy
                }
                print(f"Using proxy: {self.proxies}")

        if self.service_type == 'azure':
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI library is required for Azure OpenAI. Please install it with: pip install openai")

            # 为Azure OpenAI客户端设置代理
            client_kwargs = {
                'api_key': config['api_key'],
                'api_version': config['api_version'],
                'azure_endpoint': config['endpoint']
            }
            if self.proxies:
                import httpx
                client_kwargs['http_client'] = httpx.Client(proxies=self.proxies)

            self.client = AzureOpenAI(**client_kwargs)
        elif self.service_type == 'custom':
            # 自定义vLLM服务，使用requests
            self.endpoint = config['endpoint']
            self.headers = config.get('headers', {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'User-Agent': 'LLM-Service/1.0.0'
            })
            # 添加自定义headers
            if 'custom_headers' in config:
                self.headers.update(config['custom_headers'])
        else:
            raise ValueError(f"Unsupported service_type: {self.service_type}")

    def _build_messages(self, system: str, prompt: str, model: str) -> List[Dict]:
        """
        构建消息列表
        
        Args:
            system: 系统提示词
            prompt: 用户提示词
            model: 模型名称
            
        Returns:
            List[Dict]: 消息列表
        """
        if model.startswith('o3'):
            return [
                {
                    "role": "developer",
                    "content": [
                        {
                            "type": "text",
                            "text": system
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
        else:
            return [
                {"role": "system", "content": system},
                {"role": "user", "content": prompt}
            ]

    def _build_request_params(
        self,
        messages: List[Dict[str, str]],
        model: str = "",
        model_config: Dict[str, Any] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        frequency_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        构建请求参数
        """
        if model_config is None:
            model_config = self.default_model_config
            
        # 获取模型配置
        if model.startswith('gpt-4'):
            # GPT-4系列使用max_tokens
            token_param = "max_tokens"
            token_value = max_tokens or model_config.get(token_param, 4000)
        else:
            # O系列使用max_completion_tokens
            token_param = "max_completion_tokens"
            token_value = max_tokens or model_config.get(token_param, 4000)
            
        # 构建基础参数
        request_params = {
            "model": model_config.get('deployment_name', model),
            "messages": messages,
            token_param: token_value,
            "timeout": timeout or model_config.get('timeout', 60),
            "stream": False
        }
        
        # 添加GPT-4特有的参数
        if model.startswith('gpt-4'):
            request_params.update({
                "top_p": top_p or model_config.get('top_p', 0.95),
                "temperature": temperature or model_config.get('temperature', 0.7),
                "frequency_penalty": frequency_penalty or model_config.get('frequency_penalty', 0.1),
                "presence_penalty": presence_penalty or model_config.get('presence_penalty', 0.0),
                "stop": stop or model_config.get('stop')
            })
            
        return request_params

    def _send_custom_request(self, request_data: Dict[str, Any]) -> str:
        """
        发送自定义vLLM服务请求

        Args:
            request_data: 请求数据

        Returns:
            str: 响应内容
        """
        response = requests.post(
            self.endpoint,
            headers=self.headers,
            json=request_data,
            proxies=self.proxies,
            timeout=self.default_model_config.get('timeout', 60)
        )
        response.raise_for_status()

        result = response.json()
        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']['content']
        else:
            raise ValueError(f"Invalid response format: {result}")

    def generate(
        self,
        prompt: str,
        system_prompt: str = "",
        model: str = "",
        model_config: Dict[str, Any] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        生成文本响应

        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            model: 模型名称
            model_config: 模型配置
            temperature: 温度参数
            max_tokens: 最大token数

        Returns:
            str: 生成的文本
        """
        for attempt in range(self.retry_count):
            try:
                if self.service_type == 'azure':
                    # Azure OpenAI请求
                    messages = self._build_messages(system_prompt, prompt, model)
                    request_params = self._build_request_params(
                        messages=messages,
                        model=model,
                        model_config=model_config,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        **kwargs
                    )

                    print(f"Sending request to Azure OpenAI (attempt {attempt + 1})...")
                    response = self.client.chat.completions.create(**request_params)
                    print(f"Azure OpenAI response received successfully")
                    return response.choices[0].message.content

                elif self.service_type == 'custom':
                    # 自定义vLLM服务请求
                    messages = []
                    if system_prompt:
                        messages.append({"role": "system", "content": system_prompt})
                    messages.append({"role": "user", "content": prompt})

                    request_data = {
                        "model": model or "",
                        "messages": messages,
                        "max_tokens": max_tokens or model_config.get('max_tokens', 256) if model_config else 256,
                        "temperature": temperature or model_config.get('temperature', 0.7) if model_config else 0.7
                    }

                    print(f"Sending request to custom vLLM service (attempt {attempt + 1})...")
                    result = self._send_custom_request(request_data)
                    print(f"Custom vLLM response received successfully")
                    return result

            except Exception as e:
                print(f"尝试第 {attempt + 1} 次生成响应失败: {str(e)}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(f"生成响应失败: {str(e)}")

    def generate_table_data(
        self,
        topic: str,
        rows: int = 5,
        cols: int = 4,
        model: str = "",
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成表格数据
        
        Args:
            topic: 表格主题
            rows: 行数
            cols: 列数
            model: 模型名称
            
        Returns:
            Dict: 包含表格数据的字典
        """
        from .prompts import get_table_generation_prompt
        
        prompt = get_table_generation_prompt(topic, rows, cols)
        
        response = self.generate(
            prompt=prompt,
            system_prompt="你是一个专业的数据生成助手，擅长创建结构化的表格数据。",
            model=model,
            **kwargs
        )
        
        try:
            # 尝试解析JSON响应
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()

            # 尝试修复不完整的JSON
            if not json_str.endswith('}'):
                # 如果JSON不完整，尝试修复
                lines = json_str.split('\n')
                fixed_lines = []

                for i, line in enumerate(lines):
                    stripped = line.strip()

                    # 跳过不完整的行（没有正确结束的）
                    if stripped.startswith('[') and not (stripped.endswith('],') or stripped.endswith(']')):
                        # 这是一个不完整的数组行，停止处理
                        break

                    fixed_lines.append(line)

                # 确保data数组正确关闭
                if fixed_lines and not fixed_lines[-1].strip().endswith(']'):
                    # 移除最后一行的逗号并添加数组结束符
                    if fixed_lines[-1].strip().endswith(','):
                        fixed_lines[-1] = fixed_lines[-1].rstrip().rstrip(',')
                    fixed_lines.append('    ]')

                # 确保有description字段
                if '"description"' not in json_str:
                    fixed_lines.append('    ,"description": "表格数据"')

                # 确保JSON对象正确关闭
                if not any(line.strip() == '}' for line in fixed_lines):
                    fixed_lines.append('}')

                json_str = '\n'.join(fixed_lines)

            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"Failed to parse LLM response as JSON: {e}")
            print(f"Response: {response}")
            raise ValueError(f"LLM返回的不是有效的JSON格式: {e}")
