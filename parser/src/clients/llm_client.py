import os
import requests

class LLMClient:
    def __init__(self, base_url='http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1'):
        self.base_url = base_url
        
        # 从环境变量读取配置
        custom_user_agent = os.getenv('CUSTOM_LLM_USER_AGENT', 'Apifox/1.0.0 (https://apifox.com)')
        custom_host = os.getenv('CUSTOM_LLM_HOST', 'kmd-api.kas.wps.cn')
        
        self.headers = {
            'User-Agent': custom_user_agent,
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': custom_host,
            'Connection': 'keep-alive'
        }

    def chat_completion(self, image_url, text_prompt, model="", max_tokens=4096, temperature=0):
        """
        发送聊天补全请求，支持图片和文本输入
        
        Args:
            image_url (str): 图片的URL地址
            text_prompt (str): 文本提示
            model (str): 模型名称，默认为空
            max_tokens (int): 最大token数，默认256
            temperature (float): 温度参数，默认0
            
        Returns:
            dict: API的响应结果
        """
        url = f"{self.base_url}/chat/completions"
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        },
                        {
                            "type": "text",
                            "text": text_prompt
                        }
                    ]
                }
            ]
        }
        # 从环境变量读取代理配置
        proxy_host = os.getenv('KS3_PROXY_HOST', '127.0.0.1')
        proxy_port = int(os.getenv('KS3_PROXY_PORT', '18899'))
        proxy=f"http://{proxy_host}:{proxy_port}"
        response = requests.post(url, headers=self.headers, json=payload, proxies={"http": proxy, "https": proxy})
        response.raise_for_status()  # 如果请求失败会抛出HTTPError
        
        return response.json()

    def text_completion(self, text_prompt, system_prompt="", model="", max_tokens=2000, temperature=0.8):
        """
        发送纯文本聊天补全请求
        
        Args:
            text_prompt (str): 文本提示
            system_prompt (str): 系统提示，默认为空
            model (str): 模型名称，默认为空
            max_tokens (int): 最大token数，默认2000
            temperature (float): 温度参数，默认0.8
            
        Returns:
            dict: API的响应结果
        """
        url = f"{self.base_url}/chat/completions"
        
        messages = []
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        messages.append({
            "role": "user", 
            "content": text_prompt
        })
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": messages
        }
        
        # 从环境变量读取代理配置
        proxy_host = os.getenv('KS3_PROXY_HOST', '127.0.0.1')
        proxy_port = int(os.getenv('KS3_PROXY_PORT', '18899'))
        proxy = f"http://{proxy_host}:{proxy_port}"
        
        response = requests.post(url, headers=self.headers, json=payload, proxies={"http": proxy, "https": proxy})
        response.raise_for_status()  # 如果请求失败会抛出HTTPError
        
        return response.json()

# 使用示例
if __name__ == "__main__":
    client = LLMClient()
    
    # 示例参数
    image_url = "https://test-platform-qa.ks3-cn-beijing.ksyuncs.com/luzeshu/test.png?KSSAccessKeyId=AKLTV3HRQgmmSnm94aEKSo5A&Expires=2110154838&Signature=Ef7ypUj%2BR8ulZs%2FJ41wPFA8Ixh8%3D"
    text_prompt = "帮我完整这张图片的内容"
    
    try:
        result = client.chat_completion(image_url, text_prompt)
        print("API响应:", result)
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    except ValueError as e:
        print(f"JSON解析失败: {e}")
