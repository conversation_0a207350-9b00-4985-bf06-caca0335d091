from typing import List, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class Dst:
    """DST结构体"""
    id: str
    dst_type: str
    content: Optional[List[str]] = None
    attributes: Optional[Dict[str, Any]] = None
    order: Optional[int] = None
    parent: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "dst_type": self.dst_type,
            "content": self.content,
            "attributes": self.attributes,
            "order": self.order,
            "parent": self.parent
        }


@dataclass
class DstImage:
    """DST图片结构体"""
    url: str
    chunk_ids: Optional[List[str]] = None
    image_type: Optional[str] = None
    
    @property
    def ChunkIds(self) -> Optional[List[str]]:
        return self.chunk_ids
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "url": self.url,
            "chunk_ids": self.chunk_ids,
            "image_type": self.image_type
        }


@dataclass
class DstChunk:
    """DST块结构体"""
    content: str
    chunk_id: str
    pre_chunk: Optional[str] = None
    next_chunk: Optional[str] = None
    label: Optional[str] = None
    content_embedding: Optional[List[float]] = None
    page_num: Optional[int] = None
    block: Optional[Dict[str, Any]] = None
    dsts: Optional[List[Dst]] = None
    image: Optional[List[DstImage]] = None
    
    @property
    def ChunkId(self) -> str:
        return self.chunk_id
    
    @property
    def Content(self) -> str:
        return self.content
    
    @Content.setter
    def Content(self, value: str):
        self.content = value
    
    @property
    def Dsts(self) -> Optional[List[Dst]]:
        return self.dsts
    
    @property
    def Image(self) -> Optional[List[DstImage]]:
        return self.image
    
    @Image.setter
    def Image(self, value: Optional[List[DstImage]]):
        self.image = value
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "content": self.content,
            "chunk_id": self.chunk_id,
            "pre_chunk": self.pre_chunk,
            "next_chunk": self.next_chunk,
            "label": self.label,
            "content_embedding": self.content_embedding,
            "page_num": self.page_num,
            "block": self.block,
            "dsts": [dst.to_dict() for dst in self.dsts] if self.dsts else [],
            "image": [img.to_dict() for img in self.image] if self.image else []
        }


@dataclass
class DstParseRes:
    """DST解析结果结构体"""
    chunks: List[DstChunk]
    page_size: int = 0
    word_count: int = 0
    width: int = 0
    height: int = 0
    image: Optional[List[DstImage]] = None
    is_scan: bool = False
    
    @property
    def Chunks(self) -> List[DstChunk]:
        return self.chunks
    
    @property
    def Image(self) -> Optional[List[DstImage]]:
        return self.image 