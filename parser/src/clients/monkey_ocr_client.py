import os
import time
import uuid
import json
import requests
from typing import Optional, Dict, List, Tuple
from .base_client import BaseClient

class MonkeyOCRClient(BaseClient):
    def __init__(self):
        super().__init__()
        self.base_url = "http://vlrlabmonkey.xyz:7685"
        self.session = requests.Session()
        self.session.verify = False  # Disable SSL verification as per the curl example
        # 读取超时配置，默认300秒（5分钟）
        self.timeout = int(os.getenv("MONKEY_OCR_TIMEOUT", "300"))

    def upload_file(self, image_path: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        上传文件到MonkeyOCR服务器
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (是否成功, 上传路径, 上传ID)
        """
        try:
            upload_id = str(uuid.uuid4())[:8]
            upload_url = f"{self.base_url}/gradio_api/upload?upload_id={upload_id}"
            
            with open(image_path, 'rb') as f:
                files = {'files': (os.path.basename(image_path), f, 'image/jpeg')}
                response = self.session.post(upload_url, files=files, proxies=self.proxies)
            
            # 详细的HTTP状态码检查
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result and isinstance(result, list) and len(result) > 0:
                        uploaded_path = result[0]
                        return True, uploaded_path, upload_id
                    else:
                        print(f"上传响应格式错误: {result}")
                        return False, None, None
                except json.JSONDecodeError as e:
                    print(f"上传响应JSON解析失败: {e}, 响应内容: {response.text[:200]}")
                    return False, None, None
            else:
                # 提供详细的HTTP错误信息
                error_msg = f"HTTP {response.status_code}"
                if response.reason:
                    error_msg += f" {response.reason}"
                
                # 尝试获取响应内容
                try:
                    response_text = response.text[:200] if response.text else "无响应内容"
                    error_msg += f" - 响应: {response_text}"
                except:
                    error_msg += " - 无法读取响应内容"
                
                print(f"上传文件失败 ({image_path}): {error_msg}")
                return False, None, None
            
        except requests.exceptions.ConnectionError as e:
            print(f"上传文件连接失败 ({image_path}): {str(e)}")
            return False, None, None
        except requests.exceptions.Timeout as e:
            print(f"上传文件超时 ({image_path}): {str(e)}")
            return False, None, None
        except requests.exceptions.RequestException as e:
            print(f"上传文件请求异常 ({image_path}): {str(e)}")
            return False, None, None
        except Exception as e:
            print(f"上传文件未知错误 ({image_path}): {str(e)}")
            return False, None, None

    def wait_upload_completion(self, upload_id: str, timeout: int = 30) -> bool:
        """
        等待上传完成
        
        Args:
            upload_id: 上传ID
            timeout: 超时时间
            
        Returns:
            bool: 是否上传完成
        """
        try:
            start_time = time.time()
            progress_url = f"{self.base_url}/gradio_api/upload_progress?upload_id={upload_id}"
            
            while time.time() - start_time < timeout:
                response = self.session.get(progress_url, proxies=self.proxies, stream=True)
                
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            line = line.decode('utf-8')
                            if line.startswith('data: '):
                                try:
                                    data = json.loads(line[6:])
                                    if data.get('msg') == 'done':
                                        return True
                                except json.JSONDecodeError as e:
                                    print(f"等待上传完成JSON解析失败: {e}, 内容: {line[:100]}")
                                    continue
                else:
                    print(f"等待上传完成HTTP错误: {response.status_code} {response.reason}")
                    return False
                                    
                time.sleep(0.1)
            
            print(f"等待上传完成超时 (upload_id={upload_id}, timeout={timeout}s)")
            return False
        except requests.exceptions.ConnectionError as e:
            print(f"等待上传完成连接失败 (upload_id={upload_id}): {str(e)}")
            return False
        except requests.exceptions.Timeout as e:
            print(f"等待上传完成请求超时 (upload_id={upload_id}): {str(e)}")
            return False
        except requests.exceptions.RequestException as e:
            print(f"等待上传完成请求异常 (upload_id={upload_id}): {str(e)}")
            return False
        except Exception as e:
            print(f"等待上传完成未知错误 (upload_id={upload_id}): {str(e)}")
            return False

    def register_file_to_session(self, uploaded_path: str, image_path: str, session_hash: str) -> bool:
        """
        将文件注册到会话中
        
        Args:
            uploaded_path: 上传后的文件路径
            image_path: 原始图片路径
            session_hash: 会话哈希
            
        Returns:
            bool: 是否注册成功
        """
        try:
            file_info = {
                "path": uploaded_path,
                "url": f"{self.base_url}/gradio_api/file={uploaded_path}",
                "orig_name": os.path.basename(image_path),
                "size": os.path.getsize(image_path),
                "mime_type": "image/jpeg",
                "meta": {"_type": "gradio.FileData"}
            }

            register_payload = {
                "data": [file_info, None],
                "event_data": None,
                "fn_index": 0,
                "trigger_id": 7,
                "session_hash": session_hash
            }

            register_response = self.session.post(
                f"{self.base_url}/gradio_api/queue/join",
                json=register_payload,
                proxies=self.proxies
            )
            
            if register_response.status_code == 200:
                return True
            else:
                error_msg = f"HTTP {register_response.status_code}"
                if register_response.reason:
                    error_msg += f" {register_response.reason}"
                try:
                    response_text = register_response.text[:200] if register_response.text else "无响应内容"
                    error_msg += f" - 响应: {response_text}"
                except:
                    error_msg += " - 无法读取响应内容"
                
                print(f"注册文件到会话失败 (session={session_hash}): {error_msg}")
                return False
            
        except requests.exceptions.ConnectionError as e:
            print(f"注册文件到会话连接失败 (session={session_hash}): {str(e)}")
            return False
        except requests.exceptions.Timeout as e:
            print(f"注册文件到会话超时 (session={session_hash}): {str(e)}")
            return False
        except requests.exceptions.RequestException as e:
            print(f"注册文件到会话请求异常 (session={session_hash}): {str(e)}")
            return False
        except Exception as e:
            print(f"注册文件到会话未知错误 (session={session_hash}): {str(e)}")
            return False

    def submit_processing_request(self, session_hash: str, prompt: str, fn_index: int = 4, trigger_id: int = 11) -> bool:
        """
        提交处理请求
        
        Args:
            session_hash: 会话哈希
            prompt: 处理提示语，None表示parse模式（data=[null,null]）
            fn_index: 功能索引
            trigger_id: 触发器ID
            
        Returns:
            bool: 是否提交成功
        """
        try:
            # 根据curls.txt的配置构造data数组
            if prompt is None:
                # parse模式：fn_index=3，data=[null,null] 
                data = [None, None]
            else:
                # table模式：fn_index=4，data=[prompt,null]
                data = [prompt, None]

            payload = {
                "data": data,
                "event_data": None,
                "fn_index": fn_index,
                "trigger_id": trigger_id,
                "session_hash": session_hash
            }

            response = self.session.post(
                f"{self.base_url}/gradio_api/queue/join",
                json=payload,
                proxies=self.proxies
            )

            if response.status_code == 200:
                return True
            else:
                error_msg = f"HTTP {response.status_code}"
                if response.reason:
                    error_msg += f" {response.reason}"
                try:
                    response_text = response.text[:200] if response.text else "无响应内容"
                    error_msg += f" - 响应: {response_text}"
                except:
                    error_msg += " - 无法读取响应内容"
                
                print(f"提交处理请求失败 (session={session_hash}, fn_index={fn_index}): {error_msg}")
                return False
            
        except requests.exceptions.ConnectionError as e:
            print(f"提交处理请求连接失败 (session={session_hash}): {str(e)}")
            return False
        except requests.exceptions.Timeout as e:
            print(f"提交处理请求超时 (session={session_hash}): {str(e)}")
            return False
        except requests.exceptions.RequestException as e:
            print(f"提交处理请求异常 (session={session_hash}): {str(e)}")
            return False
        except Exception as e:
            print(f"提交处理请求未知错误 (session={session_hash}): {str(e)}")
            return False

    def get_processing_result(self, session_hash: str) -> Optional[Dict]:
        """
        获取处理结果

        Args:
            session_hash: 会话哈希

        Returns:
            Optional[Dict]: 处理结果
        """
        try:
            start_time = time.time()
            response = self.session.get(
                f"{self.base_url}/gradio_api/queue/data?session_hash={session_hash}",
                proxies=self.proxies,
                stream=True,
                timeout=self.timeout
            )

            if response.status_code != 200:
                return None

            result = {
                'processed_image_url': None,
                'text_content': [],
                'html': None,
                'is_timeout': False,
                'processing_time': 0
            }

            # 跟踪处理状态
            process_completed_count = 0
            has_meaningful_result = False

            for line in response.iter_lines():
                # 检查超时
                current_time = time.time()
                if current_time - start_time > self.timeout:
                    result['is_timeout'] = True
                    result['processing_time'] = current_time - start_time
                    result['text_content'] = [f'MonkeyOCR处理超时 ({self.timeout}秒)']
                    result['html'] = f'MonkeyOCR处理超时 ({self.timeout}秒)'
                    return result

                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])

                            if data.get('msg') == 'process_completed':
                                process_completed_count += 1
                                output = data.get('output', {})
                                output_data = output.get('data', [])

                                print(f"收到第 {process_completed_count} 个 process_completed 事件")
                                print(f"输出数据项数量: {len(output_data)}")
                                # 只在调试模式下打印详细信息
                                # for i, item in enumerate(output_data):
                                #     if isinstance(item, str):
                                #         print(f"  项 {i}: 字符串 (长度: {len(item)}) - {item[:100]}...")
                                #     elif isinstance(item, dict):
                                #         print(f"  项 {i}: 字典 - {item}")
                                #     else:
                                #         print(f"  项 {i}: {type(item)} - {item}")

                                # 检查是否包含有意义的结果
                                temp_text_content = []
                                temp_processed_image_url = None

                                for item in output_data:
                                    if isinstance(item, dict):
                                        # 检查是否是结果文件URL
                                        if 'url' in item and (item.get('url', '').endswith('.md') or 'chat_response' in item.get('url', '')):
                                            temp_processed_image_url = item['url']
                                            has_meaningful_result = True
                                        # 检查是否是包含结果文件的update对象
                                        elif 'value' in item and isinstance(item['value'], dict) and 'url' in item['value']:
                                            if item['value'].get('url', '').endswith('.md') or 'chat_response' in item['value'].get('url', ''):
                                                temp_processed_image_url = item['value']['url']
                                                has_meaningful_result = True
                                    elif isinstance(item, str) and item.strip() and not item.startswith('Please upload'):
                                        # 检查是否是有意义的内容
                                        # 接受HTML表格内容（以<table>开头）或其他非UI元素的内容
                                        if (item.startswith('<table>') or
                                            (not item.startswith('<div') and
                                             not item.startswith('<span') and
                                             'Click' not in item and
                                             'kbd' not in item and
                                             'page_info_box' not in item and
                                             len(item) > 10)):  # 确保不是空字符串或很短的无意义内容
                                            temp_text_content.append(item)
                                            has_meaningful_result = True
                                            print(f"找到有意义的文本内容 (长度: {len(item)}): {item[:200]}...")

                                # 如果这次有有意义的结果，更新result
                                if has_meaningful_result:
                                    if temp_processed_image_url:
                                        result['processed_image_url'] = temp_processed_image_url
                                    if temp_text_content:
                                        result['text_content'].extend(temp_text_content)

                                # 第一个process_completed通常是文件上传确认，第二个才是实际结果
                                # 只有在第二个process_completed且有有意义结果时才返回
                                if process_completed_count >= 2:
                                    # 如果有结果文件URL，下载并读取内容
                                    if temp_processed_image_url:
                                        try:
                                            print(f"下载结果文件: {temp_processed_image_url}")
                                            # 等待一下确保文件写入完成
                                            time.sleep(1)
                                            file_response = self.session.get(temp_processed_image_url, proxies=self.proxies)
                                            if file_response.status_code == 200:
                                                file_content = file_response.text
                                                print(f"结果文件内容长度: {len(file_content)}")
                                                if file_content.strip():
                                                    result['text_content'] = [file_content]
                                                    result['processed_image_url'] = temp_processed_image_url
                                                    has_meaningful_result = True
                                                    print(f"成功从文件获取内容: {file_content[:200]}...")
                                                else:
                                                    print("结果文件为空，可能需要等待更长时间")
                                        except Exception as e:
                                            print(f"下载结果文件失败: {e}")

                                    # 如果有文本内容或下载的文件内容，返回结果
                                    if result['text_content'] or temp_text_content:
                                        all_content = result['text_content'] + temp_text_content
                                        result['text_content'] = all_content
                                        result['html'] = '\n'.join(all_content)
                                        result['processing_time'] = time.time() - start_time
                                        return result
                                    elif has_meaningful_result:
                                        # 如果这次有结果但内容为空，也返回
                                        result['text_content'] = ['No MonkeyOCR result available']
                                        result['html'] = 'No MonkeyOCR result available'
                                        result['processing_time'] = time.time() - start_time
                                        return result

                            elif data.get('msg') == 'close_stream':
                                print("收到 close_stream 事件")
                                break
                        except Exception as e:
                            print(f"解析响应数据失败: {e}")
                            continue

            # 如果循环结束但没有返回，检查是否有结果
            if result['text_content'] or result['processed_image_url']:
                if result['text_content']:
                    result['html'] = '\n'.join(result['text_content'])
                else:
                    result['text_content'].append('No MonkeyOCR result available')
                    result['html'] = 'No MonkeyOCR result available'
            else:
                result['text_content'].append('No MonkeyOCR result available')
                result['html'] = 'No MonkeyOCR result available'

            result['processing_time'] = time.time() - start_time
            return result
        except requests.exceptions.Timeout:
            processing_time = time.time() - start_time
            return {
                'processed_image_url': None,
                'text_content': [f'MonkeyOCR请求超时 ({self.timeout}秒)'],
                'html': f'MonkeyOCR请求超时 ({self.timeout}秒)',
                'is_timeout': True,
                'processing_time': processing_time
            }
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'processed_image_url': None,
                'text_content': [f'MonkeyOCR处理失败: {str(e)}'],
                'html': f'MonkeyOCR处理失败: {str(e)}',
                'is_timeout': False,
                'processing_time': processing_time
            }

    def download_processed_image(self, url: str, save_path: str) -> bool:
        """
        下载处理后的图片
        
        Args:
            url: 图片URL
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        try:
            response = self.session.get(url, proxies=self.proxies)
            
            if response.status_code == 200:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                return True
            else:
                error_msg = f"HTTP {response.status_code}"
                if response.reason:
                    error_msg += f" {response.reason}"
                try:
                    response_text = response.text[:200] if response.text else "无响应内容"
                    error_msg += f" - 响应: {response_text}"
                except:
                    error_msg += " - 无法读取响应内容"
                
                print(f"下载图片HTTP错误 ({url} -> {save_path}): {error_msg}")
                return False
                
        except requests.exceptions.ConnectionError as e:
            print(f"下载图片连接失败 ({url}): {str(e)}")
            return False
        except requests.exceptions.Timeout as e:
            print(f"下载图片超时 ({url}): {str(e)}")
            return False
        except requests.exceptions.RequestException as e:
            print(f"下载图片请求异常 ({url}): {str(e)}")
            return False
        except IOError as e:
            print(f"下载图片文件写入失败 ({save_path}): {str(e)}")
            return False
        except Exception as e:
            print(f"下载图片未知错误 ({url}): {str(e)}")
            return False 