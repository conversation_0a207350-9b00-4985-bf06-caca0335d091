import os
import requests
import hashlib
import logging
import json
from typing import Dict, List, Optional

# 获取模块级别的logger
logger = logging.getLogger(__name__)


class KdocsClient:
    """独立的山文档客户端，包含完整的自有配置"""
    
    def __init__(self, default_cookie: str):
        """
        初始化独立客户端
        :param default_cookie: 金山文档专用cookie
        """
        self.default_cookie = default_cookie
        self._proxies = self._init_proxies()

    def _init_proxies(self) -> Optional[Dict]:
        """金山文档专属代理配置"""
        http_proxy = os.environ.get("KDOCS_HTTP_PROXY") or os.environ.get("http_proxy")
        https_proxy = os.environ.get("KDOCS_HTTPS_PROXY") or os.environ.get("https_proxy")
        
        proxies = {}
        if http_proxy:
            proxies["http"] = http_proxy
        if https_proxy:
            proxies["https"] = https_proxy
        return proxies or None

    def _make_headers(self, referer: str, cookie: Optional[str] = None) -> Dict:
        """生成金山文档专用请求头"""
        cookie_to_use = cookie or self.default_cookie
        return {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Cookie": cookie_to_use,
            "Pragma": "no-cache",
            "Referer": referer,
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin"
        }

    def _extract_csrf_token(self, cookie: str) -> Optional[str]:
        """从cookie中提取csrf token"""
        for item in cookie.split(';'):
            if 'csrf=' in item:
                return item.split('=')[1].strip()
        return None

    def list_files(self,
                 group_id: str,
                 parent_id: str,
                 company_id: str,
                 cookie: Optional[str] = None,
                 count: int = 20,
                 **kwargs) -> List[Dict]:
        """
        金山文档专属文件列表获取
        :param cookie: 可选覆盖默认cookie
        """
        params = {
            "parentid": parent_id,
            "count": count,
            "order": "DESC",
            "orderby": "mtime",
            **kwargs
        }
        
        referer = f"https://365.kdocs.cn/space/{company_id}/{group_id}/{parent_id}"
        
        response = requests.get(
            f"https://365.kdocs.cn/3rd/drive/api/v5/groups/{group_id}/files",
            headers=self._make_headers(referer, cookie),
            params=params,
            proxies=self._proxies
        )
        response.raise_for_status()
        data = response.json()
        if data.get("result") != "ok":
            raise ValueError(f"API error: {data}")
        return data.get("files", [])

    def upload_file(self,
                   local_file_path: str,
                   group_id: str,
                   parent_id: str,
                   company_id: str,
                   cookie: Optional[str] = None) -> Dict:
        """
        上传本地文件到金山文档
        :param local_file_path: 本地文件路径
        :param group_id: 文件组ID
        :param parent_id: 父文件夹ID
        :param company_id: 企业ID
        :param cookie: 可选覆盖默认cookie
        :return: 上传结果
        """
        if not os.path.exists(local_file_path):
            raise FileNotFoundError(f"File not found: {local_file_path}")
        
        file_size = os.path.getsize(local_file_path)
        file_name = os.path.basename(local_file_path)
        
        # 1. 先进行 pre_check
        pre_check_url = "https://365.kdocs.cn/3rd/drive/api/v5/files/upload/pre_check"
        pre_check_params = {
            "file_name": file_name,
            "group_id": group_id,
            "parent_id": parent_id
        }
        referer = f"https://365.kdocs.cn/space/{company_id}/{group_id}/{parent_id}"
        pre_check_headers = self._make_headers(referer, cookie)

        pre_check_resp = requests.get(
            pre_check_url,
            headers=pre_check_headers,
            params=pre_check_params,
            proxies=self._proxies
        )
        pre_check_data = pre_check_resp.json()
        
        # 处理文件名重复的情况
        if pre_check_data.get("result") == "fileNameDuplicated":
            logger.info(f"文件 {file_name} 已存在，跳过上传")
            return {"result": "skipped", "reason": "fileNameDuplicated", "msg": pre_check_data.get("msg", "")}
            
        # 如果不是文件名重复，则检查其他错误
        pre_check_resp.raise_for_status()
        
        if pre_check_data.get("result") != "ok":
            raise ValueError(f"Pre-check failed: {pre_check_data}")
        
        logger.debug(f"开始上传文件: {local_file_path}")
        logger.debug(f"文件大小: {file_size} 字节")
        logger.debug(f"文件名: {file_name}")
        
        # 计算文件MD5
        with open(local_file_path, 'rb') as f:
            file_md5 = hashlib.md5(f.read()).hexdigest()
        logger.debug(f"文件MD5: {file_md5}")
        
        # 获取文件MIME类型
        mime_type = self._get_mime_type(file_name)
        logger.debug(f"MIME类型: {mime_type}")
        
        # 获取csrf token
        cookie_to_use = cookie or self.default_cookie
        csrf_token = self._extract_csrf_token(cookie_to_use)
        logger.debug(f"CSRF Token: {csrf_token}")
        
        # 构建创建上传请求的参数
        create_upload_data = {
            "groupid": group_id,
            "parentid": parent_id,
            "parent_path": [],
            "size": file_size,
            "name": file_name,
            "req_by_internal": False,
            "client_stores": "ks3,ks3sh",
            "contenttype": mime_type,
            "startswithfilename": file_name,
            "successactionstatus": 201,
            "group_id": group_id,
            "parent_id": parent_id,
            "file_id": 0,
            "with_rapid": True,
            "tried_store": ["ks3,ks3sh"],
            "md5": file_md5
        }
        
        # 如果有csrf token，添加到请求中
        if csrf_token:
            create_upload_data["csrfmiddlewaretoken"] = csrf_token
        
        referer = f"https://365.kdocs.cn/space/{company_id}/{group_id}/{parent_id}"
        logger.debug(f"Referer: {referer}")
        
        # 创建上传请求的headers
        create_headers = {
            "host": "365.kdocs.cn",
            "sec-ch-ua-platform": "\"macOS\"",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "content-type": "application/json",
            "sec-ch-ua-mobile": "?0",
            "accept": "*/*",
            "origin": "https://365.kdocs.cn",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": referer,
            "accept-language": "zh-CN,zh;q=0.9",
            "cookie": cookie_to_use,
            "priority": "u=1, i"
        }
        logger.debug("创建上传请求头:")
        logger.debug(create_headers)
        logger.debug("创建上传请求体:")
        logger.debug(create_upload_data)
        
        # 创建上传请求
        response = requests.put(
            "https://365.kdocs.cn/3rd/drive/api/v5/files/upload/create_update",
            headers=create_headers,
            data=json.dumps(create_upload_data, separators=(',', ':')),
            proxies=self._proxies
        )
        response.raise_for_status()
        upload_info = response.json()
        
        logger.debug("创建上传请求响应:")
        logger.debug(upload_info)
        
        if upload_info.get("result") != "ok":
            raise ValueError(f"Failed to create upload request: {upload_info}")
        
        # 构建文件上传请求的headers
        upload_headers = {
            **upload_info["request"]["headers"],
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Origin": "https://365.kdocs.cn",
            "Pragma": "no-cache",
            "Referer": referer,
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "Cookie": cookie_to_use
        }
        logger.debug("文件上传请求头:")
        logger.debug(upload_headers)
        
        # 执行实际的文件上传
        logger.debug(f"开始上传文件到URL: {upload_info['url']}")
        with open(local_file_path, 'rb') as f:
            upload_response = requests.put(
                upload_info["url"],
                headers=upload_headers,
                data=f,
                proxies=self._proxies
            )
            upload_response.raise_for_status()
            
        logger.debug("文件上传响应头:")
        logger.debug(dict(upload_response.headers))
        
        # 从响应中获取key
        key = None
        if upload_response.headers.get('x-obs-save-key'):
            key = upload_response.headers['x-obs-save-key']
        else:
            # 尝试从响应体获取newfilename
            try:
                response_json = upload_response.json()
                key = response_json.get('newfilename')
            except:
                pass
        
        if not key:
            raise ValueError("Failed to get file key from upload response")
            
        # 创建文件对象
        create_file_data = {
            "key": key,
            "groupid": group_id,
            "parentid": parent_id,
            "parent_path": [],
            "name": file_name,
            "isUpNewVer": False,
            "etag": upload_response.headers.get('ETag', '').strip('"'),
            "store": upload_info.get('store', 'obscn'),
            "size": file_size,
            "sha1": key,  # 使用key作为sha1
            "apiErrorInfo": None
        }
        
        if csrf_token:
            create_file_data["csrfmiddlewaretoken"] = csrf_token
            
        # 创建文件对象的headers
        create_file_headers = {
            "host": "365.kdocs.cn",
            "sec-ch-ua-platform": "\"macOS\"",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "content-type": "application/json",
            "sec-ch-ua-mobile": "?0",
            "accept": "*/*",
            "origin": "https://365.kdocs.cn",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": referer,
            "accept-language": "zh-CN,zh;q=0.9",
            "cookie": cookie_to_use,
            "priority": "u=1, i"
        }
        
        # 创建文件对象
        create_file_response = requests.post(
            "https://365.kdocs.cn/3rd/drive/api/v5/files/file",
            headers=create_file_headers,
            json=create_file_data,
            proxies=self._proxies
        )
        create_file_response.raise_for_status()
        create_file_result = create_file_response.json()
        
        if create_file_result.get("result") != "ok":
            raise ValueError(f"Failed to create file object: {create_file_result}")
            
        return create_file_result
        
    def _get_mime_type(self, filename: str) -> str:
        """获取文件的MIME类型"""
        ext_to_mime = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.txt': 'text/plain',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif'
        }
        ext = os.path.splitext(filename)[1].lower()
        return ext_to_mime.get(ext, 'application/octet-stream')

    def upload_file_v2(self,
                    local_file_path: str,
                    drive_id: str,
                    parent_id: str,
                    cookie: Optional[str] = None) -> Dict:
        """
        使用新的三步上传流程上传文件到金山文档
        :param local_file_path: 本地文件路径
        :param drive_id: 文件组ID (同group_id)
        :param parent_id: 父文件夹ID
        :param cookie: 可选覆盖默认cookie
        :return: 上传结果
        """
        if not os.path.exists(local_file_path):
            raise FileNotFoundError(f"File not found: {local_file_path}")
        
        file_size = os.path.getsize(local_file_path)
        file_name = os.path.basename(local_file_path)
        
        logger.debug(f"开始上传文件 (v2): {local_file_path}")
        logger.debug(f"文件大小: {file_size} 字节")
        logger.debug(f"文件名: {file_name}")
        
        # 计算文件MD5
        with open(local_file_path, 'rb') as f:
            file_md5 = hashlib.md5(f.read()).hexdigest()
        logger.debug(f"文件MD5: {file_md5}")
        
        # 第1步：请求文件上传信息
        request_upload_data = {
            "name": file_name,
            "size": file_size,
            "hashes": [
                {
                    "type": "md5",
                    "sum": file_md5
                }
            ],
            "parent_path": [],
            "internal": False,
            "display_size": file_size,
            "method": "put",
            "on_name_conflict": "auto_rename",
            "on_parent_path_conflict": "auto_rename"
        }
        
        cookie_to_use = cookie or self.default_cookie
        headers = {
            **self._make_headers(f"https://www.kdocs.cn", cookie_to_use),
            "Content-Type": "application/json"
        }
        
        # 请求上传信息
        response = requests.post(
            f"https://api.wps.cn/v7/drives/{drive_id}/files/{parent_id}/request_upload",
            headers=headers,
            json=request_upload_data,
            proxies=self._proxies
        )
        response.raise_for_status()
        upload_info = response.json()
        
        if upload_info.get("code") != 0:
            raise ValueError(f"Failed to get upload info: {upload_info}")
        
        store_request = upload_info["data"]["store_request"]
        upload_id = upload_info["data"]["upload_id"]
        request_id = upload_info["data"]["id"]
        
        # 第2步：上传文件到云存储
        # 构建上传请求头
        upload_headers = {}
        for header in store_request["headers"]:
            upload_headers[header["name"]] = header["value"]
        
        # 执行文件上传
        logger.debug(f"开始上传文件到URL: {store_request['url']}")
        with open(local_file_path, 'rb') as f:
            upload_response = requests.put(
                store_request["url"],
                headers=upload_headers,
                data=f,
                proxies=self._proxies
            )
            upload_response.raise_for_status()
        
        # 第3步：提交上传完成信息
        store_response = []
        # 添加响应头信息
        for key in upload_info["data"]["store_response_keys"]["header_keys"]:
            if key in upload_response.headers:
                store_response.append({
                    "name": key,
                    "value": upload_response.headers[key]
                })
        
        commit_data = {
            "id": request_id,
            "upload_id": upload_id,
            "store_response": store_response
        }
        
        # 提交上传完成
        commit_response = requests.post(
            f"https://api.wps.cn/v7/drives/{drive_id}/files/{parent_id}/commit_upload",
            headers=headers,
            json=commit_data,
            proxies=self._proxies
        )
        commit_response.raise_for_status()
        commit_result = commit_response.json()
        
        if commit_result.get("code") != 0:
            raise ValueError(f"Failed to commit upload: {commit_result}")
        
        return commit_result

    def delete_file(self,
                   group_id: str,
                   file_id: str,
                   company_id: str,
                   cookie: Optional[str] = None) -> Dict:
        """
        删除金山文档中的文件
        :param group_id: 文件组ID
        :param file_id: 要删除的文件ID
        :param company_id: 企业ID
        :param cookie: 可选覆盖默认cookie
        :return: 删除结果
        """
        cookie_to_use = cookie or self.default_cookie
        csrf_token = self._extract_csrf_token(cookie_to_use)
        if not csrf_token:
            raise ValueError("CSRF token not found in cookie")

        referer = f"https://365.kdocs.cn/space/{company_id}/{group_id}"
        
        headers = {
            **self._make_headers(referer, cookie_to_use),
            "Content-Type": "application/json",
            "Origin": "https://365.kdocs.cn"
        }

        data = {
            "csrfmiddlewaretoken": csrf_token
        }

        response = requests.delete(
            f"https://365.kdocs.cn/3rd/drive/api/v3/groups/{group_id}/files/{file_id}",
            headers=headers,
            json=data,
            proxies=self._proxies
        )
        response.raise_for_status()
        result = response.json()
        
        if result.get("result") != "ok":
            raise ValueError(f"Failed to delete file: {result}")
            
        return result

    def create_folder(self,
                     group_id: str,
                     parent_id: str,
                     folder_name: str,
                     company_id: str,
                     cookie: Optional[str] = None) -> Dict:
        """
        在金山文档中创建文件夹
        :param group_id: 文件组ID
        :param parent_id: 父文件夹ID
        :param folder_name: 文件夹名称
        :param company_id: 企业ID
        :param cookie: 可选覆盖默认cookie
        :return: 创建结果，如果文件夹已存在则返回现有文件夹信息
        """
        cookie_to_use = cookie or self.default_cookie
        csrf_token = self._extract_csrf_token(cookie_to_use)
        if not csrf_token:
            raise ValueError("CSRF token not found in cookie")

        referer = f"https://365.kdocs.cn/space/{company_id}/{group_id}/{parent_id}"
        
        headers = {
            "host": "365.kdocs.cn",
            "pragma": "no-cache",
            "cache-control": "no-cache",
            "sec-ch-ua-platform": "\"macOS\"",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "content-type": "application/json",
            "sec-ch-ua-mobile": "?0",
            "accept": "*/*",
            "origin": "https://365.kdocs.cn",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": referer,
            "accept-language": "zh-CN,zh;q=0.9",
            "cookie": cookie_to_use,
            "priority": "u=1, i"
        }

        data = {
            "groupid": int(group_id),
            "parentid": int(parent_id),
            "name": folder_name,
            "parsed": True,
            "owner": True,
            "csrfmiddlewaretoken": csrf_token
        }

        response = requests.post(
            "https://365.kdocs.cn/3rd/drive/api/v5/files/folder",
            headers=headers,
            json=data,
            proxies=self._proxies
        )
        
        # 处理文件夹名冲突的情况
        if response.status_code == 403:
            try:
                error_data = response.json()
                if error_data.get("result") == "fileNameConflict":
                    logger.info(f"文件夹 '{folder_name}' 已存在，查找现有文件夹")
                    # 查找现有文件夹
                    existing_folder = self.find_folder_by_name(group_id, parent_id, folder_name, company_id, cookie)
                    if existing_folder:
                        logger.info(f"找到现有文件夹: {existing_folder['fname']} (ID: {existing_folder['id']})")
                        return {"result": "exists", "folder": existing_folder}
                    else:
                        raise ValueError(f"文件夹 '{folder_name}' 冲突但无法找到现有文件夹")
            except (ValueError, KeyError):
                pass
        
        response.raise_for_status()
        result = response.json()
        
        if result.get("result") == "ok":
            logger.info(f"文件夹 '{folder_name}' 创建成功 (ID: {result.get('id')})")
            return {"result": "created", "folder": result}
        else:
            raise ValueError(f"Failed to create folder: {result}")

    def find_folder_by_name(self,
                           group_id: str,
                           parent_id: str,
                           folder_name: str,
                           company_id: str,
                           cookie: Optional[str] = None) -> Optional[Dict]:
        """
        根据名称查找文件夹
        :param group_id: 文件组ID
        :param parent_id: 父文件夹ID
        :param folder_name: 文件夹名称
        :param company_id: 企业ID
        :param cookie: 可选覆盖默认cookie
        :return: 文件夹信息，如果未找到则返回None
        """
        try:
            files = self.list_files(group_id, parent_id, company_id, cookie, count=100)
            for file in files:
                if file.get("ftype") == "folder" and file.get("fname") == folder_name:
                    return file
            return None
        except Exception as e:
            logger.error(f"查找文件夹失败: {str(e)}")
            return None

    def get_or_create_test_folder(self,
                                 group_id: str,
                                 parent_id: str,
                                 test_name: str,
                                 company_id: str,
                                 cookie: Optional[str] = None) -> Dict:
        """
        获取或创建测试集文件夹
        :param group_id: 文件组ID
        :param parent_id: 父文件夹ID
        :param test_name: 测试集名称
        :param company_id: 企业ID
        :param cookie: 可选覆盖默认cookie
        :return: 文件夹信息（包含ID）
        """
        logger.info(f"获取或创建测试集文件夹: {test_name}")
        
        # 首先尝试查找现有文件夹
        existing_folder = self.find_folder_by_name(group_id, parent_id, test_name, company_id, cookie)
        if existing_folder:
            logger.info(f"找到现有测试集文件夹: {test_name} (ID: {existing_folder['id']})")
            return {"result": "exists", "folder": existing_folder}
        
        # 如果不存在，则创建新文件夹
        logger.info(f"创建新测试集文件夹: {test_name}")
        return self.create_folder(group_id, parent_id, test_name, company_id, cookie)