import os
import json
import hashlib
import uuid
from datetime import datetime
import requests
from typing import Dict, Optional
from .base_client import BaseClient


class DstClient(BaseClient):
    """DST文档解析服务客户端"""
    
    def __init__(self, 
                 ak: str, 
                 sk: str, 
                 host: str = "kna_mock.localserver",
                 # host: str = "insight-ai-gray.kna.wps.cn",
                 port: int = 19913,
                 default_cookie: Optional[str] = None,
                 request_timeout: int = 120,
                 connect_timeout: int = 30):
        """
        初始化DST客户端
        :param ak: DST专属AK
        :param sk: DST专属SK
        :param host: DST服务地址
        :param port: DST服务端口
        :param default_cookie: DST专用cookie
        :param request_timeout: 请求超时时间(秒)
        :param connect_timeout: 连接超时时间(秒)
        """
        super().__init__()
        self.ak = ak
        self.sk = sk
        self.host = host
        self.port = port
        self.default_cookie = default_cookie
        self.base_url = f"http://{host}/api/v1/aidocs_dst"
        
        # 超时配置
        self.request_timeout = request_timeout
        self.connect_timeout = connect_timeout
        
        # 创建会话以复用连接
        self.session = requests.Session()
        
        # 配置会话的适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def _make_headers(self, authorization: str, content_md5: str, date_str: str, request_id: str, content_type: str = "application/json", cookie: Optional[str] = None, content_length: int = None) -> Dict:
        """生成DST专用请求头"""
        cookie_to_use = cookie or self.default_cookie
        headers = {
            "Host": f"127.0.0.1:{self.port}",
            "User-Agent": "Go-http-client/1.1",
            "Authorization": authorization,
            "Client-Request-Id": request_id,
            "Content-MD5": content_md5,
            "Content-Type": content_type,
            "Date": date_str,
            "Pcv-Client-Type": "ai_docs",
            "X-Action-Id": request_id,
            "X-Request-Id": request_id,
            "X-Span-Id": self._generate_span_id(),
            "X-Trace-Id": self._generate_trace_id(),
            "Connection": "keep-alive"
        }
        
        # 明确设置Content-Length以防止代理截断body
        if content_length is not None:
            headers["Content-Length"] = str(content_length)
        
        if cookie_to_use:
            headers["Cookie"] = f"wps_sid={cookie_to_use}"
        return headers

    def _generate_auth(self, content_md5: str, date_str: str, content_type: str) -> str:
        """DST专属签名算法 - 参考Go版本的Wps2Sign"""
        sign_str = f"{self.sk}{content_md5}{content_type}{date_str}"
        signature = hashlib.sha1(sign_str.encode()).hexdigest()
        return f"WPS-2:{self.ak}:{signature}"
    
    def wps2_sign(self, uri: str, body: bytes = None, content_type: str = "application/json") -> tuple:
        """完全参考Go版本的Wps2Sign实现"""
        md5_hash = hashlib.md5()
        
        if body is None:
            # 如果body为None，则MD5计算URI
            md5_hash.update(uri.encode())
        else:
            # 否则MD5计算body内容
            md5_hash.update(body)
        
        content_md5 = md5_hash.hexdigest()
        date_str = datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")
        
        # SHA1签名：sk + contentMd5 + contentType + date
        sign_str = f"{self.sk}{content_md5}{content_type}{date_str}"
        signature = hashlib.sha1(sign_str.encode()).hexdigest()
        sig = f"WPS-2:{self.ak}:{signature}"
        
        return sig, date_str, content_md5
    
    def _generate_span_id(self) -> str:
        """生成Span ID"""
        return format(hash(str(uuid.uuid4())) & 0xffffffffffffffff, '016x')
    
    def _generate_trace_id(self) -> str:
        """生成Trace ID"""
        return uuid.uuid4().hex

    def start_parse(self, 
                   company_id: str,
                   file_id: str,
                   filename: str,
                   file_url: str = "",
                   file_type: str = "pdf",
                   cookie: Optional[str] = None,
                   **kwargs) -> str:
        """
        发起DST解析请求（原子方法）
        :param company_id: 公司ID
        :param file_id: 文件ID
        :param filename: 文件名
        :param file_url: 文件URL
        :param file_type: 文件类型
        :param cookie: 可选覆盖默认cookie
        :return: 解析token
        """
        payload = {
            "wps_company_id": company_id,
            "wps_v5_file_id": file_id,
            "file_url": file_url,
            "file_name": filename,
            "file_type": file_type,
            "parse_res_type": "insight_ai",
            "use_layout": False,
            "parse_target": ["chunk", "screenshot"],
            "convert_options": {
                "figure_dpi": 0
            },
            "parse_target_meta": {
                "chunk": {
                    "content": None,
                    "format": "insight_ai",
                    "model_type": "semantic",
                    "max_chunks": 1000,
                    "chunk_size": 1200,
                    "chunk_overlap": 0
                }
            },
            "req_type": "background",
            "download_id": ""
        }
        
        # 序列化JSON并转换为字节数据
        json_data = json.dumps(payload, ensure_ascii=False, separators=(',', ':'))
        json_bytes = json_data.encode('utf-8')
        content_length = len(json_bytes)
        
        print(f"DST请求payload大小: {len(json_data)} 字符, {content_length} 字节")
        
        # 使用wps2_sign生成签名
        sig, date_str, content_md5 = self.wps2_sign("/api/v1/aidocs_dst/parse_pipeline", json_bytes, "application/json")
        request_id = str(uuid.uuid4())
        
        # 发起解析请求
        try:
            print(f"发起DST解析请求，文件: {filename}")
            headers = self._make_headers(sig, content_md5, date_str, request_id, "application/json", cookie, content_length)
            
            # 调试信息：验证关键头部
            print(f"Content-Length: {headers.get('Content-Length')}")
            print(f"Content-MD5: {headers.get('Content-MD5')}")
            print(f"Content-Type: {headers.get('Content-Type')}")
            
            # 使用字节数据发送请求，明确设置Content-Length
            response = self.session.post(
                f"{self.base_url}/parse_pipeline",
                headers=headers,
                data=json_bytes,
                proxies=self.proxies,
                timeout=(self.connect_timeout, self.request_timeout),
                stream=False
            )
            response.raise_for_status()
            print(f"DST解析请求成功，状态码: {response.status_code}")
            
        except requests.exceptions.Timeout as e:
            raise Exception(f"DST parse request timeout: {e}")
        except requests.exceptions.ConnectionError as e:
            raise Exception(f"DST parse request connection error: {e}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"DST parse request failed: {e}")
        
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            raise Exception(f"DST parse response invalid JSON: {e}")
            
        if result.get("code") != 0:
            raise Exception(f"DST parse request failed: {result.get('message', 'Unknown error')}")
        
        token = result.get("data", {}).get("token")
        if not token:
            raise Exception("No token received from DST parse request")
        
        print(f"获取到DST解析token: {token}")
        return token

    def query_result(self, token: str, cookie: Optional[str] = None) -> Dict:
        """
        查询DST解析结果（原子方法）
        :param token: 解析token
        :param cookie: 可选覆盖默认cookie
        :return: 查询结果，包含status和data
        """
        query_payload = {
            "token": token,
            "parse_target": ["chunk", "screenshot"]
        }
        
        # 序列化JSON并转换为字节数据
        query_json_data = json.dumps(query_payload, ensure_ascii=False, separators=(',', ':'))
        query_json_bytes = query_json_data.encode('utf-8')
        query_content_length = len(query_json_bytes)
        
        # 使用wps2_sign生成签名
        query_sig, query_date_str, query_content_md5 = self.wps2_sign("/api/v1/aidocs_dst/general_parse_res", query_json_bytes, "application/json")
        query_request_id = str(uuid.uuid4())
        
        try:
            query_headers = self._make_headers(query_sig, query_content_md5, query_date_str, query_request_id, "application/json", cookie, query_content_length)
            
            # 使用字节数据发送请求，明确设置Content-Length
            query_response = self.session.post(
                f"{self.base_url}/general_parse_res",
                headers=query_headers,
                data=query_json_bytes,
                proxies=self.proxies,
                timeout=(self.connect_timeout, self.request_timeout),
                stream=False
            )
            query_response.raise_for_status()
            
        except requests.exceptions.Timeout as e:
            raise Exception(f"DST query request timeout: {e}")
        except requests.exceptions.ConnectionError as e:
            raise Exception(f"DST query request connection error: {e}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"DST query request failed: {e}")
        
        try:
            query_result = query_response.json()
        except json.JSONDecodeError as e:
            raise Exception(f"DST query response invalid JSON: {e}")
            
        if query_result.get("code") != 0:
            raise Exception(f"DST query failed: {query_result.get('message', 'Unknown error')}")
        
        data = query_result.get("data", {})
        status = data.get("status")
        
        return {
            "status": status,
            "data": data
        } 