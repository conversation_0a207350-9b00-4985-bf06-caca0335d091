import os
import json
import hashlib
import uuid
from datetime import datetime
import requests
from typing import Dict, Optional


class KdcClient:
    """独立的KDC服务客户端，包含完整的自有配置"""
    
    def __init__(self, 
                 ak: str, 
                 sk: str, 
                 host: str = "insight-ai.kna.wps.cn",
                 default_cookie: Optional[str] = None):
        """
        初始化独立KDC客户端
        :param ak: KDC专属AK
        :param sk: KDC专属SK
        :param host: KDC服务地址
        :param default_cookie: KDC专用cookie
        """
        self.ak = ak
        self.sk = sk
        self.host = host
        self.default_cookie = default_cookie
        self._proxies = self._init_proxies()

    def _init_proxies(self) -> Optional[Dict]:
        """KDC专属代理配置"""
        http_proxy = os.environ.get("KDC_HTTP_PROXY") or os.environ.get("http_proxy")
        https_proxy = os.environ.get("KDC_HTTPS_PROXY") or os.environ.get("https_proxy")
        
        proxies = {}
        if http_proxy:
            proxies["http"] = http_proxy
        if https_proxy:
            proxies["https"] = https_proxy
        return proxies or None

    def _make_headers(self, content_md5: str, date_str: str, cookie: Optional[str] = None) -> Dict:
        """生成KDC专用请求头"""
        cookie_to_use = cookie or self.default_cookie
        headers = {
            "Authorization": self._generate_auth(content_md5, date_str),
            "Date": date_str,
            "Content-MD5": content_md5,
            "x-request-id": str(uuid.uuid4()),
            "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
            "Content-Type": "application/json",
            "Host": self.host
        }
        if cookie_to_use:
            headers["Cookie"] = f"wps_sid={cookie_to_use}"
        return headers

    def _generate_auth(self, content_md5: str, date_str: str) -> str:
        """KDC专属签名算法"""
        sign_str = f"{self.sk}{content_md5}application/json{date_str}"
        signature = hashlib.sha1(sign_str.encode()).hexdigest()
        return f"WPS-2:{self.ak}:{signature}"

    def parse_document(self, 
                     company_id: str,
                     file_id: str,
                     filename: str,
                     cookie: Optional[str] = None,
                     **kwargs) -> Dict:
        """
        KDC专属文档解析方法
        :param cookie: 可选覆盖默认cookie
        """
        payload = {
            "wps_company_id": company_id,
            "wps_v5_file_id": file_id,
            "filename": filename,
            "format": kwargs.get("format", "markdown"),
            "include_elements": kwargs.get("include_elements", "all"),
            "convert_options": {
                "pdf_engine": kwargs.get("pdf_engine", "scan")
            }
        }
        
        json_data = json.dumps(payload, ensure_ascii=False)
        date_str = datetime.now().strftime("%a %b %d %Y %H:%M:%S GMT+0800")
        content_md5 = hashlib.md5(json_data.encode()).hexdigest()
        
        response = requests.post(
            f"http://{self.host}/api/v1/doc_parse/kdc_parse",
            headers=self._make_headers(content_md5, date_str, cookie),
            data=json_data,
            proxies=self._proxies
        )
        response.raise_for_status()
        return response.json()