# coding:utf-8
import os
from typing import Op<PERSON>, <PERSON>ple
from concurrent.futures import ThreadPoolExecutor
import asyncio
from ks3.connection import Connection
from ks3.key import Key
from ks3.prefix import Prefix

class KS3Client(object):
    def __init__(self):
        self.bucket: Optional[str] = None
        self.conn: Optional[Connection] = None

    def init(self, host: str, ak: str, sk: str, bucket: str) -> None:
        """
        初始化KS3客户端
        
        Args:
            host: KS3服务地址
            ak: Access Key
            sk: Secret Key
            bucket: 存储桶名称
        """
        self.bucket = bucket
        
        # 从环境变量读取代理配置
        proxy_host = os.getenv('KS3_PROXY_HOST', '127.0.0.1')
        proxy_port = int(os.getenv('KS3_PROXY_PORT', '18899'))
        
        self.conn = Connection(
            ak, 
            sk, 
            host=host, 
            is_secure=False, 
            proxy_host=proxy_host, 
            proxy_port=proxy_port
        )

    def upload_from_bytes(self, ks3path: str, data: bytes, content_type: str = None) -> bool:
        """
        上传字节数据到KS3
        
        Args:
            ks3path: KS3路径
            data: 字节数据
            content_type: 可选的Content-Type
            
        Returns:
            是否上传成功
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            
            # 设置Content-Type
            if content_type:
                k.content_type = content_type
            elif ks3path.lower().endswith('.html'):
                k.content_type = 'text/html; charset=utf-8'
            
            ret = k.set_contents_from_string(data, policy="private")
            return ret and ret.status == 200
        except Exception as e:
            print(f"上传文件失败: {ks3path}, error: {e}")
            return False

    async def async_upload_from_bytes(self, ks3path: str, data: bytes, content_type: str = None) -> bool:
        """
        异步上传字节数据到KS3
        
        Args:
            ks3path: KS3路径
            data: 字节数据
            content_type: 可选的Content-Type
            
        Returns:
            是否上传成功
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            
            # 设置Content-Type
            if content_type:
                k.content_type = content_type
            elif ks3path.lower().endswith('.html'):
                k.content_type = 'text/html; charset=utf-8'
            
            loop = asyncio.get_running_loop()
            with ThreadPoolExecutor() as pool:
                ret = await loop.run_in_executor(
                    pool, 
                    lambda: k.set_contents_from_string(data, policy="private")
                )
            return ret and ret.status == 200
        except Exception as e:
            print(f"异步上传文件失败: {ks3path}, error: {e}")
            return False

    def upload_from_text(self, ks3path: str, text: str, content_type: str = None) -> bool:
        """
        上传文本到KS3
        
        Args:
            ks3path: KS3路径
            text: 文本内容
            content_type: 可选的Content-Type
            
        Returns:
            是否上传成功
        """
        return self.upload_from_bytes(ks3path, text.encode("utf-8"), content_type)

    def upload_from_file(self, ks3path: str, fpath: str, replace: bool = True) -> bool:
        """
        上传本地文件到KS3
        
        Args:
            ks3path: KS3路径
            fpath: 本地文件路径
            replace: 是否替换已有文件
            
        Returns:
            是否上传成功
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            
            # 根据文件扩展名设置Content-Type
            if ks3path.lower().endswith('.html'):
                k.content_type = 'text/html; charset=UTF-8'
            elif ks3path.lower().endswith('.json'):
                k.content_type = 'application/json; charset=UTF-8'
            elif ks3path.lower().endswith('.css'):
                k.content_type = 'text/css; charset=UTF-8'
            elif ks3path.lower().endswith('.js'):
                k.content_type = 'application/javascript; charset=UTF-8'
            
            ret = k.set_contents_from_filename(fpath, replace=replace, policy="private")
            return ret and ret.status == 200
        except Exception as e:
            print(f"上传本地文件失败: {fpath} -> {ks3path}, error: {e}")
            return False

    def download_to_text(self, ks3path: str) -> Tuple[bool, str]:
        """
        从KS3下载文件内容为文本
        
        Args:
            ks3path: KS3路径
            
        Returns:
            (是否成功, 文本内容)
        """
        k = None
        try:
            b = self.conn.get_bucket(self.bucket)
            k = b.get_key(ks3path.strip("/"))
            text = k.get_contents_as_string()
            return True, text.decode("utf-8", errors="ignore")
        except Exception as e:
            print(f"下载文件失败: {ks3path}, error: {e}")
            return False, ""
        finally:
            if k:
                k.close()

    def download_to_file(self, ks3path: str, fpath: str) -> bool:
        """
        从KS3下载文件到本地
        
        Args:
            ks3path: KS3路径
            fpath: 本地保存路径
            
        Returns:
            是否下载成功
        """
        k = None
        try:
            b = self.conn.get_bucket(self.bucket)
            k = b.get_key(ks3path.strip("/"))
            k.get_contents_to_filename(fpath)
            return True
        except Exception as e:
            print(f"下载文件到本地失败: {ks3path} -> {fpath}, error: {e}")
            return False
        finally:
            if k:
                k.close()

    def generate_url(self, ks3path: str, timeout: int) -> str:
        """
        生成临时访问URL
        
        Args:
            ks3path: KS3路径
            timeout: URL有效期(秒)
            
        Returns:
            临时访问URL
        """
        try:
            return self.conn.generate_url(timeout, "GET", self.bucket, ks3path)
        except Exception as e:
            print(f"生成临时URL失败: {ks3path}, error: {e}")
            return ""

    async def async_generate_url(self, ks3path: str, timeout: int) -> str:
        """
        异步生成临时访问URL
        
        Args:
            ks3path: KS3路径
            timeout: URL有效期(秒)
            
        Returns:
            临时访问URL
        """
        try:
            loop = asyncio.get_running_loop()
            with ThreadPoolExecutor() as pool:
                return await loop.run_in_executor(
                    pool,
                    lambda: self.conn.generate_url(timeout, "GET", self.bucket, ks3path)
                )
        except Exception as e:
            print(f"异步生成临时URL失败: {ks3path}, error: {e}")
            return ""

    def list_files(self, path: str = "") -> list:
        """
        列出指定路径下的文件
        
        Args:
            path: 路径前缀
            
        Returns:
            文件列表
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(prefix=path)
            return [k.name for k in keys if isinstance(k, Key)]
        except Exception as e:
            print(f"列出文件失败: {path}, error: {e}")
            return []

    def list_dirs(self, path: str = "") -> list:
        """
        列出指定路径下的目录
        
        Args:
            path: 路径前缀
            
        Returns:
            目录列表
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(prefix=path, delimiter="/")
            return [k.name for k in keys if isinstance(k, Prefix)]
        except Exception as e:
            print(f"列出目录失败: {path}, error: {e}")
            return []

    def delete_file(self, key_name: str) -> bool:
        """
        删除指定文件
        
        Args:
            key_name: 文件key
            
        Returns:
            是否删除成功
        """
        try:
            b = self.conn.get_bucket(self.bucket)
            b.delete_key(key_name=key_name)
            return True
        except Exception as e:
            print(f"删除文件失败: {key_name}, error: {e}")
            return False


# 使用示例
if __name__ == "__main__":
    # 设置环境变量(实际使用时应提前设置)
    os.environ['KS3_PROXY_HOST'] = '127.0.0.1'
    os.environ['KS3_PROXY_PORT'] = '18899'
    
    # 初始化客户端
    client = KS3Client()
    client.init(
        host="your-ks3-host",
        ak="your-access-key",
        sk="your-secret-key",
        bucket="your-bucket-name"
    )
    
    # 测试上传
    test_data = b"test data"
    upload_success = client.upload_from_bytes("test/test.txt", test_data)
    print(f"上传结果: {upload_success}")
    
    # 测试下载
    success, content = client.download_to_text("test/test.txt")
    print(f"下载结果: {success}, 内容: {content}")
    
    # 测试生成URL
    url = client.generate_url("test/test.txt", 3600)
    print(f"临时URL: {url}")
    
    # 测试删除
    delete_success = client.delete_file("test/test.txt")
    print(f"删除结果: {delete_success}")
