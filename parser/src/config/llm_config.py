import os

# LLM配置 - 支持从环境变量读取
def get_llm_config_dict():
    """获取LLM配置字典"""
    service_type = os.getenv('LLM_SERVICE_TYPE', 'custom')  # 默认使用自定义服务

    if service_type == 'azure':
        return {
            'service_type': 'azure',
            'api_key': os.getenv('AZURE_OPENAI_API_KEY', ''),
            'api_version': os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview'),
            'endpoint': os.getenv('AZURE_OPENAI_ENDPOINT', ''),
            'use_proxy': os.getenv('LLM_USE_PROXY', 'false').lower() == 'true',
            'retry_count': int(os.getenv('LLM_RETRY_COUNT', '3')),
            'retry_delay': int(os.getenv('LLM_RETRY_DELAY', '1')),
            'model_config': {
                'deployment_name': os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', 'gpt-4o'),
                'max_tokens': int(os.getenv('LLM_MAX_TOKENS', '4000')),
                'temperature': float(os.getenv('LLM_TEMPERATURE', '0.7')),
                'top_p': float(os.getenv('LLM_TOP_P', '0.95')),
                'frequency_penalty': float(os.getenv('LLM_FREQUENCY_PENALTY', '0.1')),
                'presence_penalty': float(os.getenv('LLM_PRESENCE_PENALTY', '0.0')),
                'timeout': int(os.getenv('LLM_TIMEOUT', '60'))
            }
        }
    elif service_type == 'custom':
        return {
            'service_type': 'custom',
            'endpoint': os.getenv('CUSTOM_LLM_ENDPOINT', 'http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions'),
            'use_proxy': os.getenv('LLM_USE_PROXY', 'true').lower() == 'true',  # 自定义服务默认使用代理
            'retry_count': int(os.getenv('LLM_RETRY_COUNT', '3')),
            'retry_delay': int(os.getenv('LLM_RETRY_DELAY', '1')),
            'headers': {
                'User-Agent': os.getenv('CUSTOM_LLM_USER_AGENT', 'LLM-Service/1.0.0'),
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Host': os.getenv('CUSTOM_LLM_HOST', 'kmd-api.kas.wps.cn'),
                'Connection': 'keep-alive'
            },
            'custom_headers': {},
            'model_config': {
                'deployment_name': os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', ''),
                'max_tokens': int(os.getenv('LLM_MAX_TOKENS', '256')),
                'temperature': float(os.getenv('LLM_TEMPERATURE', '0.8')),
                'timeout': int(os.getenv('LLM_TIMEOUT', '60'))
            }
        }
    else:
        raise ValueError(f"Unsupported LLM_SERVICE_TYPE: {service_type}")

# 添加自定义headers支持
def _add_custom_headers(config):
    """从环境变量添加自定义headers"""
    if config.get('service_type') == 'custom':
        custom_headers = {}
        for key, value in os.environ.items():
            if key.startswith('CUSTOM_LLM_HEADER_'):
                header_name = key[18:].replace('_', '-')  # 移除前缀并转换下划线为连字符
                custom_headers[header_name] = value

        if custom_headers:
            config['custom_headers'].update(custom_headers)

    return config

# 获取完整配置
LLM_CONFIG = _add_custom_headers(get_llm_config_dict())

# 验证必要的配置
def validate_llm_config():
    """验证LLM配置是否完整"""
    config = LLM_CONFIG
    service_type = config.get('service_type', 'custom')

    if service_type == 'azure':
        required_fields = ['api_key', 'endpoint']
        missing_fields = []

        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)

        if missing_fields:
            raise ValueError(f"Missing required Azure OpenAI configuration: {', '.join(missing_fields)}")

    elif service_type == 'custom':
        # 自定义服务只需要endpoint
        if not config.get('endpoint'):
            raise ValueError("Missing required custom LLM configuration: endpoint")

    return True

# 获取配置的便捷函数
def get_llm_config():
    """获取LLM配置"""
    validate_llm_config()
    return LLM_CONFIG

# 表格生成相关配置
TABLE_GENERATION_CONFIG = {
    'default_model': os.getenv('TABLE_GEN_MODEL', ''),
    'default_temperature': float(os.getenv('TABLE_GEN_TEMPERATURE', '0.8')),
    'max_retries': int(os.getenv('TABLE_GEN_MAX_RETRIES', '3')),
    
    # 表格生成的默认参数
    'default_rows': int(os.getenv('TABLE_DEFAULT_ROWS', '5')),
    'default_cols': int(os.getenv('TABLE_DEFAULT_COLS', '4')),
    'min_rows': int(os.getenv('TABLE_MIN_ROWS', '3')),
    'max_rows': int(os.getenv('TABLE_MAX_ROWS', '15')),
    'min_cols': int(os.getenv('TABLE_MIN_COLS', '3')),
    'max_cols': int(os.getenv('TABLE_MAX_COLS', '8')),
}

# 预定义的表格主题
TABLE_TOPICS = [
    # 商业相关
    "销售业绩统计表",
    "员工信息管理表", 
    "产品库存清单",
    "财务收支明细",
    "客户信息登记表",
    "项目进度跟踪表",
    "供应商评估表",
    "市场调研数据",
    
    # 教育相关
    "学生成绩统计表",
    "课程安排表",
    "教师信息表",
    "图书借阅记录",
    "实验室设备清单",
    "学费缴费记录",
    
    # 医疗相关
    "患者基本信息表",
    "药品库存管理",
    "医疗设备清单",
    "检查结果统计",
    "医生排班表",
    "病房使用情况",
    
    # 生产制造
    "生产计划表",
    "质量检测记录",
    "设备维护记录",
    "原材料采购清单",
    "产品质量统计",
    "工人考勤记录",
    
    # 政府机构
    "人口统计表",
    "预算分配表",
    "公共设施清单",
    "政策执行情况",
    "公务员信息表",
    "社会保障统计",
    
    # 科研相关
    "实验数据记录表",
    "研究项目进度",
    "科研经费使用",
    "论文发表统计",
    "设备使用记录",
    "研究人员信息"
]

def get_random_table_topic():
    """获取随机表格主题"""
    import random
    return random.choice(TABLE_TOPICS)
