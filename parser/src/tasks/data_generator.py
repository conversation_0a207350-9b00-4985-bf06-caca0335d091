"""
数据生成任务模块
"""
import os
import random
import string
import json
import imgkit
from pathlib import Path
from typing import Dict, List, Any, Optional


class DataGenerator:
    """数据生成器"""
    
    # 复杂表格模板类型
    COMPLEX_TABLE_TYPES = {
        '检验单': {
            'structure': [
                {'rowspan': 2, 'colspan': 4, 'content': '检验报告单'},
                {'rows': [
                    ['患者姓名', '性别', '年龄', '科室'],
                    ['检验项目', '检验结果', '参考范围', '单位']
                ]},
                {'nested_table': '检验明细', 'colspan': 4},
                {'rows': [['检验医生', '', '检验时间', '']]}
            ]
        },
        '处方单': {
            'structure': [
                {'rowspan': 1, 'colspan': 6, 'content': '门诊处方笺'},
                {'rows': [
                    ['姓名', '', '性别', '', '年龄', ''],
                    ['科别', '', '门诊号', '', '开具时间', '']
                ]},
                {'nested_table': '药品明细', 'colspan': 6},
                {'rows': [['医师签名', '', '', '', '药师签名', '']]}
            ]
        },
        '发票': {
            'structure': [
                {'rowspan': 1, 'colspan': 4, 'content': '电子发票'},
                {'rows': [
                    ['发票号码', '', '开票日期', ''],
                    ['购买方', '', '销售方', '']
                ]},
                {'nested_table': '商品明细', 'colspan': 4},
                {'rows': [['合计金额', '', '实收金额', '']]}
            ]
        }
    }
    
    def __init__(self):
        # 配置目录 - 优先使用环境变量
        self.enhance_dir = os.getenv("PROJECT_ROOT_DIR", os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
        self.gen_data_dir = os.getenv("GEN_DATA_DIR")
        self.images_dir = os.getenv("IMAGES_DIR")
        
        if self.gen_data_dir:
            os.makedirs(self.gen_data_dir, exist_ok=True)
        if self.images_dir:
            os.makedirs(self.images_dir, exist_ok=True)
        
        # 常用中文字符集
        self.chinese_chars = [
            # 常用名字
            '张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴',
            # 常用词组的单字
            '年', '月', '日', '时', '分', '秒', '元', '万', '亿', '个',
            '大', '小', '多', '少', '长', '短', '高', '低', '快', '慢',
            '好', '坏', '是', '否', '对', '错', '行', '列', '表', '格',
            '数', '量', '价', '值', '率', '比', '总', '平', '均', '和',
            # 公司相关
            '公', '司', '厂', '店', '部', '组', '科', '室', '处', '局',
            # 商品相关
            '品', '类', '型', '款', '色', '新', '旧', '优', '次', '等',
            # 状态相关
            '完', '成', '待', '办', '进', '行', '停', '止', '开', '关',
        ]
        
        # 常用词组
        self.chinese_words = [
            '销售额', '利润率', '成本', '收入', '支出', '库存量',
            '生产量', '订单数', '客户数', '员工数', '增长率',
            '市场份额', '投资额', '回报率', '净利润', '毛利润',
            '产品名称', '规格型号', '生产日期', '保质期限',
            '合格率', '不良率', '达标率', '完成率', '使用率',
        ]
    
    def random_chinese_text(self, min_chars=1, max_chars=4):
        """生成随机中文文本"""
        if random.random() < 0.3:  # 30%概率使用预设词组
            return random.choice(self.chinese_words)
        length = random.randint(min_chars, max_chars)
        return ''.join(random.choice(self.chinese_chars) for _ in range(length))

    def random_mixed_text(self, min_len=2, max_len=6):
        """生成混合文本（中文、英文、数字）"""
        text_type = random.choices(['chinese', 'mixed', 'number'], weights=[0.5, 0.3, 0.2])[0]
        
        if text_type == 'chinese':
            return self.random_chinese_text(1, 4)
        elif text_type == 'number':
            # 生成数字（可能带小数点或百分号）
            if random.random() < 0.3:  # 30%概率生成百分比
                return f"{random.randint(0, 100)}.{random.randint(0, 99)}%"
            elif random.random() < 0.5:  # 50%概率生成带小数的数字
                return f"{random.randint(0, 10000)}.{random.randint(0, 99)}"
            else:  # 生成整数
                return str(random.randint(0, 999999))
        else:  # mixed
            # 混合英文字母和数字
            length = random.randint(min_len, max_len)
            return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

    def random_table_data(self, min_rows=3, max_rows=10, min_cols=3, max_cols=8):
        """随机生成表格数据"""
        rows = random.randint(min_rows, max_rows)
        cols = random.randint(min_cols, max_cols)
        data = []
        
        # 生成表头（倾向于使用中文）
        headers = []
        for _ in range(cols):
            if random.random() < 0.8:  # 80%概率使用中文表头
                headers.append(self.random_chinese_text(2, 4))
            else:
                headers.append(self.random_mixed_text(2, 6))
        data.append(headers)
        
        # 生成数据行
        for _ in range(rows - 1):
            row = [self.random_mixed_text() for _ in range(cols)]
            data.append(row)
        return data

    def table_to_html(self, data, title=None, is_complex=False):
        """渲染为html"""
        html = '''
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                table {
                    border-collapse: collapse;
                    width: 100%;
                    max-width: 1200px;
                    margin: 0 auto;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                    border: 1px solid #ddd;
                }
                th {
                    font-weight: bold;
                    background-color: #f4f4f4;
                }
                .nested-table {
                    width: 100%;
                    margin: 0;
                }
                .nested-table td, .nested-table th {
                    border: 1px solid #ddd;
                }
                .table-title {
                    text-align: center;
                    font-size: 18px;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
        '''
        if title:
            html += f'<div class="table-title">{title}</div>'
        
        html += '<table>'
        
        if is_complex and isinstance(data, dict) and 'structure' in data:
            # 处理复杂表格结构
            for item in data['structure']:
                if 'rowspan' in item and 'colspan' in item:
                    # 处理合并单元格
                    html += f'<tr><td rowspan="{item["rowspan"]}" colspan="{item["colspan"]}" style="text-align:center;font-weight:bold;">{item["content"]}</td></tr>'
                elif 'rows' in item:
                    # 处理普通行数据
                    for row in item['rows']:
                        html += '<tr>' + ''.join(f'<td>{cell}</td>' for cell in row) + '</tr>'
                elif 'nested_table' in item:
                    # 处理嵌套表格
                    nested_data = data.get('nested_data', {}).get(item['nested_table'], [])
                    if nested_data:
                        html += f'<tr><td colspan="{item["colspan"]}">'
                        html += '<table class="nested-table">'
                        # 添加嵌套表格的表头和数据
                        for i, row in enumerate(nested_data):
                            html += '<tr>' + ''.join(
                                f'<{"th" if i==0 else "td"}>{cell}</{"th" if i==0 else "td"}>'
                                for cell in row
                            ) + '</tr>'
                        html += '</table></td></tr>'
        else:
            # 处理常规表格数据
            for i, row in enumerate(data):
                html += '<tr>' + ''.join(
                    f'<{"th" if i==0 else "td"}>{cell}</{"th" if i==0 else "td"}>'
                    for cell in row
                ) + '</tr>'
        
        html += '</table></body></html>'
        return html

    def init_llm_service(self):
        """初始化LLM服务"""
        try:
            from clients.llm_client import LLMClient
            
            # 获取LLM配置 - 使用环境变量
            base_url = os.getenv('CUSTOM_LLM_ENDPOINT', 'http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1')
            # 如果URL只是主机名，需要去掉/chat/completions后缀
            if base_url.endswith('/chat/completions'):
                base_url = base_url[:-len('/chat/completions')]
            client = LLMClient(base_url)
            return client
        except ImportError as e:
            print(f"LLM功能不可用: {e}")
            return None
        except Exception as e:
            print(f"初始化LLM服务失败: {e}")
            return None

    def generate_semantic_table_data(self, llm_client, topic: str = None, rows: int = 5, cols: int = 4) -> Optional[List[List[str]]]:
        """
        使用LLM生成语义化表格数据

        Args:
            llm_client: LLM客户端实例
            topic: 表格主题，如果为None则随机生成
            rows: 行数（包括表头）
            cols: 列数

        Returns:
            List[List[str]]: 表格数据，第一行为表头
        """
        if llm_client is None:
            print("LLM服务不可用，使用随机数据")
            return None

        try:
            # 复杂表格生成概率提升到70%
            if topic is None and random.random() < 0.7:
                template_type = random.choice(list(self.COMPLEX_TABLE_TYPES.keys()))
                template = self.COMPLEX_TABLE_TYPES[template_type]
                
                # 生成嵌套表格数据（3-8行，内容为中文/数字/业务词）
                nested_data = {}
                for item in template['structure']:
                    if 'nested_table' in item:
                        nested_headers = []
                        if item['nested_table'] == '检验明细':
                            nested_headers = ['检验项目', '检测值', '参考范围', '单位']
                        elif item['nested_table'] == '药品明细':
                            nested_headers = ['药品名称', '规格', '用法', '数量', '单价', '金额']
                        elif item['nested_table'] == '商品明细':
                            nested_headers = ['商品名称', '数量', '单价', '金额']
                        nrows = random.randint(4, 8)
                        nested_rows = []
                        for _ in range(nrows):
                            row = []
                            for h in nested_headers:
                                if '数量' in h or '单价' in h or '金额' in h or '检测值' in h:
                                    row.append(str(random.randint(1, 10000)))
                                elif '参考范围' in h:
                                    row.append(f"{random.randint(1, 10)}-{random.randint(11, 20)}")
                                elif '单位' in h:
                                    row.append(random.choice(['mg', 'g', 'ml', '次', '盒', '片', '元']))
                                else:
                                    row.append(self.random_chinese_text(2, 4))
                            nested_rows.append(row)
                        nested_data[item['nested_table']] = [nested_headers] + nested_rows
                # 普通行内容也用中文/数字/业务词填充
                for item in template['structure']:
                    if 'rows' in item:
                        for row in item['rows']:
                            for idx, cell in enumerate(row):
                                if cell == '':
                                    row[idx] = self.random_chinese_text(2, 4)
                table_data = {
                    'structure': template['structure'],
                    'nested_data': nested_data
                }
                print(f"成功生成复杂表格: {template_type}")
                return table_data
            else:
                # 生成普通表格
                topics = ['销售报表', '员工统计', '产品清单', '财务报告', '库存管理', '客户信息', '项目进度', '设备清单', '考勤记录', '订单明细']
                topic = random.choice(topics) if topic is None else topic
            print(f"正在生成主题为'{topic}'的表格数据...")

            # 构建表格生成的提示词
            prompt = f"""请生成一个关于"{topic}"的表格数据，包含{rows}行（包括表头）和{cols}列。

要求：
1. 返回JSON格式，包含三个字段：topic（主题）、headers（表头列表）、data（数据行列表）
2. 表头要符合主题，使用中文
3. 数据要真实合理，与主题相关
4. 确保JSON格式完整正确，不要有多余的字符

示例格式：
{{
    "topic": "{topic}",
    "headers": ["列1", "列2", "列3", "列4"],
    "data": [
        ["数据1-1", "数据1-2", "数据1-3", "数据1-4"],
        ["数据2-1", "数据2-2", "数据2-3", "数据2-4"],
        ["数据3-1", "数据3-2", "数据3-3", "数据3-4"],
        ["数据4-1", "数据4-2", "数据4-3", "数据4-4"]
    ]
}}"""

            system_prompt = "你是一个专业的数据生成助手，擅长创建结构化的表格数据。请严格按照JSON格式返回，不要添加任何额外的文本或解释。"

            # 调用LLM生成表格数据
            response = llm_client.text_completion(
                text_prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=int(os.getenv('LLM_MAX_TOKENS', '2000')),
                temperature=float(os.getenv('LLM_TEMPERATURE', '0.8'))
            )

            # 解析响应
            if 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content']
                
                try:
                    # 清理响应内容，移除可能的markdown代码块标记
                    if "```json" in content:
                        json_start = content.find("```json") + 7
                        json_end = content.find("```", json_start)
                        if json_end != -1:
                            content = content[json_start:json_end].strip()
                    elif "```" in content:
                        # 移除任何代码块标记
                        content = content.replace("```", "").strip()
                    
                    # 修复常见的JSON格式问题
                    content = content.strip()
                    if not content:
                        print("LLM返回的内容为空")
                        return None
                    
                    # 修复多余的闭合括号
                    if content.count('}') > content.count('{'):
                        # 找到最后一个有效的}位置
                        brace_count = 0
                        valid_end = len(content)
                        for i, char in enumerate(content):
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0:
                                    valid_end = i + 1
                                    break
                        content = content[:valid_end]
                    
                    # 尝试解析JSON
                    result = json.loads(content)
                    
                    # 验证结果格式
                    if 'headers' in result and 'data' in result:
                        table_data = [result['headers']] + result['data']
                        print(f"成功生成表格: 表格数据")
                        return table_data
                    else:
                        print("LLM返回的数据格式不正确，缺少必要字段")
                        return None
                        
                except json.JSONDecodeError as e:
                    print(f"LLM返回的不是有效的JSON格式: {e}")
                    print(f"响应内容: {content}")
                    return None
            else:
                print("LLM响应格式不正确")
                return None

        except Exception as e:
            print(f"LLM生成表格数据失败: {e}")
            return None

    def generate_tables(self, num_tables=10, use_llm=False, topics=None, strict_llm=False):
        """
        生成表格数据

        Args:
            num_tables: 生成表格数量
            use_llm: 是否使用LLM生成语义化数据
            topics: 指定的主题列表，如果为None则随机生成
            strict_llm: 严格LLM模式，失败时不降级到随机生成
        """
        if not self.gen_data_dir or not self.images_dir:
            raise ValueError("缺少必要的目录配置：GEN_DATA_DIR或IMAGES_DIR")
        
        llm_client = None
        if use_llm:
            llm_client = self.init_llm_service()
            if llm_client is None:
                if strict_llm:
                    print("❌ LLM客户端初始化失败，严格模式下不使用随机数据生成")
                    return
                else:
                    print("LLM客户端初始化失败，将使用随机数据生成")
                    use_llm = False

        successful_count = 0
        for i in range(num_tables):
            # 确定表格数据生成方式
            is_complex = False
            if use_llm and llm_client:
                # 确定主题
                topic = None
                if topics and i < len(topics):
                    topic = topics[i]

                # 随机确定表格大小
                rows = random.randint(4, 8)  # 包括表头
                cols = random.randint(3, 6)

                # 使用LLM生成数据，支持重试
                data = None
                max_retries = 3
                for retry in range(max_retries):
                    data = self.generate_semantic_table_data(llm_client, topic, rows, cols)
                    if isinstance(data, dict) and 'structure' in data:
                        is_complex = True
                    if data is not None:
                        break
                    else:
                        print(f"表格 {i+1}: LLM生成失败，第 {retry+1}/{max_retries} 次重试")

                # 严格模式：LLM生成失败时跳过这个表格
                if strict_llm and data is None:
                    print(f"❌ 表格 {i+1}: LLM生成失败，严格模式下跳过此表格")
                    continue
                
                # 非严格模式：LLM生成失败时降级到随机生成
                if data is None:
                    print(f"表格 {i+1}: LLM生成失败，使用随机数据")
                    data = self.random_table_data()
                    title_prefix = "随机表格"
                else:
                    title_prefix = "语义表格"
            else:
                # 使用随机数据
                data = self.random_table_data()
                title_prefix = "随机表格"
            # 生成文件
            fname = f'table_{i+1}.html'
            html_path = os.path.join(self.gen_data_dir, fname)
            img_path = os.path.join(self.images_dir, f'table_{i+1}.png')
            html = self.table_to_html(data, title=f'{title_prefix} {i+1}', is_complex=is_complex)
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html)
            # html转图片
            try:
                imgkit.from_file(html_path, img_path, options={
                    'format': 'png',
                    'encoding': 'utf-8',
                    'quiet': ''
                })
                print(f'Generated: {img_path}')
                successful_count += 1
            except Exception as e:
                print(f'Failed to generate image for {html_path}: {e}')
        
        print(f"✅ 成功生成 {successful_count} 个表格文件")
        return successful_count