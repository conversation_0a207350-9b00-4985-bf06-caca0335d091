"""
报告生成任务模块
"""
import os
import json
import datetime
from typing import Dict, Any, List

from processors.html_generator import generate_html_report
from clients.ks3_client import KS3Client
from core.result_manager import ResultManager


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, dataset_name: str = None):
        self.dataset_name = dataset_name or os.getenv("DATASET_NAME", "default_test")
        self.result_manager = ResultManager(self.dataset_name)
    
    def upload_report_to_ks3(self, report_file: str) -> str:
        """将报告上传到KS3的luzeshu/reports/路径"""
        try:
            # 初始化KS3客户端
            ks3 = KS3Client()
            ks3.init(
                host=os.getenv('KS3_HOST', 'your-ks3-host'),
                ak=os.getenv('KS3_AK', 'your-access-key'),
                sk=os.getenv('KS3_SK', 'your-secret-key'),
                bucket=os.getenv('KS3_BUCKET', 'your-bucket-name')
            )
            
            # 构造KS3路径
            filename = os.path.basename(report_file)
            ks3_path = f"luzeshu/reports/{filename}"
            
            # 上传文件
            if ks3.upload_from_file(ks3_path, report_file):
                print(f"报告已上传到KS3: {ks3_path}")
                
                # 生成分享链接（7天有效期）
                share_url = ks3.generate_url(ks3_path, 7 * 24 * 3600)
                if share_url:
                    print(f"报告分享链接: {share_url}")
                    return share_url
                else:
                    print("生成分享链接失败")
                    return True
            else:
                print(f"上传报告到KS3失败: {report_file}")
                return False
                
        except Exception as e:
            print(f"上传报告到KS3时发生错误: {e}")
            return False
    
    def generate_report(self, results_file: str = None, output_filename: str = None, 
                        include_progress: bool = False) -> tuple:
        """
        从解析结果生成HTML报告
        
        Args:
            results_file: 解析结果文件路径，None表示使用最新结果
            output_filename: 输出文件名，None表示自动生成
            include_progress: 是否包含进行中的会话结果
            
        Returns:
            (report_file_path, share_url_or_bool)
        """
        # 加载解析结果
        if results_file is None:
            parse_data = self.result_manager.load_latest_results()
            
            # 如果没有最新结果，且允许包含进度，尝试查找进行中的会话
            if not parse_data and include_progress:
                parse_data = self._load_in_progress_session()
            
            if not parse_data:
                print("未找到最新结果文件")
                return None, None
            
            # 检查是否是进行中的会话
            status = parse_data.get("metadata", {}).get("status", "unknown")
            if status == "in_progress":
                print("✨ 加载进行中的解析会话结果")
                self._print_progress_info(parse_data)
            else:
                print("成功加载最新解析结果")
        else:
            if not os.path.exists(results_file):
                print(f"结果文件不存在: {results_file}")
                return None, None
            
            try:
                with open(results_file, "r", encoding="utf-8") as f:
                    parse_data = json.load(f)
                print(f"成功加载解析结果: {results_file}")
            except Exception as e:
                print(f"加载解析结果失败: {str(e)}")
                return None, None
        
        if not parse_data:
            print("无效的解析结果数据")
            return None, None
        
        # 提取解析结果的各个组件
        processed_files = parse_data.get("processed_files", [])
        uploaded_files = parse_data.get("uploaded_files", [])
        local_monkey_results = parse_data.get("local_monkey_results", [])
        
        # 从新的结构中提取解析结果
        parse_results = parse_data.get("parse_results", {})
        kdc_results = parse_results.get("kdc_markdown", [])  # 注意这里使用新的命名
        kdc_plain_results = parse_results.get("kdc_plain", [])
        kdc_kdc_results = parse_results.get("kdc_kdc", [])
        monkey_ocr_results = parse_results.get("monkey_ocr", [])
        monkey_ocr_results_v2 = parse_results.get("monkey_ocr_latex", [])
        
        # 兼容旧格式
        if not kdc_results:
            kdc_results = parse_results.get("kdc", [])  # 兼容旧的kdc命名
        if not kdc_plain_results:
            kdc_plain_results = parse_data.get("kdc_plain_results", [])
        if not kdc_kdc_results:
            kdc_kdc_results = parse_data.get("kdc_kdc_results", [])
        if not monkey_ocr_results:
            monkey_ocr_results = parse_data.get("monkey_ocr_results", [])
        if not monkey_ocr_results_v2:
            monkey_ocr_results_v2 = parse_data.get("monkey_ocr_results_v2", [])
        if not local_monkey_results:
            local_monkey_results = parse_data.get("monkey_ocr_local_results", [])
        
        # 计算实际有结果的文件数
        actual_files = len([f for f in uploaded_files if any(
            f.get('fname') == result.get('filename') 
            for parser_results in [kdc_results, kdc_plain_results, kdc_kdc_results, 
                                 monkey_ocr_results, monkey_ocr_results_v2] 
            for result in parser_results
        )])
        
        print(f"生成报告 - 处理文件数: {len(processed_files)}, 已有结果文件数: {actual_files}")
        
        # 生成HTML报告
        try:
            html = generate_html_report(
                processed_files,
                kdc_results,
                uploaded_files,
                monkey_ocr_results,
                monkey_ocr_results_v2,
                kdc_plain_results,
                kdc_kdc_results,
                local_monkey_results,
                parse_data
            )
            
            # 确定输出文件名
            if output_filename is None:
                # 支持测试集隔离
                reports_base_dir = os.getenv("REPORTS_BASE_DIR", os.path.join("..", "reports"))
                reports_dir = os.path.join(reports_base_dir, self.dataset_name)
                os.makedirs(reports_dir, exist_ok=True)
                timestamp = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
                
                # 如果是进行中的会话，在文件名中标明
                status = parse_data.get("metadata", {}).get("status", "unknown")
                if status == "in_progress":
                    output_filename = os.path.join(reports_dir, f"report_progress_{timestamp}.html")
                else:
                    output_filename = os.path.join(reports_dir, f"report_{timestamp}.html")
            
            # 保存报告
            with open(output_filename, "w", encoding="utf-8") as f:
                f.write(html)
            
            print(f"报告已生成: {output_filename}")
            
            # 上传报告到KS3
            share_url = self.upload_report_to_ks3(output_filename)
            
            return output_filename, share_url
            
        except Exception as e:
            print(f"生成报告失败: {str(e)}")
            return None, None
    
    def _load_in_progress_session(self) -> Dict[str, Any]:
        """查找并加载进行中的解析会话"""
        try:
            # 查找所有解析结果文件
            available_results = self.result_manager.list_available_results()
            
            # 找到最新的进行中会话
            for result in available_results:
                try:
                    with open(result["filepath"], "r", encoding="utf-8") as f:
                        data = json.load(f)
                    
                    status = data.get("metadata", {}).get("status", "unknown")
                    if status == "in_progress":
                        print(f"找到进行中的会话: {result['filename']}")
                        return data
                        
                except Exception as e:
                    continue
            
            return {}
            
        except Exception as e:
            print(f"查找进行中会话失败: {e}")
            return {}
    
    def _print_progress_info(self, parse_data: Dict[str, Any]):
        """打印进度信息"""
        try:
            progress = parse_data.get("progress", {})
            metadata = parse_data.get("metadata", {})
            
            completed_count = progress.get("completed_count", 0)
            total_count = progress.get("total_count", 0)
            current_file = progress.get("current_file", "")
            current_parser = progress.get("current_parser", "")
            
            print(f"📊 解析进度: {completed_count}/{total_count} 文件已完成")
            if current_file and current_parser != "completed":
                print(f"🔄 当前处理: {current_file} ({current_parser})")
            
            # 显示各解析器的完成情况
            parse_results = parse_data.get("parse_results", {})
            if parse_results:
                print("📈 各解析器进度:")
                for parser_name, results in parse_results.items():
                    success_count = len([r for r in results if r.get('success', False)])
                    print(f"  {parser_name}: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            print(f"显示进度信息失败: {e}")
    
    def generate_progress_report(self) -> tuple:
        """生成当前进度的报告"""
        return self.generate_report(include_progress=True)
    
    def list_available_results(self) -> List[Dict[str, str]]:
        """列出可用的解析结果文件"""
        results = self.result_manager.list_available_results()
        
        # 转换为兼容格式
        result_files = []
        for result in results:
            result_files.append({
                "filename": result["filename"],
                "filepath": result["filepath"],
                "mtime": datetime.datetime.fromisoformat(result["modified_time"]),
                "size": os.path.getsize(result["filepath"]) if os.path.exists(result["filepath"]) else 0
            })
        
        return result_files 