#!/usr/bin/env python3
"""
测试解析器重试功能

这个脚本用于测试ParseManager的重试机制是否正常工作
"""

import os
import sys
import logging
from unittest.mock import Mock, patch
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.parse_manager import ParseManager
from core.parsers.base import BaseParser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockFailingParser(BaseParser):
    """模拟会失败的解析器，用于测试重试功能"""
    
    def __init__(self, name: str, fail_count: int = 2):
        super().__init__(name)
        self.fail_count = fail_count  # 前几次调用会失败
        self.call_count = 0
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        self.call_count += 1
        logger.info(f"MockFailingParser {self.name} 第 {self.call_count} 次调用")
        
        if self.call_count <= self.fail_count:
            raise Exception(f"模拟失败 - 第 {self.call_count} 次调用")
        
        # 成功情况
        return {
            "result": f"成功解析 - 第 {self.call_count} 次调用",
            "call_count": self.call_count
        }
    
    def is_available(self) -> bool:
        return True


class MockAlwaysFailingParser(BaseParser):
    """模拟总是失败的解析器"""

    def __init__(self, name: str):
        super().__init__(name)
        self.call_count = 0

    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        self.call_count += 1
        logger.info(f"MockAlwaysFailingParser {self.name} 第 {self.call_count} 次调用")
        raise Exception(f"总是失败 - 第 {self.call_count} 次调用")

    def is_available(self) -> bool:
        return True


class MockMonkeyOCRStyleParser(BaseParser):
    """模拟MonkeyOCR风格的解析器，返回嵌套的result结构"""

    def __init__(self, name: str, fail_count: int = 2):
        super().__init__(name)
        self.fail_count = fail_count
        self.call_count = 0

    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        self.call_count += 1
        logger.info(f"MockMonkeyOCRStyleParser {self.name} 第 {self.call_count} 次调用")

        if self.call_count <= self.fail_count:
            # 模拟MonkeyOCR的失败返回格式
            return {"result": {
                'processed_image_url': None,
                'text_content': [f'模拟失败 - 第 {self.call_count} 次调用'],
                'html': f'模拟失败 - 第 {self.call_count} 次调用',
                'is_timeout': False,
                'processing_time': 0,
                'success': False,
                'error_type': 'simulated_failure'
            }}

        # 成功情况
        return {"result": {
            'processed_image_url': 'http://example.com/result.png',
            'text_content': [f'成功解析 - 第 {self.call_count} 次调用'],
            'html': f'<table>成功解析 - 第 {self.call_count} 次调用</table>',
            'is_timeout': False,
            'processing_time': 1.5,
            'success': True,
            'error_type': None
        }}

    def is_available(self) -> bool:
        return True


class MockVLLLMStyleParser(BaseParser):
    """模拟VL LLM风格的解析器，返回嵌套的result结构"""

    def __init__(self, name: str, fail_count: int = 2):
        super().__init__(name)
        self.fail_count = fail_count
        self.call_count = 0

    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        self.call_count += 1
        logger.info(f"MockVLLLMStyleParser {self.name} 第 {self.call_count} 次调用")

        if self.call_count <= self.fail_count:
            # 模拟VL LLM的失败返回格式
            return {"result": {
                'success': False,
                'error': f'VL LLM处理失败 - 第 {self.call_count} 次调用',
                'content': None
            }}

        # 成功情况
        return {"result": {
            'success': True,
            'error': None,
            'content': f'VL LLM成功解析 - 第 {self.call_count} 次调用'
        }}

    def is_available(self) -> bool:
        return True


def test_retry_success():
    """测试重试成功的情况"""
    logger.info("=" * 50)
    logger.info("测试重试成功的情况")
    logger.info("=" * 50)
    
    # 设置环境变量
    os.environ["PARSER_RETRY_COUNT"] = "3"
    os.environ["PARSER_RETRY_DELAY"] = "0.5"
    
    # 创建解析管理器
    manager = ParseManager()
    
    # 注册模拟解析器（前2次失败，第3次成功）
    failing_parser = MockFailingParser("test_failing", fail_count=2)
    manager.register_parser(failing_parser)
    
    # 测试文件信息
    file_info = {
        "filename": "test.png",
        "file_id": "test_001",
        "original_image": "/path/to/test.png"
    }
    
    # 执行解析
    result = manager.parse_with_parser_retry("test_failing", file_info)
    
    # 验证结果
    logger.info(f"解析结果: {result}")
    assert result["success"] == True, "解析应该成功"
    assert failing_parser.call_count == 3, f"应该调用3次，实际调用了{failing_parser.call_count}次"
    
    logger.info("✅ 重试成功测试通过")


def test_retry_failure():
    """测试重试失败的情况"""
    logger.info("=" * 50)
    logger.info("测试重试失败的情况")
    logger.info("=" * 50)
    
    # 设置环境变量
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "0.1"
    
    # 创建解析管理器
    manager = ParseManager()
    
    # 注册总是失败的解析器
    always_failing_parser = MockAlwaysFailingParser("test_always_failing")
    manager.register_parser(always_failing_parser)
    
    # 测试文件信息
    file_info = {
        "filename": "test.png",
        "file_id": "test_002",
        "original_image": "/path/to/test.png"
    }
    
    # 执行解析
    result = manager.parse_with_parser_retry("test_always_failing", file_info)
    
    # 验证结果
    logger.info(f"解析结果: {result}")
    assert result["success"] == False, "解析应该失败"
    assert always_failing_parser.call_count == 3, f"应该调用3次（1次初始+2次重试），实际调用了{always_failing_parser.call_count}次"
    assert "重试 2 次后失败" in result["error"], "错误信息应该包含重试信息"
    
    logger.info("✅ 重试失败测试通过")


def test_multiple_parsers_with_retry():
    """测试多路解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试多路解析器的重试功能")
    logger.info("=" * 50)

    # 设置环境变量
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "0.1"
    os.environ["PARSER_EXECUTION_MODE"] = "sequential"

    # 创建解析管理器
    manager = ParseManager()

    # 注册多个解析器
    parser1 = MockFailingParser("parser1", fail_count=1)  # 第2次成功
    parser2 = MockAlwaysFailingParser("parser2")  # 总是失败
    parser3 = MockFailingParser("parser3", fail_count=0)  # 第1次就成功

    manager.register_parser(parser1)
    manager.register_parser(parser2)
    manager.register_parser(parser3)

    # 测试文件信息
    file_info = {
        "filename": "test_multi.png",
        "file_id": "test_003",
        "original_image": "/path/to/test_multi.png"
    }

    # 执行多路解析，明确指定解析器名称和执行模式
    results = manager.parse_with_multiple_parsers(
        file_info,
        parser_names=["parser1", "parser2", "parser3"],
        execution_mode="sequential"
    )

    # 验证结果
    logger.info(f"多路解析结果: {results}")

    # 检查是否有结果返回
    assert len(results) == 3, f"应该有3个解析器的结果，实际有{len(results)}个"

    # parser1应该成功（第2次调用成功）
    assert results["parser1"]["success"] == True, "parser1应该成功"
    assert parser1.call_count == 2, f"parser1应该调用2次，实际{parser1.call_count}次"

    # parser2应该失败（总是失败）
    assert results["parser2"]["success"] == False, "parser2应该失败"
    assert parser2.call_count == 3, f"parser2应该调用3次，实际{parser2.call_count}次"

    # parser3应该成功（第1次就成功）
    assert results["parser3"]["success"] == True, "parser3应该成功"
    assert parser3.call_count == 1, f"parser3应该调用1次，实际{parser3.call_count}次"

    logger.info("✅ 多路解析器重试测试通过")


def test_monkeyocr_style_retry():
    """测试MonkeyOCR风格解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试MonkeyOCR风格解析器的重试功能")
    logger.info("=" * 50)

    # 设置环境变量
    os.environ["PARSER_RETRY_COUNT"] = "3"
    os.environ["PARSER_RETRY_DELAY"] = "0.5"

    # 创建解析管理器
    manager = ParseManager()

    # 注册MonkeyOCR风格的解析器（前2次失败，第3次成功）
    monkey_style_parser = MockMonkeyOCRStyleParser("test_monkey_style", fail_count=2)
    manager.register_parser(monkey_style_parser)

    # 测试文件信息
    file_info = {
        "filename": "test_monkey.png",
        "file_id": "test_004",
        "original_image": "/path/to/test_monkey.png"
    }

    # 执行解析
    result = manager.parse_with_parser_retry("test_monkey_style", file_info)

    # 验证结果
    logger.info(f"MonkeyOCR风格解析结果: {result}")
    assert result["success"] == True, "解析应该成功"
    assert monkey_style_parser.call_count == 3, f"应该调用3次，实际调用了{monkey_style_parser.call_count}次"
    assert result["result"]["success"] == True, "内层result的success应该为True"

    logger.info("✅ MonkeyOCR风格重试测试通过")


def test_vl_llm_style_retry():
    """测试VL LLM风格解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试VL LLM风格解析器的重试功能")
    logger.info("=" * 50)

    # 设置环境变量
    os.environ["PARSER_RETRY_COUNT"] = "3"
    os.environ["PARSER_RETRY_DELAY"] = "0.5"

    # 创建解析管理器
    manager = ParseManager()

    # 注册VL LLM风格的解析器（前2次失败，第3次成功）
    vl_llm_style_parser = MockVLLLMStyleParser("test_vl_llm_style", fail_count=2)
    manager.register_parser(vl_llm_style_parser)

    # 测试文件信息
    file_info = {
        "filename": "test_vl_llm.png",
        "file_id": "test_005",
        "original_image": "/path/to/test_vl_llm.png"
    }

    # 执行解析
    result = manager.parse_with_parser_retry("test_vl_llm_style", file_info)

    # 验证结果
    logger.info(f"VL LLM风格解析结果: {result}")
    assert result["success"] == True, "解析应该成功"
    assert vl_llm_style_parser.call_count == 3, f"应该调用3次，实际调用了{vl_llm_style_parser.call_count}次"
    assert result["result"]["success"] == True, "内层result的success应该为True"

    logger.info("✅ VL LLM风格重试测试通过")


def main():
    """主测试函数"""
    logger.info("开始测试解析器重试功能")

    try:
        test_retry_success()
        test_retry_failure()
        test_multiple_parsers_with_retry()
        test_monkeyocr_style_retry()
        test_vl_llm_style_retry()

        logger.info("=" * 50)
        logger.info("🎉 所有测试通过！重试功能正常工作")
        logger.info("=" * 50)

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
