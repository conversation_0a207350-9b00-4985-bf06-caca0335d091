#!/bin/bash

# ===============================================
# 测试集管理工具
# ===============================================

# 项目路径配置
PROJECT_ROOT_DIR="/data/projects/kingsoft/personal/workspace/tablerag/enhance"
DATASET_BASE_DIR="${PROJECT_ROOT_DIR}/dataset"
PARSE_RESULTS_BASE_DIR="${PROJECT_ROOT_DIR}/parse_results"
REPORTS_BASE_DIR="${PROJECT_ROOT_DIR}/reports"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色文本的函数
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 列出所有测试集
list_datasets() {
    print_info "可用的测试集:"
    if [ -d "${DATASET_BASE_DIR}" ]; then
        local count=0
        for dataset in "${DATASET_BASE_DIR}"/*; do
            if [ -d "$dataset" ]; then
                dataset_name=$(basename "$dataset")
                count=$((count + 1))
                
                # 获取统计信息
                local images_count=0
                local results_count=0
                local reports_count=0
                
                [ -d "${DATASET_BASE_DIR}/${dataset_name}/images" ] && images_count=$(ls "${DATASET_BASE_DIR}/${dataset_name}/images" 2>/dev/null | wc -l)
                [ -d "${PARSE_RESULTS_BASE_DIR}/${dataset_name}" ] && results_count=$(ls "${PARSE_RESULTS_BASE_DIR}/${dataset_name}"/*.json 2>/dev/null | wc -l)
                [ -d "${REPORTS_BASE_DIR}/${dataset_name}" ] && reports_count=$(ls "${REPORTS_BASE_DIR}/${dataset_name}"/*.html 2>/dev/null | wc -l)
                
                echo "   ${count}. ${dataset_name}"
                echo "      📁 目录: ${dataset}"
                echo "      📊 数据: 图片 ${images_count}, 解析结果 ${results_count}, 报告 ${reports_count}"
                
                # 显示最后修改时间
                if [ -d "${PARSE_RESULTS_BASE_DIR}/${dataset_name}" ]; then
                    local latest_result=$(ls -t "${PARSE_RESULTS_BASE_DIR}/${dataset_name}"/*.json 2>/dev/null | head -n1)
                    if [ -n "$latest_result" ]; then
                        local last_run=$(stat -c %y "$latest_result" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1)
                        echo "      🕒 最后运行: ${last_run}"
                    fi
                fi
                echo ""
            fi
        done
        
        if [ $count -eq 0 ]; then
            print_warning "没有找到测试集"
        fi
    else
        print_warning "测试集目录不存在: ${DATASET_BASE_DIR}"
    fi
}

# 创建新测试集
create_dataset() {
    local dataset_name="$1"
    
    if [ -z "$dataset_name" ]; then
        print_error "请提供测试集名称"
        echo "用法: $0 create <测试集名称>"
        return 1
    fi
    
    # 检查名称是否合法（只允许字母、数字、下划线、连字符）
    if [[ ! "$dataset_name" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        print_error "测试集名称只能包含字母、数字、下划线和连字符"
        return 1
    fi
    
    local dataset_dir="${DATASET_BASE_DIR}/${dataset_name}"
    
    if [ -d "$dataset_dir" ]; then
        print_warning "测试集 '${dataset_name}' 已存在"
        return 1
    fi
    
    print_info "创建测试集: ${dataset_name}"
    
    # 创建目录结构
    mkdir -p "${dataset_dir}/images"
    mkdir -p "${dataset_dir}/converted_pdfs"
    mkdir -p "${dataset_dir}/monkey_ocr"
    mkdir -p "${dataset_dir}/gen_data"
    mkdir -p "${PARSE_RESULTS_BASE_DIR}/${dataset_name}"
    mkdir -p "${REPORTS_BASE_DIR}/${dataset_name}"
    
    print_success "测试集 '${dataset_name}' 创建成功"
    echo "目录位置: ${dataset_dir}"
}

# 删除测试集
delete_dataset() {
    local dataset_name="$1"
    
    if [ -z "$dataset_name" ]; then
        print_error "请提供测试集名称"
        echo "用法: $0 delete <测试集名称>"
        return 1
    fi
    
    local dataset_dir="${DATASET_BASE_DIR}/${dataset_name}"
    local parse_dir="${PARSE_RESULTS_BASE_DIR}/${dataset_name}"
    local reports_dir="${REPORTS_BASE_DIR}/${dataset_name}"
    
    if [ ! -d "$dataset_dir" ]; then
        print_error "测试集 '${dataset_name}' 不存在"
        return 1
    fi
    
    # 确认删除
    print_warning "即将删除测试集 '${dataset_name}' 及其所有数据"
    echo "包括:"
    echo "  - 数据集: ${dataset_dir}"
    [ -d "$parse_dir" ] && echo "  - 解析结果: ${parse_dir}"
    [ -d "$reports_dir" ] && echo "  - 报告: ${reports_dir}"
    echo ""
    read -p "确认删除? (输入 'yes' 确认): " confirm
    
    if [ "$confirm" = "yes" ]; then
        print_info "正在删除测试集..."
        rm -rf "$dataset_dir"
        [ -d "$parse_dir" ] && rm -rf "$parse_dir"
        [ -d "$reports_dir" ] && rm -rf "$reports_dir"
        print_success "测试集 '${dataset_name}' 已删除"
    else
        print_info "取消删除操作"
    fi
}

# 复制测试集
copy_dataset() {
    local source_name="$1"
    local target_name="$2"
    
    if [ -z "$source_name" ] || [ -z "$target_name" ]; then
        print_error "请提供源测试集和目标测试集名称"
        echo "用法: $0 copy <源测试集> <目标测试集>"
        return 1
    fi
    
    local source_dir="${DATASET_BASE_DIR}/${source_name}"
    local target_dir="${DATASET_BASE_DIR}/${target_name}"
    
    if [ ! -d "$source_dir" ]; then
        print_error "源测试集 '${source_name}' 不存在"
        return 1
    fi
    
    if [ -d "$target_dir" ]; then
        print_error "目标测试集 '${target_name}' 已存在"
        return 1
    fi
    
    print_info "复制测试集: ${source_name} -> ${target_name}"
    
    # 复制数据集目录
    cp -r "$source_dir" "$target_dir"
    
    # 创建解析结果和报告目录（但不复制内容，因为需要重新解析）
    mkdir -p "${PARSE_RESULTS_BASE_DIR}/${target_name}"
    mkdir -p "${REPORTS_BASE_DIR}/${target_name}"
    
    print_success "测试集复制成功"
    print_info "注意: 只复制了原始数据，解析结果和报告需要重新生成"
}

# 清理测试集（保留原始数据，清除解析结果和报告）
clean_dataset() {
    local dataset_name="$1"
    
    if [ -z "$dataset_name" ]; then
        print_error "请提供测试集名称"
        echo "用法: $0 clean <测试集名称>"
        return 1
    fi
    
    local dataset_dir="${DATASET_BASE_DIR}/${dataset_name}"
    local parse_dir="${PARSE_RESULTS_BASE_DIR}/${dataset_name}"
    local reports_dir="${REPORTS_BASE_DIR}/${dataset_name}"
    
    if [ ! -d "$dataset_dir" ]; then
        print_error "测试集 '${dataset_name}' 不存在"
        return 1
    fi
    
    print_info "清理测试集 '${dataset_name}' 的解析结果和报告"
    
    # 清理生成的文件
    [ -d "${dataset_dir}/converted_pdfs" ] && rm -f "${dataset_dir}/converted_pdfs"/*
    [ -d "${dataset_dir}/monkey_ocr" ] && rm -f "${dataset_dir}/monkey_ocr"/*
    [ -d "$parse_dir" ] && rm -f "${parse_dir}"/*
    [ -d "$reports_dir" ] && rm -f "${reports_dir}"/*
    
    print_success "测试集 '${dataset_name}' 清理完成"
    print_info "保留了原始图片和生成数据，清除了解析结果和报告"
}

# 切换到测试集并运行
run_with_dataset() {
    local dataset_name="$1"
    
    if [ -z "$dataset_name" ]; then
        print_error "请提供测试集名称"
        echo "用法: $0 run <测试集名称>"
        return 1
    fi
    
    local dataset_dir="${DATASET_BASE_DIR}/${dataset_name}"
    
    if [ ! -d "$dataset_dir" ]; then
        print_error "测试集 '${dataset_name}' 不存在"
        return 1
    fi
    
    print_info "使用测试集 '${dataset_name}' 运行完整流程"
    
    # 切换到项目目录并运行
    cd "$(dirname "$0")"
    DATASET_NAME="$dataset_name" ./onekey.sh
}

# 强制重新生成数据并运行
force_run_with_dataset() {
    local dataset_name="$1"
    
    if [ -z "$dataset_name" ]; then
        print_error "请提供测试集名称"
        echo "用法: $0 force-run <测试集名称>"
        return 1
    fi
    
    local dataset_dir="${DATASET_BASE_DIR}/${dataset_name}"
    
    if [ ! -d "$dataset_dir" ]; then
        print_error "测试集 '${dataset_name}' 不存在"
        return 1
    fi
    
    print_info "强制重新生成测试集 '${dataset_name}' 的数据并运行完整流程"
    
    # 清理现有图片
    local images_dir="${dataset_dir}/images"
    if [ -d "$images_dir" ]; then
        print_info "清理现有图片文件..."
        rm -f "${images_dir}"/*
    fi
    
    # 切换到项目目录并运行
    cd "$(dirname "$0")"
    DATASET_NAME="$dataset_name" ./onekey.sh
}

# 显示使用帮助
show_help() {
    echo "测试集管理工具"
    echo ""
    echo "用法: $0 <命令> [参数]"
    echo ""
    echo "命令:"
    echo "  list                          列出所有测试集"
    echo "  create <名称>                 创建新测试集"
    echo "  delete <名称>                 删除测试集"
    echo "  copy <源名称> <目标名称>      复制测试集"
    echo "  clean <名称>                  清理测试集（保留原始数据）"
    echo "  run <名称>                    使用指定测试集运行流程（智能检测）"
    echo "  force-run <名称>              强制重新生成数据并运行流程"
    echo "  help                          显示此帮助信息"
    echo ""
    echo "📁 目录管理说明:"
    echo "  • 本地目录: 自动创建和管理本地测试集目录结构"
    echo "  • 云端目录: 运行解析流程时自动创建对应的云端文件夹"
    echo "  • 测试集隔离: 本地和云端都按测试集名称完全隔离"
    echo "  • 智能检测: run命令会检测是否已有图片数据，自动跳过生成步骤"
    echo ""
    echo "示例:"
    echo "  $0 list                       # 列出所有测试集"
    echo "  $0 create my_test             # 创建名为 my_test 的测试集"
    echo "  $0 run my_test                # 使用 my_test 运行流程（智能检测）"
    echo "  $0 force-run my_test          # 强制重新生成数据并运行"
    echo "  $0 copy my_test backup_test   # 复制测试集"
    echo "  $0 clean my_test              # 清理测试集的解析结果"
    echo "  $0 delete old_test            # 删除测试集"
}

# 主逻辑
case "$1" in
    "list"|"ls")
        list_datasets
        ;;
    "create"|"new")
        create_dataset "$2"
        ;;
    "delete"|"del"|"rm")
        delete_dataset "$2"
        ;;
    "copy"|"cp")
        copy_dataset "$2" "$3"
        ;;
    "clean")
        clean_dataset "$2"
        ;;
    "run")
        run_with_dataset "$2"
        ;;
    "force-run"|"force")
        force_run_with_dataset "$2"
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        print_error "未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac