# test10文件名匹配修复验证

## 🔍 问题确认

用户正确指出：**不能使用索引匹配，必须使用文件名匹配**，因为不同解析器的处理顺序可能不一致。

## 📊 文件名结构分析

### 实际数据结构：

**图片文件列表**：
```
0: 601328_交通银行：2023年度报告_17_table_1_1 copy.png
1: 601328_交通银行：2023年度报告_17_table_1_1.png
2: 601328_交通银行：2023年度报告_225_table_2_1.png
```

**KDC解析结果**：
```
0: filename=601328_交通银行：2023年度报告_17_table_1_1 copy.pdf, original_image=601328_交通银行：2023年度报告_17_table_1_1 copy.png
1: filename=601328_交通银行：2023年度报告_17_table_1_1.pdf, original_image=601328_交通银行：2023年度报告_17_table_1_1.png
```

**VL LLM解析结果**：
```
0: filename=601328_交通银行：2023年度报告_17_table_1_1 copy.pdf, original_image=601328_交通银行：2023年度报告_17_table_1_1 copy.png
1: filename=601328_交通银行：2023年度报告_17_table_1_1.pdf, original_image=601328_交通银行：2023年度报告_17_table_1_1.png
```

### 匹配策略：

✅ **完美匹配**: `original_image` 字段与图片文件名完全匹配  
✅ **基础名称匹配**: 去掉扩展名后的基础名称匹配  
✅ **多格式支持**: 支持 `.png` → `.pdf` 的转换匹配

## 🛠️ 修复实现

### 1. 创建文件名映射Map

```javascript
const createFileNameMap = (results, getFileNameFn, getOriginalImageFn) => {
  const map = new Map();
  results.forEach(result => {
    const fileName = getFileNameFn(result);     // 如: "xxx.pdf"
    const originalImage = getOriginalImageFn(result); // 如: "xxx.png"
    
    if (fileName) {
      const baseName = getImageBaseName(fileName); // "xxx"
      map.set(baseName, result);
      map.set(fileName, result);
    }
    
    if (originalImage) {
      const baseName = getImageBaseName(originalImage); // "xxx"
      map.set(baseName, result);
      map.set(originalImage, result); // 完全匹配
    }
  });
  return map;
};
```

### 2. 多重匹配策略

```javascript
const findResult = (map, targetBaseName, targetFileName) => {
  // 1. 优先基础名称匹配
  let result = map.get(targetBaseName);
  if (result) return result;
  
  // 2. 完整文件名匹配
  result = map.get(targetFileName);
  if (result) return result;
  
  // 3. PDF文件名匹配
  result = map.get(targetBaseName + '.pdf');
  if (result) return result;
  
  // 4. 图片格式转换匹配
  result = map.get(targetFileName.replace(/\.(pdf)$/i, '.png'));
  if (result) return result;
  
  return null;
};
```

### 3. 具体匹配示例

对于图片: `601328_交通银行：2023年度报告_17_table_1_1 copy.png`

匹配尝试顺序：
1. ✅ 基础名称: `"601328_交通银行：2023年度报告_17_table_1_1 copy"`
2. ✅ 完整文件名: `"601328_交通银行：2023年度报告_17_table_1_1 copy.png"`
3. ✅ PDF格式: `"601328_交通银行：2023年度报告_17_table_1_1 copy.pdf"`
4. ✅ Original image: `"601328_交通银行：2023年度报告_17_table_1_1 copy.png"` **← 最佳匹配**

## ✅ 验证方法

### 1. 控制台日志检查

刷新test10页面后，在浏览器控制台查看：

```javascript
// 应该看到文件名映射创建成功
Created file name maps: {
  kdcMap: 2,           // ✅ 应该大于0
  kdcPlainMap: 2,      // ✅ 应该大于0  
  kdcKdcMap: 2,        // ✅ 应该大于0
  monkeyOcrMap: 2,     // ✅ 应该大于0
  vlLlmMap: 2,         // ✅ 应该大于0
  // ...
}

// 应该看到匹配成功信息
Found results for 601328_交通银行：2023年度报告_17_table_1_1 copy.png (baseName: 601328_交通银行：2023年度报告_17_table_1_1 copy): {
  kdc: true,           // ✅ 应该为true
  kdcPlain: true,      // ✅ 应该为true
  kdcKdc: true,        // ✅ 应该为true
  vlLLM: true,         // ✅ 应该为true
  // ...
}
```

### 2. 界面检查

- ✅ **第1个案例**: 应该显示所有解析结果（绿色✅指示器）
- ✅ **第2个案例**: 应该显示所有解析结果（绿色✅指示器）  
- ⏳ **其他案例**: 显示等待解析状态（灰色指示器）

### 3. 详细内容检查

点击前2个案例，应该能看到：
- ✅ **原始文本**: 各路解析结果的文本内容
- ✅ **渲染结果**: 表格正确渲染
- ✅ **状态指示**: 解析成功状态

## 🎯 关键改进

### 索引匹配 → 文件名匹配

#### 修复前 (❌ 错误):
```javascript
// 假设所有解析器按相同顺序处理 - 错误假设!
const kdcResult = finalKdcResults[index] || null;
const vlLLMResult = finalVlLlmResults[index] || null;
```

#### 修复后 (✅ 正确):
```javascript
// 使用文件名精确匹配
const kdcResult = findResult(kdcMap, baseName, fileName);
const vlLLMResult = findResult(vlLlmMap, baseName, fileName);
```

### 鲁棒性增强

- ✅ **多字段支持**: `filename`, `fname`, `original_image`
- ✅ **多格式兼容**: `.png`, `.pdf`, 基础名称
- ✅ **容错处理**: 多种匹配策略，逐级降级
- ✅ **调试友好**: 详细的匹配日志

## 📊 预期效果

修复后，test10数据集应该显示：

| 案例 | 图片 | KDC | MonkeyOCR | VL-LLM | 状态 |
|------|------|-----|-----------|--------|------|
| 1 | ✅ | ✅ | ✅ | ✅ | 完成 |
| 2 | ✅ | ✅ | ✅ | ✅ | 完成 |
| 3-11 | ✅ | ⏳ | ⏳ | ⏳ | 等待 |

## 🚀 总结

通过改用文件名匹配策略，我们解决了：

1. **顺序不一致问题**: 不同解析器处理顺序不同
2. **文件名变换问题**: `.png` ↔ `.pdf` 格式转换
3. **字段名兼容问题**: 支持多种文件名字段
4. **调试困难问题**: 增加详细的匹配日志

这个修复确保了数据匹配的准确性和可靠性。 