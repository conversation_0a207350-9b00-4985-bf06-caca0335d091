# TableRAG 人工标注系统

基于现有的parser（多路OCR解析）和analyzer（结果展示）系统，新增人工标注功能，提供完整的表格解析准确率评估和报告生成能力。

## 🚀 系统架构

```
TableRAG 人工标注系统
├── parser/              # 现有：多路OCR解析流程
├── analyzer/            # 现有：React前端（已集成标注功能）
├── backend/             # 新增：统一后端服务
│   ├── api/             # RESTful API接口
│   ├── database/        # 数据库模块（MySQL支持）
│   ├── services/        # 业务逻辑服务
│   └── static/          # 静态文件服务
└── reports/             # 扩展：准确率报告生成
```

## ✨ 核心功能

### 1. 统一后端服务
- **替代原有HTTP文件服务器**：一个服务提供所有后端功能
- **数据集管理**：自动发现和管理测试数据集
- **解析结果访问**：统一的解析结果API接口
- **静态文件服务**：高效的图片和文件访问

### 2. 人工标注功能
- **标注编辑器**：支持Markdown和HTML格式的表格内容编辑
- **表格结构定义**：行数、列数、合并单元格等结构信息
- **标注状态管理**：草稿、完成、已审核等状态流转
- **批量操作**：支持批量创建、更新、删除标注

### 3. 准确率评估
- **多维度评估**：结构准确率、内容准确率、综合准确率
- **智能算法**：基于表格结构和内容相似度的准确率计算
- **批量评估**：支持整个数据集的批量准确率评估
- **实时计算**：标注完成后即可进行准确率评估

### 4. 报告生成
- **详细报告**：包含解析器对比、准确率趋势、详细结果等
- **多种格式**：支持JSON、HTML等多种报告格式
- **可视化展示**：图表、表格、统计数据的直观展示
- **导出功能**：支持报告的保存和导出

## 🛠️ 技术栈

### 后端技术
- **FastAPI**: 现代化Python Web框架，自动生成API文档
- **SQLAlchemy**: ORM数据库操作，支持多种数据库
- **MySQL**: 默认数据库（也支持SQLite/PostgreSQL）
- **Uvicorn**: 高性能ASGI服务器
- **Pydantic**: 数据验证和序列化

### 前端技术
- **React 18**: 现代化前端框架
- **Axios**: HTTP客户端
- **CSS3**: 响应式设计和动画效果
- **标注编辑器**: 自定义的表格内容编辑组件

### 数据库设计
- **tablerag_datasets**: 数据集管理
- **tablerag_images**: 图片文件管理
- **tablerag_annotations**: 人工标注数据
- **tablerag_accuracy_evaluations**: 准确率评估结果
- **tablerag_reports**: 生成的报告记录

> 所有表名都使用 `tablerag_` 前缀，避免与其他应用的表名冲突

## 🚀 快速开始

### 1. 一键启动（推荐）

```bash
./start_tablerag.sh
```

这个脚本会自动：
- 检查系统依赖
- 安装后端和前端依赖
- 启动后端服务（端口8000）
- 启动前端服务（端口3000）

### 2. 手动启动

**启动后端服务：**
```bash
cd backend
pip install -r requirements.txt
python main.py
```

**启动前端服务：**
```bash
cd analyzer
npm install
npm start
```

### 3. 访问系统

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📖 使用指南

### 1. 数据集管理
- 系统自动扫描 `dataset/` 目录下的数据集
- 支持创建、删除、同步数据集
- 自动发现图片文件并建立索引

### 2. 解析结果查看
- 在"解析分析"标签页查看多路OCR解析结果
- 支持KDC、MonkeyOCR、VL LLM等解析器结果对比
- 提供图片预览和表格渲染功能

### 3. 人工标注
- 切换到"人工标注"标签页
- 选择图片后点击"新建标注"
- 填写标注员信息和表格结构
- 输入标准答案的表格内容（支持Markdown/HTML）
- 保存标注并设置状态

### 4. 准确率评估
- 完成标注后可进行准确率评估
- 系统自动计算结构准确率和内容准确率
- 支持单个图片评估和批量评估
- 提供详细的评估结果和统计信息

### 5. 报告生成
- 基于标注数据和评估结果生成报告
- 支持解析器性能对比
- 提供准确率趋势分析
- 可导出HTML格式报告

## 🔧 配置说明

### 环境变量

```bash
# 服务配置
BACKEND_HOST=0.0.0.0          # 后端服务地址
BACKEND_PORT=8000             # 后端服务端口
DEBUG=false                   # 调试模式

# 数据库配置（MySQL）
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/tablerag

# 数据库配置（SQLite，用于开发）
DATABASE_URL=sqlite:///./tablerag_annotations.db
```

### MySQL数据库初始化

```bash
# 创建数据库
mysql -u root -p < backend/init_mysql.sql

# 或者手动创建
mysql -u root -p
CREATE DATABASE tablerag CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📊 API接口

### 数据集管理
- `GET /api/datasets` - 获取数据集列表
- `POST /api/datasets` - 创建数据集
- `GET /api/datasets/{name}/images` - 获取图片列表

### 标注功能
- `GET /api/annotations` - 获取标注列表
- `POST /api/annotations` - 创建标注
- `PUT /api/annotations/{id}` - 更新标注
- `DELETE /api/annotations/{id}` - 删除标注

### 准确率评估
- `POST /api/evaluate` - 评估单个图片
- `GET /api/evaluations/{dataset_name}` - 获取评估结果
- `GET /api/evaluations/{dataset_name}/summary` - 获取评估摘要

### 报告生成
- `POST /api/reports/generate` - 生成报告
- `GET /api/reports` - 获取报告列表
- `GET /api/reports/{id}` - 获取报告内容

完整API文档请访问：http://localhost:8000/docs

## 🔄 与原系统的集成

### 原来的启动方式
```bash
python -m http.server 10000  # 文件服务器
cd analyzer && npm start     # React前端
```

### 现在的启动方式
```bash
./start_tablerag.sh         # 一键启动所有服务
# 或者
cd backend && python main.py    # 统一后端服务
cd analyzer && npm start        # React前端（已集成标注功能）
```

### 主要改进
1. **统一服务架构**：一个后端服务提供所有功能
2. **数据库支持**：持久化存储标注和评估数据
3. **标注功能**：完整的人工标注工作流
4. **准确率评估**：自动化的准确率计算和对比
5. **报告生成**：详细的分析报告和可视化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件
- 项目讨论区
