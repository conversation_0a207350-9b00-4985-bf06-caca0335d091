#!/usr/bin/env python3
"""
分析和修复KDC结果文件名错乱问题的脚本
"""

import os
import json
import glob
from pathlib import Path
from datetime import datetime

def analyze_filename_mismatch(dataset_name="kingsoft"):
    """分析文件名不匹配的问题"""
    print(f"🔍 分析测试集 '{dataset_name}' 的文件名匹配问题...")
    
    # 获取各个目录
    dataset_dir = f"dataset/{dataset_name}"
    images_dir = f"{dataset_dir}/images"
    parse_results_dir = f"parse_results/{dataset_name}"
    
    # 1. 获取原始图片文件列表
    if not os.path.exists(images_dir):
        print(f"❌ 图片目录不存在: {images_dir}")
        return
    
    image_files = sorted([f for f in os.listdir(images_dir) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))])
    print(f"📸 原始图片文件 ({len(image_files)} 个):")
    for i, img in enumerate(image_files):
        print(f"   {i+1:2d}. {img}")
    
    # 2. 获取最新的解析结果
    latest_results_file = f"{parse_results_dir}/latest_results.json"
    if not os.path.exists(latest_results_file):
        print(f"❌ 解析结果文件不存在: {latest_results_file}")
        return
    
    with open(latest_results_file, 'r', encoding='utf-8') as f:
        parse_data = json.load(f)
    
    # 3. 分析各种结果的文件名映射
    print("\n📊 解析结果文件名映射分析:")
    
    # 分析processed_files
    processed_files = parse_data.get("processed_files", [])
    print(f"\n📄 已处理文件 ({len(processed_files)} 个):")
    for i, file in enumerate(processed_files):
        print(f"   {i+1:2d}. {file.get('fname', 'N/A')} (ID: {file.get('id', 'N/A')})")
    
    # 分析uploaded_files
    uploaded_files = parse_data.get("uploaded_files", [])
    print(f"\n📤 上传文件映射 ({len(uploaded_files)} 个):")
    for i, file in enumerate(uploaded_files):
        print(f"   {i+1:2d}. PDF: {file.get('fname', 'N/A')} <- 图片: {file.get('original_image', 'N/A')}")
    
    # 分析KDC结果
    kdc_results = parse_data.get("kdc_results", [])
    print(f"\n🔍 KDC解析结果 ({len(kdc_results)} 个):")
    for i, result in enumerate(kdc_results):
        filename = result.get("filename", "N/A")
        success = result.get("success", False)
        print(f"   {i+1:2d}. {filename} - {'✅' if success else '❌'}")
    
    # 分析本地MonkeyOCR结果
    monkey_ocr_local = parse_data.get("monkey_ocr_local_results", [])
    print(f"\n🐒 本地MonkeyOCR结果 ({len(monkey_ocr_local)} 个):")
    for i, result in enumerate(monkey_ocr_local):
        filename = result.get("filename", "N/A")
        original_image = result.get("original_image", "N/A")
        print(f"   {i+1:2d}. PDF: {filename} <- 图片: {original_image}")
    
    # 检查不匹配的情况
    print("\n⚠️  潜在问题分析:")
    
    # 检查图片文件与上传文件的匹配
    print("\n1. 图片文件与上传文件匹配:")
    for img_file in image_files:
        pdf_name = f"{Path(img_file).stem}.pdf"
        found = False
        for uploaded in uploaded_files:
            if uploaded.get("original_image") == img_file:
                found = True
                if uploaded.get("fname") != pdf_name:
                    print(f"   ⚠️  {img_file} -> 期望PDF: {pdf_name}, 实际PDF: {uploaded.get('fname')}")
                break
        if not found:
            print(f"   ❌ {img_file} -> 未找到对应的上传记录")
    
    # 检查KDC结果与文件的匹配
    print("\n2. KDC结果与文件匹配:")
    if len(kdc_results) != len(processed_files):
        print(f"   ⚠️  KDC结果数量 ({len(kdc_results)}) 与处理文件数量 ({len(processed_files)}) 不匹配")
    
    for i, (kdc_result, processed_file) in enumerate(zip(kdc_results, processed_files)):
        kdc_filename = kdc_result.get("filename", "N/A")
        processed_filename = processed_file.get("fname", "N/A")
        if kdc_filename != processed_filename:
            print(f"   ⚠️  位置 {i+1}: KDC结果文件名 '{kdc_filename}' != 处理文件名 '{processed_filename}'")
    
    return {
        "image_files": image_files,
        "uploaded_files": uploaded_files,
        "processed_files": processed_files,
        "kdc_results": kdc_results,
        "monkey_ocr_local": monkey_ocr_local,
        "parse_data": parse_data
    }

def fix_filename_mismatch(dataset_name="kingsoft"):
    """修复文件名不匹配的问题"""
    print(f"\n🔧 开始修复测试集 '{dataset_name}' 的文件名匹配问题...")
    
    # 先分析问题
    analysis = analyze_filename_mismatch(dataset_name)
    if not analysis:
        return False
    
    parse_data = analysis["parse_data"]
    parse_results_dir = f"parse_results/{dataset_name}"
    
    # 创建正确的文件名映射
    print("\n📝 创建正确的文件名映射...")
    
    # 建立原始图片 -> PDF文件名的映射
    image_to_pdf = {}
    for uploaded in analysis["uploaded_files"]:
        original_image = uploaded.get("original_image")
        pdf_fname = uploaded.get("fname")
        if original_image and pdf_fname:
            image_to_pdf[original_image] = pdf_fname
    
    print("图片到PDF映射:")
    for img, pdf in image_to_pdf.items():
        print(f"   {img} -> {pdf}")
    
    # 建立PDF文件名 -> 处理文件信息的映射
    pdf_to_processed = {}
    for processed in analysis["processed_files"]:
        pdf_fname = processed.get("fname")
        if pdf_fname:
            pdf_to_processed[pdf_fname] = processed
    
    print("\nPDF到处理文件映射:")
    for pdf, processed in pdf_to_processed.items():
        print(f"   {pdf} -> ID: {processed.get('id')}")
    
    # 修复KDC结果的文件名匹配
    print("\n🔧 修复KDC结果文件名匹配...")
    fixed_kdc_results = []
    
    for i, kdc_result in enumerate(analysis["kdc_results"]):
        if i < len(analysis["processed_files"]):
            # 使用正确的文件信息
            processed_file = analysis["processed_files"][i]
            correct_filename = processed_file.get("fname")
            
            # 更新KDC结果的文件名
            fixed_result = kdc_result.copy()
            fixed_result["filename"] = correct_filename
            fixed_result["file_id"] = processed_file.get("id")
            
            # 添加原始图片信息
            for img, pdf in image_to_pdf.items():
                if pdf == correct_filename:
                    fixed_result["original_image"] = img
                    break
            
            fixed_kdc_results.append(fixed_result)
            
            old_filename = kdc_result.get("filename", "N/A")
            if old_filename != correct_filename:
                print(f"   ✅ 修复 {i+1}: '{old_filename}' -> '{correct_filename}'")
        else:
            fixed_kdc_results.append(kdc_result)
    
    # 修复其他结果的文件名匹配
    print("\n🔧 修复其他结果的文件名匹配...")
    
    def fix_results_list(results_list, result_type_name):
        """修复结果列表的文件名匹配"""
        fixed_results = []
        for i, result in enumerate(results_list):
            if i < len(analysis["processed_files"]):
                processed_file = analysis["processed_files"][i]
                correct_filename = processed_file.get("fname")
                
                fixed_result = result.copy()
                fixed_result["filename"] = correct_filename
                
                # 添加原始图片信息
                for img, pdf in image_to_pdf.items():
                    if pdf == correct_filename:
                        fixed_result["original_image"] = img
                        break
                
                fixed_results.append(fixed_result)
                
                old_filename = result.get("filename", "N/A")
                if old_filename != correct_filename:
                    print(f"   ✅ 修复{result_type_name} {i+1}: '{old_filename}' -> '{correct_filename}'")
            else:
                fixed_results.append(result)
        return fixed_results
    
    # 修复各种结果
    fixed_kdc_plain = fix_results_list(parse_data.get("kdc_plain_results", []), "KDC Plain")
    fixed_kdc_kdc = fix_results_list(parse_data.get("kdc_kdc_results", []), "KDC KDC")
    fixed_monkey_ocr = fix_results_list(parse_data.get("monkey_ocr_results", []), "MonkeyOCR")
    fixed_monkey_ocr_v2 = fix_results_list(parse_data.get("monkey_ocr_results_v2", []), "MonkeyOCR V2")
    fixed_vl_llm = fix_results_list(parse_data.get("vl_llm_results", []), "VL LLM")
    
    # 保存修复后的结果
    print("\n💾 保存修复后的结果...")
    
    fixed_parse_data = parse_data.copy()
    fixed_parse_data["kdc_results"] = fixed_kdc_results
    fixed_parse_data["kdc_plain_results"] = fixed_kdc_plain
    fixed_parse_data["kdc_kdc_results"] = fixed_kdc_kdc
    fixed_parse_data["monkey_ocr_results"] = fixed_monkey_ocr
    fixed_parse_data["monkey_ocr_results_v2"] = fixed_monkey_ocr_v2
    fixed_parse_data["vl_llm_results"] = fixed_vl_llm
    
    # 添加修复标记
    fixed_parse_data["metadata"]["fixed_filename_mismatch"] = True
    fixed_parse_data["metadata"]["fix_timestamp"] = datetime.now().isoformat()
    
    # 备份原始文件
    import shutil
    backup_file = f"{parse_results_dir}/latest_results_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    shutil.copy2(f"{parse_results_dir}/latest_results.json", backup_file)
    print(f"   📦 原始文件已备份到: {backup_file}")
    
    # 保存修复后的文件
    with open(f"{parse_results_dir}/latest_results.json", 'w', encoding='utf-8') as f:
        json.dump(fixed_parse_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"   ✅ 修复后的结果已保存到: {parse_results_dir}/latest_results.json")
    
    # 验证修复结果
    print("\n✅ 修复完成，验证结果:")
    print(f"   KDC结果: {len(fixed_kdc_results)} 个")
    print(f"   KDC Plain结果: {len(fixed_kdc_plain)} 个")
    print(f"   KDC KDC结果: {len(fixed_kdc_kdc)} 个")
    print(f"   MonkeyOCR结果: {len(fixed_monkey_ocr)} 个")
    print(f"   MonkeyOCR V2结果: {len(fixed_monkey_ocr_v2)} 个")
    print(f"   VL LLM结果: {len(fixed_vl_llm)} 个")
    
    return True

if __name__ == "__main__":
    import sys
    
    dataset_name = sys.argv[1] if len(sys.argv) > 1 else "kingsoft"
    
    print("🔍 KDC结果文件名匹配分析和修复工具")
    print("=" * 50)
    
    # 分析问题
    analyze_filename_mismatch(dataset_name)
    
    # 询问是否修复
    print("\n" + "=" * 50)
    response = input("是否要修复文件名匹配问题？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        success = fix_filename_mismatch(dataset_name)
        if success:
            print("\n🎉 修复完成！现在可以重新生成报告来验证修复效果。")
            print("建议运行: python generate_report.py")
        else:
            print("\n❌ 修复失败，请检查错误信息。")
    else:
        print("\n取消修复操作。") 