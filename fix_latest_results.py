#!/usr/bin/env python3
"""
修复缺失的latest_results.json文件

用于修复那些解析过程中断导致没有生成latest_results.json的测试集
"""

import os
import sys
import argparse

# 添加parser/src到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'parser', 'src'))

from core.result_manager import ResultManager

def fix_latest_results(dataset_name: str):
    """
    为指定数据集修复latest_results.json
    
    Args:
        dataset_name: 数据集名称
    """
    print(f"🔧 修复数据集 '{dataset_name}' 的latest_results.json...")
    
    # 创建结果管理器
    result_manager = ResultManager(dataset_name)
    
    # 检查是否已存在latest_results.json
    latest_file = os.path.join(result_manager.parse_results_dir, "latest_results.json")
    if os.path.exists(latest_file):
        print(f"⚠️  latest_results.json 已存在: {latest_file}")
        response = input("是否要重新创建? (y/N): ")
        if response.lower() != 'y':
            print("❌ 取消操作")
            return False
    
    # 从最新结果文件创建latest_results.json
    created_file = result_manager.create_latest_results_from_most_recent()
    
    if created_file:
        print(f"✅ 成功创建latest_results.json: {created_file}")
        return True
    else:
        print(f"❌ 创建latest_results.json失败")
        return False

def list_datasets_without_latest():
    """列出没有latest_results.json的数据集"""
    parse_results_dir = "parse_results"
    if not os.path.exists(parse_results_dir):
        print(f"❌ 解析结果目录不存在: {parse_results_dir}")
        return []
    
    datasets_without_latest = []
    
    for dataset_name in os.listdir(parse_results_dir):
        dataset_dir = os.path.join(parse_results_dir, dataset_name)
        if not os.path.isdir(dataset_dir):
            continue
        
        latest_file = os.path.join(dataset_dir, "latest_results.json")
        if not os.path.exists(latest_file):
            # 检查是否有解析结果文件
            has_results = any(
                f.startswith("parse_results_") and f.endswith(".json")
                for f in os.listdir(dataset_dir)
            )
            if has_results:
                datasets_without_latest.append(dataset_name)
    
    return datasets_without_latest

def main():
    parser = argparse.ArgumentParser(description="修复缺失的latest_results.json文件")
    parser.add_argument("dataset_name", nargs="?", help="数据集名称")
    parser.add_argument("--list", action="store_true", help="列出缺失latest_results.json的数据集")
    parser.add_argument("--fix-all", action="store_true", help="修复所有缺失latest_results.json的数据集")
    
    args = parser.parse_args()
    
    if args.list:
        print("🔍 查找缺失latest_results.json的数据集...")
        datasets = list_datasets_without_latest()
        if datasets:
            print(f"📋 找到 {len(datasets)} 个缺失latest_results.json的数据集:")
            for dataset in datasets:
                print(f"  - {dataset}")
        else:
            print("✅ 所有数据集都有latest_results.json")
        return
    
    if args.fix_all:
        print("🔧 修复所有缺失latest_results.json的数据集...")
        datasets = list_datasets_without_latest()
        if not datasets:
            print("✅ 所有数据集都有latest_results.json")
            return
        
        success_count = 0
        for dataset in datasets:
            print(f"\n--- 处理数据集: {dataset} ---")
            if fix_latest_results(dataset):
                success_count += 1
        
        print(f"\n🎯 修复完成: {success_count}/{len(datasets)} 个数据集成功")
        return
    
    if args.dataset_name:
        success = fix_latest_results(args.dataset_name)
        sys.exit(0 if success else 1)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
