{"name": "tablerag-analyzer", "version": "1.0.0", "description": "React-based analyzer for TableRAG parsing results", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "recharts": "^3.0.2", "remark-gfm": "^3.0.1", "styled-components": "^6.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "PORT=3000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "devDependencies": {"http-proxy-middleware": "^3.0.5"}}