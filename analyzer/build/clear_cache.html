<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除Analyzer缓存</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .step { margin: 20px 0; padding: 15px; background: #e8f5e8; border-left: 4px solid #27ae60; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .success { background: #d4edda; border-left: 4px solid #28a745; }
        code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: Monaco, monospace; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Analyzer缓存清除指南</h1>
        
        <div class="success">
            <h2>✅ 修复已完成</h2>
            <p>KDC解析结果与文件名的对应关系已经修复成功！wi数据集的62个文件现在都正确匹配。</p>
        </div>

        <div class="warning">
            <h2>⚠️ 如果你还看到旧数据</h2>
            <p>这是由于浏览器缓存导致的，请按以下步骤清除缓存：</p>
        </div>

        <div class="step">
            <h3>方法1: 强制刷新页面</h3>
            <p>在analyzer页面按：<code>Ctrl + F5</code> 或 <code>Ctrl + Shift + R</code></p>
        </div>

        <div class="step">
            <h3>方法2: 清除浏览器缓存</h3>
            <ol>
                <li>按 <code>F12</code> 打开开发者工具</li>
                <li>右键点击刷新按钮</li>
                <li>选择"清空缓存并强制刷新"</li>
            </ol>
        </div>

        <div class="step">
            <h3>方法3: 清除本地存储</h3>
            <ol>
                <li>在analyzer页面按 <code>F12</code></li>
                <li>点击"Application"选项卡</li>
                <li>在左侧找到"Local Storage"和"Session Storage"</li>
                <li>右键删除所有条目</li>
                <li>刷新页面</li>
            </ol>
        </div>

        <div class="step">
            <h3>验证修复结果</h3>
            <p>清除缓存后，wi数据集第一条记录应该显示：</p>
            <ul>
                <li><strong>文件名</strong>: 601328_交通银行：2023年度报告_17_table_1_1.pdf</li>
                <li><strong>图片</strong>: 601328_交通银行：2023年度报告_17_table_1_1.png</li>
                <li><strong>内容</strong>: 交通银行2023年财务数据表格（包含年初余额、本年计提等）</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:3000" class="btn">🚀 返回Analyzer</a>
            <button onclick="clearCache()" class="btn" style="background: #dc3545;">🗑️ 一键清除缓存</button>
        </div>
    </div>

    <script>
        function clearCache() {
            // 清除各种缓存
            if ('localStorage' in window) {
                localStorage.clear();
            }
            if ('sessionStorage' in window) {
                sessionStorage.clear();
            }
            
            // 清除Service Worker缓存
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(function(registrations) {
                    for(let registration of registrations) {
                        registration.unregister();
                    }
                });
            }
            
            // 强制刷新页面
            window.location.href = 'http://localhost:3000?t=' + Date.now();
        }
    </script>
</body>
</html> 