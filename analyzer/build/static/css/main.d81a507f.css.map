{"version": 3, "file": "static/css/main.d81a507f.css", "mappings": "AACA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,UAAW,CANX,mIAEY,CAKZ,eACF,CAEA,KACE,uEAEF,CAGA,WAEE,aAAc,CADd,cAAe,CAGf,eAAgB,CADhB,cAEF,CAEA,SAGE,kBAAmB,CAGnB,UAAW,CALX,YAAa,CAIb,cAAe,CAHf,sBAAuB,CAEvB,YAGF,CAWA,gBAHE,aAUF,CAPA,SAEE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CAId,YAEF,CAGA,KACE,oBAAqB,CAErB,UAAW,CAKX,oBAEF,CAOA,mBACE,wBACF,CAOA,qBACE,wBACF,CAGA,YACE,kBACF,CAEA,YACE,aAAc,CAEd,eAAgB,CADhB,iBAEF,CAEA,cAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,gBAAiB,CADjB,UAKF,CAEA,oBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,MACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,aAEE,+BAAgC,CAChC,eAAgB,CAFhB,iBAGF,CAEA,WACE,YACF,CAGA,OAEE,wBAAyB,CACzB,kBAAmB,CAFnB,UAGF,CAEA,oBAIE,+BAAgC,CAFhC,YAAa,CACb,eAEF,CAEA,UAEE,eACF,CAEA,iDAJE,wBAMF,CAGA,eASE,kBAAmB,CAHnB,sBAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,eACE,eAAiB,CACjB,iBAAkB,CAElB,eAAgB,CADhB,cAAe,CAEf,aAAc,CACd,iBACF,CAEA,aAIE,eAAgB,CAChB,WAAY,CAGZ,UAAW,CADX,cAAe,CADf,cAAe,CALf,iBAAkB,CAElB,UAAW,CADX,QAAS,CAOT,YACF,CAEA,mBACE,UACF,CChMA,kBACE,eAAiB,CACjB,iBAAkB,CAGlB,8BAAwC,CADxC,kBAAmB,CADnB,YAGF,CAEA,yBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,iBACF,CAEA,wBAEE,UAAW,CADX,eAAgB,CAEhB,cACF,CAEA,2BAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,OAEF,CAEA,yBAOE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CAPf,QAAO,CAKP,cAAe,CAJf,eAAgB,CAChB,gBAMF,CAEA,+BAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,kCACE,wBAAyB,CACzB,kBAAmB,CACnB,UACF,CAEA,8BAIE,qBAAuB,CAFvB,wBAAyB,CACzB,iBAAkB,CAIlB,UAAW,CAFX,cAAe,CACf,cAAe,CAGf,cAAe,CARf,gBAAiB,CAOjB,uBAEF,CAEA,mDACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,uCACE,kBAAmB,CACnB,UACF,CAEA,0BACE,UAAW,CACX,iBACF,CAEA,wBAGE,kBAAmB,CAFnB,aAAc,CACd,YAAa,CAEb,OACF,CAEA,uBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CAKb,cAAe,CAJf,6BAA8B,CAE9B,eAGF,CAEA,0BACE,UACF,CAEA,iCACE,aACF,CAEA,wBACE,UAAW,CACX,cACF,CAGA,yBACE,yBAEE,mBAEF,CAEA,oDALE,qBAAsB,CAEtB,OAMF,CAEA,yBACE,cACF,CAEA,uBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CClIA,WACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CAGxC,YAAa,CACb,qBAAsB,CAFtB,0BAA2B,CAD3B,kBAIF,CAEA,kBAGE,kBAAmB,CAGnB,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAN1B,YAAa,CACb,6BAA8B,CAE9B,iBAIF,CAEA,qBAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CADf,QAAS,CAFT,6BAIF,CAEA,iBAEE,UAAW,CADX,cAAe,CAEf,kBACF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,4BAWE,kBAAmB,CAPnB,qBAAuB,CAFvB,qBAAsB,CACtB,iBAAkB,CAIlB,UAAW,CAFX,cAAe,CAKf,YAAa,CAJb,cAAe,CAMf,sBAAuB,CAHvB,cAAe,CARf,gBAAiB,CAOjB,uBAKF,CAEA,kCACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,mCAIE,2BAA4B,CAH5B,wBAAyB,CACzB,oBAAqB,CACrB,UAEF,CAEA,iBACE,GACE,kBACF,CACA,IACE,qBACF,CACA,GACE,kBACF,CACF,CAEA,uBAIE,qBAAuB,CAFvB,qBAAsB,CACtB,iBAAkB,CAIlB,UAAW,CAFX,cAAe,CACf,cAAe,CAGf,cAAe,CARf,gBAAiB,CAOjB,uBAEF,CAEA,4CACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,gCACE,kBAAmB,CACnB,UACF,CAEA,mBAGE,UAAW,CAFX,iBAAkB,CAClB,iBAEF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAOA,cAEE,cAAe,CADf,kBAEF,CAEA,kBAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,aAAc,CACd,eAAgB,CALhB,WAMF,CAEA,sBAEE,wBAAyB,CADzB,WAAY,CAEZ,yBACF,CAEA,iBACE,YAAa,CACb,iBACF,CAEA,eACE,aAAc,CACd,kBACF,CAEA,iBAIE,qBAAuB,CAFvB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CACd,cAAe,CALf,gBAAiB,CAMjB,uBACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAEA,iBAGE,UAAW,CACX,iBAAkB,CAHlB,iBAAkB,CAClB,iBAGF,CAEA,qBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAEA,iBAGE,wBAAyB,CADzB,+BAAgC,CADhC,YAGF,CAEA,mBACE,QAAO,CACP,eAAgB,CAChB,WACF,CAGA,wBAEE,gBAAuB,CADvB,eAAgB,CAGhB,QAAS,CADT,SAEF,CAEA,+BAEE,kBAAmB,CADnB,gBAEF,CAEA,uBAEE,OAAQ,CADR,mCAEF,CAEA,sBACE,WACF,CAEA,uBACE,cACF,CAOA,kCAJE,cAeF,CAXA,WAEE,kBAAmB,CAOnB,qBAAuB,CAJvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CANf,YAAa,CAGb,iBAAkB,CADlB,gBAAiB,CAKjB,uBAGF,CAEA,iBACE,wBAAyB,CACzB,oBAAqB,CACrB,8BACF,CAEA,oBACE,wBAAyB,CACzB,oBACF,CAEA,iBAEE,aAAc,CADd,eAAgB,CAEhB,cAAe,CACf,iBACF,CAEA,gBACE,QAAO,CACP,gBACF,CAEA,oBAEE,UAAW,CADX,eAAgB,CAEhB,iBAAkB,CAClB,oBACF,CAEA,gBAIE,UAAW,CAHX,YAAa,CAIb,cAAe,CAFf,cAAe,CADf,QAIF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,OAAQ,CACR,cACF,CAEA,uBACE,cAAe,CACf,iBAAkB,CAClB,kBACF,CAEA,kBAGE,iBAAkB,CADlB,UAAW,CADX,SAGF,CAEA,4BACE,wBACF,CAEA,2BACE,wBACF,CAEA,6BACE,wBACF,CAEA,4BACE,wBACF,CAGA,cAME,4BAA8B,CAD9B,sBAA6B,CAF7B,iBAAkB,CAClB,cAAe,CAHf,kBAAmB,CACnB,YAKF,CAaA,0BACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,wBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,sBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,OAEF,CAEA,aACE,cAAe,CACf,aACF,CAEA,aAEE,QAAO,CADP,eAAgB,CAEhB,eACF,CAEA,wBAKE,+BAAgC,CAHhC,UAAW,CADX,cAAe,CAGf,iBAAkB,CADlB,UAGF,CAEA,qBACE,MACE,UACF,CACA,IACE,SACF,CACF,CAGA,yBAOE,sCAEE,mBAAoB,CADpB,qBAAsB,CAEtB,OACF,CAEA,mBACE,sBACF,CAEA,WAEE,mBAAoB,CADpB,qBAAsB,CAEtB,eACF,CAEA,iBAEE,iBAAkB,CADlB,eAEF,CAEA,gBACE,aACF,CAEA,gBACE,cACF,CAEA,sBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CAEA,aACE,cACF,CAEA,wBACE,kBAAmB,CACnB,iBACF,CACF,CAGA,kBAGE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAJlB,aAAc,CACd,YAIF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,cAME,eAAiB,CAHjB,wBAAyB,CACzB,iBAAkB,CAHlB,QAAO,CAIP,cAAe,CAHf,eAKF,CAEA,oBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,eAME,eAAiB,CAHjB,wBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CANf,QAAO,CAIP,cAAe,CAHf,eAMF,CAEA,qBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,kBAGE,kBAAmB,CADnB,wBAAyB,CAGzB,iBAAkB,CADlB,UAAY,CAGZ,cAAe,CADf,cAAe,CALf,eAAgB,CAOhB,kBACF,CAEA,uCACE,kBAAmB,CACnB,oBACF,CAEA,2BAEE,kBAAmB,CADnB,UAEF,CAEA,aAEE,aAAc,CADd,cAAe,CAEf,iBACF,CCjhBA,qBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,qBAIE,eAAiB,CACjB,iBAAkB,CAClB,+BAAyC,CACzC,YAAa,CACb,qBAAsB,CALtB,eAAgB,CADhB,cAAe,CAOf,eAAgB,CARhB,iBASF,CAEA,mBAeE,kBAAmB,CAXnB,oBAA8B,CAC9B,WAAY,CAQZ,iBAAkB,CAPlB,UAAY,CAGZ,cAAe,CAKf,YAAa,CAPb,cAAe,CACf,eAAiB,CAIjB,WAAY,CAIZ,sBAAuB,CAfvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAeT,oCAAsC,CANtC,UAAW,CADX,YAQF,CAEA,yBACE,oBACF,CAEA,mBAEE,wBAAyB,CACzB,+BAAgC,CAEhC,UAAW,CACX,cAAe,CAFf,eAAgB,CAHhB,iBAAkB,CAMlB,oBACF,CAEA,6BAIE,kBAAmB,CAGnB,wBAAyB,CALzB,YAAa,CADb,QAAO,CAEP,sBAAuB,CAGvB,gBAAiB,CADjB,YAGF,CAEA,mBAIE,iBAAkB,CAClB,+BAA0C,CAH1C,eAAgB,CADhB,cAAe,CAEf,kBAGF,CAEA,mBACE,aAAc,CACd,cAAe,CAEf,YAAa,CADb,iBAEF,CAEA,sBAME,kBAAmB,CAJnB,wBAAyB,CACzB,4BAA6B,CAC7B,YAAa,CAGb,QAAS,CAFT,6BAA8B,CAJ9B,iBAOF,CAEA,eAGE,OACF,CAEA,yBAJE,kBAAmB,CADnB,YAcF,CATA,UAOE,cAAe,CACf,eAAiB,CANjB,qBAAuB,CAIvB,sBAAuB,CAHvB,mBAAqB,CAFrB,oBAQF,CAEA,YAIE,UAAW,CADX,cAAe,CAFf,cAAe,CACf,iBAGF,CAEA,iBACE,YAAa,CACb,QACF,CAEA,iBAIE,qBAAuB,CAFvB,wBAAyB,CACzB,iBAAkB,CAElB,UAAW,CACX,cAAe,CACf,cAAe,CANf,gBAAiB,CAOjB,uBACF,CAQA,oDACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,mCACE,wBAAyB,CACzB,oBACF,CAGA,yBACE,qBACE,YACF,CAEA,qBAEE,gBAAiB,CADjB,eAEF,CAEA,mBAEE,cAAe,CADf,iBAEF,CAEA,6BACE,YACF,CAEA,sBAEE,qBAAsB,CADtB,iBAEF,CAEA,iBAEE,iBAAkB,CADlB,UAEF,CAEA,4BACE,eACF,CACF,CAGA,qBACE,6BACF,CAEA,qBACE,8BACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CCtNA,gBAGE,aACF,CAEA,wCAJE,WAAY,CADZ,UAQF,CAEA,sBAEE,kBAAmB,CAGnB,UAAW,CAJX,YAAa,CAKb,iBAAkB,CAHlB,sBAAuB,CACvB,gBAGF,CAEA,eAKE,wBAAyB,CAEzB,yBAA0B,CAD1B,iBAAkB,CAHlB,UAAW,CACX,iBAAkB,CAHlB,YAAa,CACb,iBAMF,CAGA,8BAEE,wBAAyB,CAEzB,cAAe,CADf,QAAS,CAFT,UAIF,CAEA,sDAME,oBAAqB,CAJrB,wBAAyB,CACzB,gBAAiB,CACjB,eAAgB,CAChB,kBAAmB,CAEnB,oBACF,CAEA,2BACE,wBAAyB,CAEzB,UAAW,CADX,eAAgB,CAEhB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,UACF,CAEA,+CACE,wBACF,CAEA,uCACE,wBACF,CAGA,YACE,iCAAqC,CACrC,cACF,CAEA,eACE,wBAAyB,CACzB,eACF,CAEA,eACE,eAAgB,CAChB,eAAgB,CAChB,sBACF,CAEA,YAOE,oBAAqB,CAJrB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAJf,QAAS,CAOT,gBAAiB,CACjB,eAAgB,CAPhB,WAAY,CAIZ,oBAIF,CAsBA,0BAVE,oBAAqB,CANrB,wBAAyB,CAQzB,wBAAyB,CAPzB,iBAAkB,CAMlB,UAAW,CALX,iCAAqC,CACrC,cAAe,CAMf,WAAY,CALZ,eAAgB,CANhB,QAAS,CAaT,iBAAkB,CADlB,gBAAiB,CAEjB,eAAgB,CAbhB,YAAa,CAMb,oBA2BF,CAGA,OAEE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CAMd,cAAe,CADf,YAAa,CADb,YAGF,CAGA,mCAEE,UAAW,CADX,SAEF,CAEA,yCACE,kBAAmB,CACnB,iBACF,CAEA,yCACE,kBAAmB,CACnB,iBACF,CAEA,+CACE,kBACF,CAGA,yBACE,8BACE,cACF,CAEA,sDAEE,eACF,CAMA,wBAHE,cAMF,CAHA,YAEE,eACF,CAEA,0BAEE,cAAe,CACf,WACF,CACF,CC5LA,aAOE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CALlB,wCAA6C,CAC7C,cAAe,CAHf,WAAY,CAIZ,eAAgB,CAHhB,aAAc,CAFd,UASF,CAEA,mBAEE,kBAAmB,CAKnB,wBAAyB,CAFzB,UAAW,CAJX,YAAa,CAKb,iBAAkB,CAHlB,sBAAuB,CACvB,gBAIF,CAEA,yBAGE,UAAW,CACX,iBAAkB,CAHlB,YAAa,CACb,iBAGF,CAEA,oBACE,wBAAyB,CACzB,+BAAgC,CAChC,gBAAiB,CACjB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,UACF,CAEA,oBAOE,kBAAmB,CANnB,eAAgB,CAChB,WAAY,CAQZ,iBAAkB,CALlB,UAAW,CAFX,cAAe,CAGf,YAAa,CAFb,cAAe,CAIf,OAAQ,CACR,eAAgB,CAEhB,oCACF,CAEA,0BACE,wBACF,CAEA,mBAGE,UAAW,CADX,cAAe,CADf,6BAGF,CAEA,6BACE,sBACF,CAEA,4BACE,uBACF,CAEA,qBAEE,gBAAiB,CACjB,eAAgB,CAFhB,YAGF,CAGA,WACE,YACF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,YACF,CAEA,kBAQE,kBAAmB,CAPnB,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAHlB,cAAe,CAIf,YAAa,CAGb,WAAY,CALZ,gBAAiB,CAIjB,cAAe,CALf,eAOF,CAEA,wBACE,wBACF,CAEA,YAEE,UAAW,CADX,aAAc,CAEd,6BACF,CAEA,sBACE,sBACF,CAEA,qBACE,uBACF,CAGA,UACE,UAAc,CACd,eACF,CAEA,aACE,WACF,CAEA,aACE,aAAc,CACd,eACF,CAEA,cACE,aAAc,CACd,eACF,CAEA,WACE,aAAc,CACd,iBACF,CAEA,cACE,UAAW,CACX,eACF,CAEA,YACE,UAAW,CACX,YACF,CAEA,YACE,UACF,CAEA,uCAEE,UAAW,CACX,cAAe,CACf,eACF,CAEA,kBACE,aAAc,CACd,eAAgB,CAChB,gBACF,CAEA,qCAEE,UAAW,CACX,iBACF,CAGA,yCAEE,aACF,CAEA,mCAEE,YAAa,CACb,iBACF,CAEA,+CAEE,0BACF,CAEA,cACE,aAAc,CACd,iBACF,CAGA,gCAEE,UAAW,CADX,SAEF,CAEA,sCACE,kBAAmB,CACnB,iBACF,CAEA,sCACE,kBAAmB,CACnB,iBACF,CAEA,4CACE,kBACF,CAEA,wCAEE,UAAW,CADX,SAEF,CAEA,8CACE,kBAAmB,CACnB,iBACF,CAEA,8CACE,kBAAmB,CACnB,iBACF,CAEA,oDACE,kBACF,CAGA,yBACE,aACE,cACF,CAEA,oBACE,cAAe,CACf,eACF,CAEA,qBAEE,gBAAiB,CADjB,WAEF,CAEA,kBAEE,WAAY,CADZ,cAEF,CACF,CAGA,+BACE,aACE,iBACF,CAEA,UACE,UACF,CAEA,aACE,UACF,CAEA,aACE,UACF,CAEA,cACE,YACF,CAEA,WACE,aACF,CACF,CAGA,mCACE,aACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,oBACE,wBAAyB,CACzB,2BACF,CAEA,oBACE,aACF,CAEA,0BACE,wBACF,CAEA,UACE,aACF,CAEA,aACE,aACF,CAEA,aACE,aACF,CAEA,cACE,aACF,CAEA,WACE,aACF,CAEA,sCAGE,aACF,CAEA,uCAEE,aACF,CAEA,kBACE,aACF,CAEA,+CAEE,0BACF,CACF,CC1VA,aACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,kBAAmB,CACnB,8BAA+B,CAC/B,eACF,CAEA,mBAGE,UAAW,CAEX,cAAe,CADf,iBAAkB,CAHlB,iBAAkB,CAClB,iBAIF,CAEA,oBAGE,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAH1B,iBAIF,CAEA,uBAEE,UAAW,CACX,cAAe,CAFf,cAGF,CAEA,sBAEE,UAAW,CACX,qBAAsB,CAFtB,cAAe,CAGf,oBACF,CAGA,iBACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,kBAGE,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAH1B,iBAIF,CAEA,qBAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,mBAIE,sBAAuB,CAFvB,YAAa,CAGb,cAAe,CAFf,QAAS,CAFT,YAKF,CAGA,wBACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,yBAOE,kBAAmB,CAJnB,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAC1B,YAAa,CACb,6BAA8B,CAL9B,iBAOF,CAEA,4BAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,iBAGE,oBAAqB,CAFrB,YAAa,CACb,qBAAsB,CAEtB,cACF,CAEA,uBAEE,kBAAmB,CAInB,UAAW,CAFX,cAAe,CAHf,YAAa,CAEb,OAAQ,CAER,iBAEF,CAEA,sCACE,QACF,CAEA,eACE,UAAW,CAEX,cAAe,CADf,iBAEF,CAEA,0BAEE,YAAa,CAEb,cAAe,CADf,QAAS,CAFT,YAIF,CAGA,uBACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,wBAGE,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAH1B,iBAIF,CAEA,2BAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,qBAIE,wBAAyB,CAHzB,aAAc,CAId,cAAe,CAFf,kBAAmB,CADnB,UAIF,CAEA,iBACE,iBACF,CAEA,kBAEE,wBAAyB,CADzB,kBAAmB,CAGnB,iBAAkB,CADlB,kBAEF,CASA,wDAEE,SAAU,CADV,SAEF,CAEA,iBAGE,aAAc,CAKd,aAAc,CAPd,cAAe,CACf,eAAiB,CAEjB,iBAKF,CAEA,4BALE,wBAAyB,CACzB,iBAAkB,CAFlB,YAWF,CALA,WAIE,cACF,CAEA,WAEE,UAAW,CAGX,cAAe,CAJf,eAAgB,CAEhB,iBAAkB,CAClB,oBAEF,CAEA,WAEE,UAAW,CACX,qBAAsB,CAFtB,cAGF,CAEA,yBAEE,wBAAyB,CAEzB,iBAAkB,CAClB,aAAc,CAEd,aAAc,CADd,eAAgB,CAHhB,YAAa,CAFb,iBAOF,CAEA,eAUE,kCAA2B,CAA3B,0BAA2B,CAN3B,wBAAyB,CACzB,iBAAkB,CAFlB,cAAe,CAIf,aAAc,CALd,WAAY,CADZ,cAAe,CAKf,6BAA+B,CAG/B,qBAEF,CAEA,qBAEE,8BAAyC,CADzC,qBAEF,CAEA,mBACE,wBAAyB,CAEzB,iBAAkB,CAClB,cAAe,CAFf,YAGF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,8BAEE,UAAW,CADX,cAAe,CAEf,eAAgB,CAChB,iBACF,CAEA,mBACE,YAAa,CACb,cAAe,CACf,OACF,CAEA,kBAEE,kBAAmB,CAKnB,gBAAiB,CADjB,kBAAmB,CALnB,mBAAoB,CAGpB,cAAe,CACf,eAAgB,CAFhB,eAAgB,CAKhB,kBACF,CAEA,0BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAEA,wBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAGA,sBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,yBAEE,UAAW,CACX,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,wBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,0CAGE,kBAAmB,CAGnB,gBAAiB,CADjB,iBAAkB,CAJlB,YAAa,CAMb,cAAe,CACf,eAAgB,CANhB,6BAA8B,CAE9B,gBAKF,CAEA,aACE,QACF,CAEA,aAEE,cAAe,CADf,eAEF,CAEA,aACE,wBAAyB,CAEzB,+BAAgC,CAGhC,UAAW,CADX,cAAe,CADf,eAAgB,CAFhB,gBAKF,CAEA,cAIE,cAAe,CAFf,gBAAiB,CACjB,eAAgB,CAGhB,2BAA4B,CAL5B,WAAY,CAOZ,+BAAgC,CADhC,oBAEF,CAGA,iCAEE,UAAW,CADX,SAEF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,6CACE,kBACF,CAGA,0BACE,WAAY,CACZ,gBACF,CAEA,wBACE,WAAY,CACZ,gBACF,CAEA,cAGE,oBAAqB,CAGrB,UAAW,CACX,iCAAqC,CAHrC,cAAe,CACf,eAAgB,CAJhB,QAAS,CACT,oBAMF,CAEA,kBACE,cAAe,CACf,eACF,CAEA,wBAEE,wBAAyB,CACzB,QAAS,CAFT,UAGF,CAEA,0CAEE,wBAAyB,CACzB,WAAY,CACZ,eAAgB,CAChB,kBACF,CAEA,qBACE,wBAAyB,CACzB,eACF,CAEA,eAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAgB,CAHhB,YAAa,CADb,iBAKF,CAEA,gBAEE,UAAW,CADX,cAAe,CAEf,iBACF,CAEA,gBAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAGA,4BACE,YAAa,CACb,QAAS,CACT,kBACF,CAGA,wBACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,QAAO,CACP,WACF,CAGA,uBACE,QAAO,CACP,WACF,CAEA,yBAGE,wBAAyB,CADzB,+BAAgC,CAEhC,yBAA0B,CAH1B,iBAIF,CAEA,4BAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,0BAEE,YAAa,CAEb,cAAe,CADf,QAAS,CAFT,YAIF,CAEA,cAGE,wBAAyB,CAGzB,wBAAyB,CAFzB,iBAAkB,CAClB,eAAgB,CAHhB,YAAa,CADb,iBAMF,CAEA,eAEE,UAAW,CADX,cAAe,CAGf,eAAgB,CADhB,iBAEF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,uBACE,aACF,CAEA,uBACE,aACF,CAGA,qCACE,yBACF,CAEA,qCACE,4BACF,CAEA,+CACE,0BACF,CAEA,8CACE,2BACF,CAGA,oJAIE,0BAA2B,CAC3B,2BACF,CAGA,0BACE,qBACE,aACF,CAEA,iBAGE,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CACd,kBAAmB,CAGnB,eACF,CAEA,kBAIE,WAAgC,CAAhC,+BAAgC,CAHhC,aAAc,CACd,UAGF,CAEA,6BACE,kBACF,CAEA,uBACE,UACF,CAEA,mBACE,kBAAmB,CACnB,cAAe,CACf,QACF,CAEA,iBACE,aACF,CAEA,WACE,cACF,CAEA,yBACE,aACF,CAEA,mBACE,cACF,CAEA,cACE,YACF,CACF,CCtkBA,mBAIE,eAAgB,CAHhB,YAAa,CACb,qBAAsB,CACtB,WAEF,CAEA,0BAGE,kBAAmB,CAEnB,kBAAmB,CACnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,6BAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,2BACE,YAAa,CACb,OACF,CAEA,2BACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAGA,cACE,kBACF,CAEA,iBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CALrB,eAAkB,CAIlB,wBAEF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,UAGE,eAAgB,CAFhB,YAAa,CACb,QAEF,CAEA,YACE,QACF,CAEA,kBAKE,aAAc,CAJd,aAAc,CAEd,cAAe,CACf,eAAgB,CAFhB,iBAIF,CAEA,qCAIE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,gBAAiB,CAIjB,2BAA6B,CAL7B,UAMF,CAEA,iDAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,qBACE,YAAa,CACb,OACF,CAEA,iCACE,kBAAmB,CACnB,UACF,CAGA,gBAIE,wBAAyB,CACzB,iBAAkB,CAClB,8CAAwD,CACxD,cAAe,CACf,eAAgB,CANhB,gBAAiB,CACjB,YAAa,CAMb,eAAgB,CAChB,2BAA6B,CAT7B,UAUF,CAEA,sBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,iBAKE,kBAAmB,CAFnB,wBAAyB,CACzB,iBAAkB,CAHlB,gBAAiB,CAKjB,aAAc,CAJd,YAKF,CAEA,eAEE,wBAAyB,CACzB,QAAS,CAFT,UAGF,CAEA,oCAGE,wBAAyB,CADzB,gBAAiB,CAEjB,eACF,CAEA,kBAEE,eACF,CAEA,6CAJE,kBAMF,CAEA,qBAME,oBAAqB,CAJrB,8CAAwD,CACxD,cAAe,CACf,eAAgB,CAHhB,QAAS,CAIT,oBAEF,CChKA,iBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAEA,wBAEE,kBAAmB,CACnB,+BAAgC,CAFhC,YAGF,CAEA,2BAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,yBACE,QAAO,CACP,eAAgB,CAChB,WACF,CAEA,gDAIE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAKtB,iBAAkB,CAFlB,YAAa,CADb,sBAIF,CAEA,yBACE,YACF,CAGA,iBAGE,eAAgB,CAChB,wBAAyB,CACzB,iBAAkB,CAJlB,kBAAmB,CACnB,YAAa,CAIb,yBACF,CAEA,uBACE,8BACF,CAEA,wBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,eAIE,kBAAmB,CAEnB,iBAAkB,CAHlB,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,eAEF,CAEA,sBAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,mBAIE,kBAAmB,CAHnB,cAAe,CACf,eAAgB,CAIhB,mBAAqB,CAHrB,eAAgB,CAEhB,wBAEF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,iBACE,kBAAmB,CACnB,aACF,CAEA,yBACE,YAAa,CACb,OACF,CAEA,yBACE,kBACF,CAEA,sBACE,iBACF,CAEA,gBAGE,kBAAmB,CAEnB,iBAAkB,CAHlB,aAAc,CAId,oBAAqB,CALrB,cAAe,CAGf,eAGF,CAEA,4BACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,gBAAiB,CACjB,eAAgB,CAFhB,WAGF,CAEA,gCAOE,oBAAqB,CAFrB,aAAc,CAHd,8CAAwD,CACxD,cAAe,CACf,eAAgB,CAHhB,QAAS,CAKT,oBAEF,CAEA,wBAKE,4BAA6B,CAD7B,aAAc,CAHd,YAAa,CAEb,cAAe,CADf,QAAS,CAIT,eACF,CAEA,iBAEE,kBAAmB,CADnB,YAEF,CCjKA,oBAEE,kBAAmB,CACnB,iBAAkB,CAClB,aAAc,CAHd,YAIF,CAEA,mBAGE,kBAAmB,CAGnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,sBAEE,aAAc,CACd,gBAAiB,CAFjB,QAGF,CAEA,oBACE,YAAa,CACb,QACF,CAEA,KAOE,uBACF,CAOA,aACE,wBAEF,CAEA,kCACE,wBACF,CAEA,eACE,wBAEF,CAEA,oCACE,wBACF,CAEA,YACE,wBAEF,CAEA,iCACE,wBACF,CAEA,QAEE,cAAe,CADf,eAEF,CAEA,eACE,wBAAyB,CAKzB,wBAAyB,CAFzB,iBAAkB,CAFlB,aAAc,CAGd,kBAAmB,CAFnB,YAIF,CAEA,kBAKE,eAAiB,CAEjB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CACb,QAAS,CACT,kBAAmB,CACnB,YAIF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,cACF,CAEA,YAEE,aAEF,CAEA,YAGE,aAAc,CADd,eAEF,CAEA,qCAME,eAAiB,CAEjB,wBAAyB,CADzB,iBAAkB,CAFlB,aAAc,CADd,YAAa,CADb,iBAMF,CAEA,kBACE,aACF,CAEA,kBACE,eAAiB,CAEjB,wBAAyB,CADzB,iBAAkB,CAElB,eACF,CAEA,yBAIE,aAAS,CAET,kBAAmB,CAJnB,YAAa,CAEb,QAAS,CADT,+CAAgD,CAEhD,iBAEF,CAEA,cACE,wBAAyB,CAGzB,+BAAgC,CADhC,aAAc,CADd,eAGF,CAEA,WACE,+BAAgC,CAChC,oCACF,CAEA,iBACE,wBACF,CAEA,sBACE,kBACF,CAEA,WACE,qBAAsB,CACtB,cAAe,CACf,oBACF,CAEA,0BAIE,kBAAmB,CAFnB,oBAAqB,CAGrB,cAAe,CACf,eAAiB,CAHjB,eAAgB,CAIhB,wBACF,CAEA,2BACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAEA,aAEE,aAAc,CADd,cAEF,CAEA,aACE,YAAa,CACb,sBACF,CAGA,yBACE,mBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAMA,sCAHE,sBAMF,CAHA,kBACE,cAEF,CAEA,yBAGE,OAAQ,CADR,yBAEF,CAEA,cACE,YACF,CAEA,WAEE,wBAAyB,CAEzB,iBAAkB,CADlB,kBAAmB,CAFnB,YAIF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,aACF,CAEA,sBAGE,aAAc,CAFd,wBAAyB,CACzB,eAEF,CAEA,kBAAqB,cAAiB,CACtC,sBAAyB,eAAkB,CAC3C,iBAAoB,cAAiB,CACrC,mBAAsB,cAAiB,CACvC,oBAAuB,gBAAmB,CAC1C,oBAAuB,cAAiB,CAC1C,CCrQA,kBAIE,eAAgB,CAChB,wBAAyB,CACzB,iBAAkB,CALlB,YAAa,CACb,qBAAsB,CACtB,WAAY,CAIZ,eACF,CAEA,yBAEE,kBAAmB,CACnB,+BAAgC,CAFhC,YAGF,CAEA,4BAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,2BACE,YAAa,CACb,OAAQ,CACR,cACF,CAEA,uCAGE,eAAgB,CADhB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAEd,cAAe,CACf,cAAe,CACf,eAAgB,CAPhB,gBAAiB,CAQjB,uBACF,CAEA,6CACE,kBAAmB,CACnB,oBACF,CAEA,8CACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,oDACE,kBAAmB,CACnB,oBACF,CAEA,uBAIE,aAAc,CAHd,YAAa,CAEb,cAAe,CADf,QAGF,CAEA,wBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAKb,iBAAkB,CAFlB,YAAa,CADb,sBAIF,CAEA,kBASE,kBAAmB,CANnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CACd,YAAa,CACb,6BAA8B,CAP9B,WAAY,CACZ,YAQF,CAEA,yBACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CACf,eACF,CAEA,0BAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAEA,0BAIE,+BAAgC,CAFhC,YAAa,CACb,OAAQ,CAFR,YAIF,CAGA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CACf,cAAe,CACf,eAAgB,CALhB,gBAAiB,CAMjB,kBACF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,kCACE,kBACF,CAEA,eACE,kBAAmB,CACnB,UACF,CAEA,oCACE,kBACF,CAEA,WAEE,cAAe,CADf,eAEF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,iCACE,kBACF,CC1JA,KAIE,wBAAyB,CAFzB,YAAa,CACb,qBAAsB,CAFtB,gBAIF,CAEA,YACE,kDAA6D,CAG7D,8BAAwC,CAFxC,UAAY,CACZ,cAEF,CAEA,WAEE,cAAe,CACf,eAAgB,CAFhB,cAGF,CAEA,cAEE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,UAEF,CAEA,UACE,QAAO,CACP,cACF,CAEA,aACE,kBACF,CAGA,gBAGE,kBAAmB,CAEnB,iBAAkB,CAJlB,YAAa,CACb,OAAQ,CAER,WAAY,CAEZ,yBAAkB,CAAlB,iBACF,CAEA,YAGE,gBAAuB,CADvB,WAAY,CAKZ,iBAAkB,CAHlB,aAAc,CAId,cAAe,CAHf,cAAe,CACf,eAAgB,CALhB,iBAAkB,CAQlB,kBACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kBAAmB,CAEnB,8BAA4C,CAD5C,UAEF,CAEA,aACE,YAAa,CACb,QAAS,CACT,gBACF,CAEA,aAEE,YAAa,CACb,qBAAsB,CACtB,QAAS,CAHT,SAIF,CAEA,YAEE,QAAO,CADP,SAEF,CAEA,YACE,wBAAyB,CACzB,UAAY,CAGZ,eAAgB,CAFhB,cAAe,CACf,iBAEF,CAEA,cAEE,cAAe,CADf,QAAS,CAET,UACF,CAGA,0BACE,aACE,qBACF,CAEA,yBAEE,UACF,CACF,CAEA,yBACE,aACE,QACF,CAEA,aACE,YACF,CACF,CAEA,yBACE,YACE,cACF,CAEA,WACE,cACF,CAEA,cACE,cACF,CAEA,UACE,cACF,CAEA,WACE,cACF,CACF,CAEA,yBACE,WACE,cACF,CAEA,cACE,cACF,CAEA,UACE,cACF,CAEA,WACE,cACF,CAEA,aACE,QACF,CACF,CAGA,aAGE,kBAAmB,CAEnB,UAAW,CAJX,YAAa,CACb,sBAAuB,CAEvB,gBAEF,CAEA,qBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,iBAAkB,CANlB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,WACE,wBAAyB,CACzB,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAGd,aAAc,CAFd,YAGF,CAEA,iBACE,eAAgB,CAChB,iBACF,CAEA,mBACE,QACF,CAGA,WAGE,UAAW,CADX,iBAAkB,CADlB,iBAGF,CAEA,gBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,iBACE,cAAe,CACf,eAAgB,CAChB,iBACF,CAEA,mBACE,cAAe,CACf,UACF,CAGA,iBACE,WAAY,CACZ,UACF,CAEA,kBAGE,kBAAmB,CAEnB,UAAW,CAJX,YAAa,CAKb,cAAe,CAJf,sBAAuB,CAEvB,YAGF,CAEA,0BAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,gBAAiB,CANjB,UAOF,CAGA,sBACE,sBACF,CAEA,yCACE,SACF,CAEA,+CACE,kBAAmB,CACnB,iBACF,CAEA,+CACE,kBAAmB,CACnB,iBACF,CAEA,qDACE,kBACF,CAGA,aACE,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CACxC,YACF,CAEA,oBAEE,+BAAgC,CAChC,kBAAmB,CAFnB,mBAGF,CAEA,uBAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAEF,CAEA,WACE,kBAAmB,CACnB,iBAAkB,CAClB,YAAa,CACb,iBACF,CAEA,YAEE,UAAW,CADX,cAAe,CAEf,iBACF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,oBACE,aACF,CAEA,oBACE,aACF,CAEA,mBACE,aACF", "sources": ["styles/index.css", "components/DatasetSelector.css", "components/CaseList.css", "components/ImageModal.css", "components/TableRenderer.css", "components/JsonViewer.css", "components/CaseDetail.css", "components/annotation/AnnotationEditor.css", "components/annotation/AnnotationList.css", "components/AnnotationManager.css", "components/annotation/AnnotationPanel.css", "App.css"], "sourcesContent": ["/* Global styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8f9fa;\n  color: #333;\n  line-height: 1.6;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Utility classes */\n.container {\n  max-width: 100%;\n  margin: 0 auto;\n  padding: 0 20px;\n  overflow-x: auto;\n}\n\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40px;\n  font-size: 16px;\n  color: #666;\n}\n\n.error {\n  color: #dc3545;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  padding: 12px;\n  margin: 10px 0;\n}\n\n.success {\n  color: #155724;\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n  border-radius: 4px;\n  padding: 12px;\n  margin: 10px 0;\n}\n\n/* Button styles */\n.btn {\n  display: inline-block;\n  padding: 8px 16px;\n  margin: 4px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  text-decoration: none;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover {\n  background-color: #545b62;\n}\n\n/* Form styles */\n.form-group {\n  margin-bottom: 16px;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Card styles */\n.card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.card-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e9ecef;\n  font-weight: 600;\n}\n\n.card-body {\n  padding: 20px;\n}\n\n/* Table styles */\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 20px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n}\n\n.table-striped tbody tr:nth-child(odd) {\n  background-color: #f8f9fa;\n}\n\n/* Modal styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  max-width: 90vw;\n  max-height: 90vh;\n  overflow: auto;\n  position: relative;\n}\n\n.modal-close {\n  position: absolute;\n  top: 10px;\n  right: 15px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n  z-index: 1001;\n}\n\n.modal-close:hover {\n  color: #333;\n}\n", ".dataset-selector {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.dataset-selector-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.dataset-selector-label {\n  font-weight: 600;\n  color: #333;\n  min-width: 60px;\n}\n\n.dataset-selector-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex: 1;\n}\n\n.dataset-selector-select {\n  flex: 1;\n  max-width: 300px;\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 14px;\n  background-color: white;\n  cursor: pointer;\n}\n\n.dataset-selector-select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.dataset-selector-select:disabled {\n  background-color: #f8f9fa;\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n.dataset-selector-refresh-btn {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  background-color: white;\n  cursor: pointer;\n  font-size: 16px;\n  color: #666;\n  transition: all 0.2s ease;\n  min-width: 40px;\n}\n\n.dataset-selector-refresh-btn:hover:not(:disabled) {\n  background-color: #f8f9fa;\n  border-color: #007bff;\n  color: #007bff;\n}\n\n.dataset-selector-refresh-btn:disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n.dataset-selector-loading {\n  color: #666;\n  font-style: italic;\n}\n\n.dataset-selector-error {\n  color: #dc3545;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.dataset-selector-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 8px;\n  border-top: 1px solid #e9ecef;\n  font-size: 14px;\n}\n\n.dataset-selector-current {\n  color: #333;\n}\n\n.dataset-selector-current strong {\n  color: #007bff;\n}\n\n.dataset-selector-count {\n  color: #666;\n  font-size: 12px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .dataset-selector-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 8px;\n  }\n  \n  .dataset-selector-controls {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .dataset-selector-select {\n    max-width: none;\n  }\n  \n  .dataset-selector-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n}\n", ".case-list {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n  height: calc(100vh - 200px);\n  display: flex;\n  flex-direction: column;\n}\n\n.case-list-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n}\n\n.case-list-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 18px;\n}\n\n.case-list-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.case-list-count {\n  font-size: 14px;\n  color: #666;\n  white-space: nowrap;\n}\n\n.case-list-buttons {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.case-list-auto-refresh-btn {\n  padding: 6px 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  background-color: white;\n  cursor: pointer;\n  font-size: 14px;\n  color: #666;\n  transition: all 0.2s ease;\n  min-width: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.case-list-auto-refresh-btn:hover {\n  background-color: #f0f8ff;\n  border-color: #007bff;\n  color: #007bff;\n}\n\n.case-list-auto-refresh-btn.active {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n.case-list-refresh-btn {\n  padding: 6px 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  background-color: white;\n  cursor: pointer;\n  font-size: 14px;\n  color: #666;\n  transition: all 0.2s ease;\n  min-width: 36px;\n}\n\n.case-list-refresh-btn:hover:not(:disabled) {\n  background-color: #f8f9fa;\n  border-color: #007bff;\n  color: #007bff;\n}\n\n.case-list-refresh-btn:disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n.case-list-loading {\n  padding: 40px 20px;\n  text-align: center;\n  color: #666;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  margin-bottom: 16px;\n  font-size: 14px;\n}\n\n.loading-progress {\n  width: 200px;\n  height: 4px;\n  background-color: #e9ecef;\n  border-radius: 2px;\n  margin: 0 auto;\n  overflow: hidden;\n}\n\n.loading-progress-bar {\n  height: 100%;\n  background-color: #007bff;\n  transition: width 0.3s ease;\n}\n\n.case-list-error {\n  padding: 20px;\n  text-align: center;\n}\n\n.error-message {\n  color: #dc3545;\n  margin-bottom: 12px;\n}\n\n.error-retry-btn {\n  padding: 8px 16px;\n  border: 1px solid #dc3545;\n  border-radius: 4px;\n  background-color: white;\n  color: #dc3545;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.error-retry-btn:hover {\n  background-color: #dc3545;\n  color: white;\n}\n\n.case-list-empty {\n  padding: 40px 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n}\n\n.case-list-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.case-list-stats {\n  padding: 16px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n}\n\n.case-list-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n/* 调整统计面板在案例列表中的样式 */\n.case-list .stats-panel {\n  box-shadow: none;\n  background: transparent;\n  padding: 0;\n  margin: 0;\n}\n\n.case-list .stats-panel-header {\n  padding: 0 0 12px 0;\n  margin-bottom: 12px;\n}\n\n.case-list .stats-grid {\n  grid-template-columns: repeat(3, 1fr);\n  gap: 8px;\n}\n\n.case-list .stat-item {\n  padding: 8px;\n}\n\n.case-list .stat-label {\n  font-size: 11px;\n}\n\n.case-list .stat-value {\n  font-size: 14px;\n}\n\n/* Case item styles */\n.case-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  margin-bottom: 4px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background-color: white;\n  font-size: 14px;\n}\n\n.case-item:hover {\n  background-color: #f8f9fa;\n  border-color: #007bff;\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);\n}\n\n.case-item.selected {\n  background-color: #e3f2fd;\n  border-color: #007bff;\n}\n\n.case-item-index {\n  font-weight: 600;\n  color: #007bff;\n  min-width: 40px;\n  text-align: center;\n}\n\n.case-item-info {\n  flex: 1;\n  margin-left: 12px;\n}\n\n.case-item-filename {\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  word-break: break-all;\n}\n\n.case-item-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 12px;\n  color: #666;\n  flex-wrap: wrap;\n}\n\n.case-item-status {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2px;\n  min-width: 60px;\n}\n\n.case-item-status span {\n  font-size: 10px;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-indicator.has-image {\n  background-color: #28a745;\n}\n\n.status-indicator.no-image {\n  background-color: #dc3545;\n}\n\n.status-indicator.has-vl-llm {\n  background-color: #17a2b8;\n}\n\n.status-indicator.no-vl-llm {\n  background-color: #ffc107;\n}\n\n/* 解析状态指示器 */\n.parse-status {\n  margin-bottom: 12px;\n  padding: 12px;\n  border-radius: 6px;\n  font-size: 14px;\n  border: 1px solid transparent;\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.parse-status.in_progress {\n  background-color: #e7f3ff;\n  border-color: #b3d9ff;\n  color: #0066cc;\n}\n\n.parse-status.completed {\n  background-color: #e8f5e8;\n  border-color: #c3e6c3;\n  color: #2d5a2d;\n}\n\n.parse-status.unknown {\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n  color: #856404;\n}\n\n.parse-status-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.status-icon {\n  font-size: 16px;\n  line-height: 1;\n}\n\n.status-text {\n  font-weight: 500;\n  flex: 1;\n  min-width: 200px;\n}\n\n.auto-refresh-indicator {\n  font-size: 12px;\n  color: #666;\n  opacity: 0.8;\n  font-style: italic;\n  animation: fadeInOut 3s infinite;\n}\n\n@keyframes fadeInOut {\n  0%, 100% {\n    opacity: 0.8;\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .case-list-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 8px;\n  }\n  \n  .case-list-controls {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 8px;\n  }\n  \n  .case-list-buttons {\n    justify-content: center;\n  }\n  \n  .case-item {\n    flex-direction: column;\n    align-items: stretch;\n    text-align: left;\n  }\n  \n  .case-item-index {\n    text-align: left;\n    margin-bottom: 8px;\n  }\n  \n  .case-item-info {\n    margin-left: 0;\n  }\n  \n  .case-item-meta {\n    flex-wrap: wrap;\n  }\n  \n  .parse-status-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n  \n  .status-text {\n    min-width: auto;\n  }\n  \n  .auto-refresh-indicator {\n    align-self: stretch;\n    text-align: center;\n  }\n}\n\n/* 过滤器样式 */\n.case-list-filter {\n  margin: 12px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 6px;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.filter-group label {\n  font-size: 12px;\n  font-weight: 500;\n  color: #495057;\n}\n\n.filter-input-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-input {\n  flex: 1;\n  padding: 6px 8px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 12px;\n  background: white;\n}\n\n.filter-input:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.filter-select {\n  flex: 1;\n  padding: 6px 8px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 12px;\n  background: white;\n  cursor: pointer;\n}\n\n.filter-select:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.filter-clear-btn {\n  padding: 6px 8px;\n  border: 1px solid #dc3545;\n  background: #dc3545;\n  color: white;\n  border-radius: 4px;\n  font-size: 10px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.filter-clear-btn:hover:not(:disabled) {\n  background: #c82333;\n  border-color: #c82333;\n}\n\n.filter-clear-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.filter-help {\n  font-size: 11px;\n  color: #6c757d;\n  font-style: italic;\n}\n", ".image-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.9);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.image-modal-content {\n  position: relative;\n  max-width: 95vw;\n  max-height: 95vh;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.image-modal-close {\n  position: absolute;\n  top: 10px;\n  right: 15px;\n  background: rgba(0, 0, 0, 0.7);\n  border: none;\n  color: white;\n  font-size: 28px;\n  font-weight: bold;\n  cursor: pointer;\n  z-index: 1001;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n}\n\n.image-modal-close:hover {\n  background: rgba(0, 0, 0, 0.9);\n}\n\n.image-modal-title {\n  padding: 16px 20px;\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n  font-weight: 600;\n  color: #333;\n  font-size: 16px;\n  word-break: break-all;\n}\n\n.image-modal-image-container {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  min-height: 200px;\n  background-color: #f8f9fa;\n}\n\n.image-modal-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.image-modal-error {\n  color: #dc3545;\n  font-size: 16px;\n  text-align: center;\n  padding: 40px;\n}\n\n.image-modal-controls {\n  padding: 16px 20px;\n  background-color: #f8f9fa;\n  border-top: 1px solid #dee2e6;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 12px;\n}\n\n.zoom-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.zoom-btn {\n  width: 32px !important;\n  height: 32px !important;\n  padding: 0 !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.zoom-level {\n  min-width: 50px;\n  text-align: center;\n  font-size: 14px;\n  color: #666;\n}\n\n.action-controls {\n  display: flex;\n  gap: 12px;\n}\n\n.image-modal-btn {\n  padding: 8px 16px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  background-color: white;\n  color: #333;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s ease;\n}\n\n.image-modal-btn:hover {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.image-modal-btn:first-child {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.image-modal-btn:first-child:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .image-modal-overlay {\n    padding: 10px;\n  }\n  \n  .image-modal-content {\n    max-width: 100vw;\n    max-height: 100vh;\n  }\n  \n  .image-modal-title {\n    padding: 12px 16px;\n    font-size: 14px;\n  }\n  \n  .image-modal-image-container {\n    padding: 15px;\n  }\n  \n  .image-modal-controls {\n    padding: 12px 16px;\n    flex-direction: column;\n  }\n  \n  .image-modal-btn {\n    width: 100%;\n    margin-bottom: 8px;\n  }\n  \n  .image-modal-btn:last-child {\n    margin-bottom: 0;\n  }\n}\n\n/* 动画效果 */\n.image-modal-overlay {\n  animation: fadeIn 0.2s ease-out;\n}\n\n.image-modal-content {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n", ".table-renderer {\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n}\n\n.table-renderer-content {\n  width: 100%;\n  height: 100%;\n}\n\n.table-renderer-empty {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100px;\n  color: #666;\n  font-style: italic;\n}\n\n.empty-message {\n  padding: 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border: 1px dashed #dee2e6;\n}\n\n/* 表格样式 */\n.table-renderer-content table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 0;\n  font-size: 14px;\n}\n\n.table-renderer-content th,\n.table-renderer-content td {\n  border: 1px solid #dee2e6;\n  padding: 8px 12px;\n  text-align: left;\n  vertical-align: top;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n\n.table-renderer-content th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.table-renderer-content tbody tr:nth-child(even) {\n  background-color: #f9f9f9;\n}\n\n.table-renderer-content tbody tr:hover {\n  background-color: #e3f2fd;\n}\n\n/* JSON表格特殊样式 */\n.json-table {\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n\n.json-table th {\n  background-color: #e9ecef;\n  font-weight: bold;\n}\n\n.json-table td {\n  max-width: 300px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.json-value {\n  margin: 0;\n  padding: 4px;\n  background-color: #f8f9fa;\n  border-radius: 2px;\n  font-size: 11px;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 100px;\n  overflow-y: auto;\n}\n\n/* 纯文本样式 */\n.plain-text {\n  margin: 0;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  color: #333;\n  border: 1px solid #dee2e6;\n  height: auto;\n  min-height: 100px;\n  max-height: 1500px;\n  overflow-y: auto;\n}\n\n/* JSON内容样式 */\n.json-content {\n  margin: 0;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  color: #333;\n  border: 1px solid #dee2e6;\n  height: auto;\n  min-height: 100px;\n  max-height: 1500px;\n  overflow-y: auto;\n}\n\n/* 错误样式 */\n.error {\n  color: #dc3545;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  padding: 12px;\n  margin: 8px 0;\n  font-size: 14px;\n}\n\n/* 滚动条样式 */\n.table-renderer::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n.table-renderer::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n.table-renderer::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n.table-renderer::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .table-renderer-content table {\n    font-size: 12px;\n  }\n  \n  .table-renderer-content th,\n  .table-renderer-content td {\n    padding: 6px 8px;\n  }\n  \n  .json-table {\n    font-size: 10px;\n  }\n  \n  .json-value {\n    font-size: 10px;\n    max-height: 60px;\n  }\n  \n  .plain-text,\n  .json-content {\n    font-size: 11px;\n    padding: 8px;\n  }\n}\n", ".json-viewer {\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n  font-family: 'Courier New', Monaco, monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.json-viewer-empty {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100px;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.json-viewer-placeholder {\n  padding: 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n}\n\n.json-viewer-header {\n  background-color: #e9ecef;\n  border-bottom: 1px solid #dee2e6;\n  padding: 8px 12px;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.json-viewer-toggle {\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 12px;\n  color: #333;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 4px 8px;\n  border-radius: 3px;\n  transition: background-color 0.2s ease;\n}\n\n.json-viewer-toggle:hover {\n  background-color: #dee2e6;\n}\n\n.json-viewer-arrow {\n  transition: transform 0.2s ease;\n  font-size: 10px;\n  color: #666;\n}\n\n.json-viewer-arrow.collapsed {\n  transform: rotate(0deg);\n}\n\n.json-viewer-arrow.expanded {\n  transform: rotate(90deg);\n}\n\n.json-viewer-content {\n  padding: 12px;\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n/* JSON节点样式 */\n.json-node {\n  margin: 2px 0;\n}\n\n.json-node-header {\n  display: flex;\n  align-items: center;\n  margin: 2px 0;\n}\n\n.json-node-toggle {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 2px 4px;\n  margin-right: 4px;\n  border-radius: 2px;\n  display: flex;\n  align-items: center;\n  min-width: 16px;\n  height: 16px;\n}\n\n.json-node-toggle:hover {\n  background-color: #e9ecef;\n}\n\n.json-arrow {\n  font-size: 8px;\n  color: #666;\n  transition: transform 0.2s ease;\n}\n\n.json-arrow.collapsed {\n  transform: rotate(0deg);\n}\n\n.json-arrow.expanded {\n  transform: rotate(90deg);\n}\n\n/* 数据类型样式 */\n.json-key {\n  color: #0066cc;\n  font-weight: 500;\n}\n\n.json-string {\n  color: #008000;\n}\n\n.json-number {\n  color: #1976d2;\n  font-weight: 500;\n}\n\n.json-boolean {\n  color: #9c27b0;\n  font-weight: 500;\n}\n\n.json-null {\n  color: #757575;\n  font-style: italic;\n}\n\n.json-bracket {\n  color: #333;\n  font-weight: bold;\n}\n\n.json-colon {\n  color: #333;\n  margin: 0 2px;\n}\n\n.json-comma {\n  color: #333;\n}\n\n.json-array-length,\n.json-object-length {\n  color: #666;\n  font-size: 10px;\n  margin-left: 4px;\n}\n\n.json-array-index {\n  color: #ff5722;\n  font-weight: 500;\n  margin-right: 4px;\n}\n\n.json-array-empty,\n.json-object-empty {\n  color: #666;\n  font-style: italic;\n}\n\n/* 内容区域样式 */\n.json-array-content,\n.json-object-content {\n  margin-left: 0;\n}\n\n.json-array-item,\n.json-object-item {\n  margin: 1px 0;\n  position: relative;\n}\n\n.json-object-item:hover,\n.json-array-item:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n.json-unknown {\n  color: #e91e63;\n  font-style: italic;\n}\n\n/* 滚动条样式 */\n.json-viewer::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n.json-viewer::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.json-viewer::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.json-viewer::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.json-viewer-content::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n.json-viewer-content::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.json-viewer-content::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.json-viewer-content::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .json-viewer {\n    font-size: 11px;\n  }\n  \n  .json-viewer-toggle {\n    font-size: 11px;\n    padding: 3px 6px;\n  }\n  \n  .json-viewer-content {\n    padding: 8px;\n    max-height: 400px;\n  }\n  \n  .json-node-toggle {\n    min-width: 14px;\n    height: 14px;\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .json-viewer {\n    border-color: #000;\n  }\n  \n  .json-key {\n    color: #0000ff;\n  }\n  \n  .json-string {\n    color: #006600;\n  }\n  \n  .json-number {\n    color: #000080;\n  }\n  \n  .json-boolean {\n    color: #800080;\n  }\n  \n  .json-null {\n    color: #404040;\n  }\n}\n\n/* 暗色主题支持 */\n@media (prefers-color-scheme: dark) {\n  .json-viewer {\n    background-color: #2d3748;\n    border-color: #4a5568;\n    color: #e2e8f0;\n  }\n  \n  .json-viewer-header {\n    background-color: #4a5568;\n    border-bottom-color: #718096;\n  }\n  \n  .json-viewer-toggle {\n    color: #e2e8f0;\n  }\n  \n  .json-viewer-toggle:hover {\n    background-color: #718096;\n  }\n  \n  .json-key {\n    color: #63b3ed;\n  }\n  \n  .json-string {\n    color: #68d391;\n  }\n  \n  .json-number {\n    color: #90cdf4;\n  }\n  \n  .json-boolean {\n    color: #d6bcfa;\n  }\n  \n  .json-null {\n    color: #a0aec0;\n  }\n  \n  .json-bracket,\n  .json-colon,\n  .json-comma {\n    color: #e2e8f0;\n  }\n  \n  .json-array-length,\n  .json-object-length {\n    color: #a0aec0;\n  }\n  \n  .json-array-index {\n    color: #fc8181;\n  }\n  \n  .json-object-item:hover,\n  .json-array-item:hover {\n    background-color: rgba(99, 179, 237, 0.1);\n  }\n} ", ".case-detail {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n  max-height: calc(100vh - 200px); /* 限制最大高度 */\n  overflow-y: auto; /* 允许整体滚动 */\n}\n\n.case-detail-empty {\n  padding: 60px 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  font-size: 16px;\n}\n\n.case-detail-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n}\n\n.case-detail-header h3 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 18px;\n}\n\n.case-detail-filename {\n  font-size: 14px;\n  color: #666;\n  font-family: monospace;\n  word-break: break-all;\n}\n\n/* 案例信息块 */\n.case-info-block {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.case-info-header {\n  padding: 12px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n}\n\n.case-info-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n}\n\n.case-info-content {\n  padding: 20px;\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n  flex-wrap: wrap;\n}\n\n/* 准确率汇总块 */\n.accuracy-summary-block {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.accuracy-summary-header {\n  padding: 12px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.accuracy-summary-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n}\n\n.baseline-toggle {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  font-size: 12px;\n}\n\n.baseline-toggle label {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  cursor: pointer;\n  margin-bottom: 3px;\n  color: #555;\n}\n\n.baseline-toggle input[type=\"checkbox\"] {\n  margin: 0;\n}\n\n.baseline-info {\n  color: #666;\n  font-style: italic;\n  font-size: 11px;\n}\n\n.accuracy-summary-content {\n  padding: 20px;\n  display: flex;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n/* 解析详情块 */\n.parsing-details-block {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.parsing-details-header {\n  padding: 12px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n}\n\n.parsing-details-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n}\n\n.case-detail-content {\n  display: table;\n  width: 100%;\n  table-layout: fixed;\n  border-collapse: collapse;\n  padding: 0 20px; /* 添加左右内边距 */\n}\n\n.case-detail-row {\n  display: table-row;\n}\n\n.case-detail-cell {\n  display: table-cell;\n  border: 1px solid #dee2e6;\n  vertical-align: top;\n  position: relative;\n}\n\n/* 原始文本列 */\n.case-detail-original-text {\n  width: 50%; /* 占一半宽度 */\n  padding: 0;\n}\n\n/* 渲染结果列 */\n.case-detail-rendered-result {\n  width: 50%; /* 占一半宽度 */\n  padding: 0;\n}\n\n.sequence-number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #007bff;\n  text-align: center;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  flex: 0 0 auto;\n}\n\n.file-info {\n  background-color: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  flex: 1 1 200px;\n}\n\n.file-name {\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n  word-break: break-all;\n  font-size: 14px;\n}\n\n.base-name {\n  font-size: 12px;\n  color: #666;\n  font-family: monospace;\n}\n\n.image-preview-container {\n  text-align: center;\n  background-color: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  flex: 0 0 auto;\n  max-width: 300px; /* 限制最大宽度 */\n  margin: 0 auto; /* 居中显示 */\n}\n\n.image-preview {\n  max-width: 100%;\n  height: auto;\n  cursor: pointer;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  transition: transform 0.2s ease;\n  display: block;\n  /* 优化图片加载 */\n  will-change: transform;\n  backface-visibility: hidden;\n}\n\n.image-preview:hover {\n  transform: scale(1.05);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.processing-status {\n  background-color: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  flex: 1 1 200px;\n}\n\n.status-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-summary .status-label {\n  font-size: 13px;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.status-indicators {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n}\n\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  padding: 3px 8px;\n  font-size: 11px;\n  font-weight: 500;\n  border-radius: 12px;\n  border: 1px solid;\n  white-space: nowrap;\n}\n\n.status-indicator.success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.status-indicator.error {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n/* 解析状态块样式 */\n.parsing-status-block {\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 20px;\n}\n\n.parsing-status-block h3 {\n  margin: 0 0 12px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.status-indicators-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 12px;\n}\n\n.status-indicators-grid .status-indicator {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  border-radius: 6px;\n  border: 1px solid;\n  font-size: 13px;\n  font-weight: 500;\n}\n\n.status-name {\n  flex: 1;\n}\n\n.status-icon {\n  font-weight: bold;\n  font-size: 14px;\n}\n\n.cell-header {\n  background-color: #f8f9fa;\n  padding: 8px 12px;\n  border-bottom: 1px solid #dee2e6;\n  font-weight: 600;\n  font-size: 14px;\n  color: #333;\n}\n\n.cell-content {\n  padding: 8px;\n  min-height: 100px;\n  overflow-x: auto;\n  font-size: 11px;\n  /* 优化滚动行为 */\n  overscroll-behavior: contain; /* 防止滚动传播 */\n  scrollbar-width: thin; /* Firefox */\n  scrollbar-color: #c1c1c1 #f1f1f1; /* Firefox */\n}\n\n/* 自定义滚动条样式 */\n.cell-content::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n.cell-content::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.cell-content::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.cell-content::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 当内容较多时自动调整高度 */\n.cell-content.has-content {\n  height: auto;\n  min-height: 150px;\n}\n\n.cell-content.has-table {\n  height: auto;\n  min-height: 200px;\n}\n\n.text-content {\n  margin: 0;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  font-family: 'Courier New', monospace;\n}\n\n.rendered-content {\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.rendered-content table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 0;\n}\n\n.rendered-content th,\n.rendered-content td {\n  border: 1px solid #dee2e6;\n  padding: 8px;\n  text-align: left;\n  vertical-align: top;\n}\n\n.rendered-content th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n}\n\n.accuracy-item {\n  text-align: center;\n  padding: 16px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  min-width: 120px;\n}\n\n.accuracy-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 8px;\n}\n\n.accuracy-value {\n  font-size: 20px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n/* 特征信息和指标信息容器 */\n.features-metrics-container {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 20px;\n}\n\n/* 特征信息块 */\n.features-summary-block {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  flex: 1;\n  min-width: 0; /* 允许收缩 */\n}\n\n/* 指标信息块 */\n.metrics-summary-block {\n  flex: 1;\n  min-width: 0; /* 允许收缩 */\n}\n\n.features-summary-header {\n  padding: 12px 20px;\n  border-bottom: 1px solid #e9ecef;\n  background-color: #f8f9fa;\n  border-radius: 8px 8px 0 0;\n}\n\n.features-summary-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n}\n\n.features-summary-content {\n  padding: 20px;\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.feature-item {\n  text-align: center;\n  padding: 12px;\n  background-color: #f1f8ff;\n  border-radius: 6px;\n  min-width: 100px;\n  border: 1px solid #d1ecf1;\n}\n\n.feature-label {\n  font-size: 11px;\n  color: #666;\n  margin-bottom: 6px;\n  font-weight: 500;\n}\n\n.feature-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #0c5460;\n}\n\n.feature-value-success {\n  color: #28a745;\n}\n\n.feature-value-warning {\n  color: #dc3545;\n}\n\n/* 行边框样式 - 6行布局 */\n.case-detail-row-1 .case-detail-cell {\n  border-top: 2px solid #333;\n}\n\n.case-detail-row-6 .case-detail-cell {\n  border-bottom: 2px solid #333;\n}\n\n.case-detail-row .case-detail-cell:first-child {\n  border-left: 2px solid #333;\n}\n\n.case-detail-row .case-detail-cell:last-child {\n  border-right: 2px solid #333;\n}\n\n/* 中间行样式 */\n.case-detail-row-2 .case-detail-cell,\n.case-detail-row-3 .case-detail-cell,\n.case-detail-row-4 .case-detail-cell,\n.case-detail-row-5 .case-detail-cell {\n  border-left: 2px solid #333;\n  border-right: 2px solid #333;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .case-detail-content {\n    display: block;\n  }\n\n  .case-detail-row {\n    display: block;\n    margin-bottom: 20px;\n    border: 1px solid #dee2e6;\n    border-radius: 6px;\n    overflow: hidden;\n  }\n\n  .case-detail-cell {\n    display: block;\n    width: 100%;\n    border: none;\n    border-bottom: 1px solid #dee2e6;\n  }\n\n  .case-detail-cell:last-child {\n    border-bottom: none;\n  }\n\n  .case-detail-data-info {\n    width: 100%;\n  }\n\n  .data-info-content {\n    flex-direction: row;\n    flex-wrap: wrap;\n    gap: 12px;\n  }\n\n  .sequence-number {\n    flex: 0 0 auto;\n  }\n\n  .file-info {\n    flex: 1 1 200px;\n  }\n\n  .image-preview-container {\n    flex: 0 0 auto;\n  }\n\n  .processing-status {\n    flex: 1 1 200px;\n  }\n\n  .cell-content {\n    height: 200px;\n  }\n}\n", "/* 标注编辑器样式 */\n.annotation-editor {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: #fff;\n}\n\n.annotation-editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.annotation-editor-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.annotation-editor-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.annotation-editor-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n}\n\n/* 表单样式 */\n.form-section {\n  margin-bottom: 24px;\n}\n\n.form-section h5 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #495057;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.form-section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.form-row {\n  display: flex;\n  gap: 16px;\n  align-items: end;\n}\n\n.form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 4px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 14px;\n  transition: border-color 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 内容模式切换 */\n.content-mode-toggle {\n  display: flex;\n  gap: 4px;\n}\n\n.content-mode-toggle .btn.active {\n  background: #007bff;\n  color: white;\n}\n\n/* 内容编辑器 */\n.content-editor {\n  width: 100%;\n  min-height: 300px;\n  padding: 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 13px;\n  line-height: 1.5;\n  resize: vertical;\n  transition: border-color 0.2s;\n}\n\n.content-editor:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 内容预览 */\n.content-preview {\n  min-height: 300px;\n  padding: 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  background: #f8f9fa;\n  overflow: auto;\n}\n\n.preview-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 0;\n}\n\n.preview-table th,\n.preview-table td {\n  padding: 8px 12px;\n  border: 1px solid #dee2e6;\n  text-align: left;\n}\n\n.preview-table th {\n  background: #e9ecef;\n  font-weight: 600;\n}\n\n.preview-table .header-row {\n  background: #e9ecef;\n}\n\n.content-preview pre {\n  margin: 0;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 13px;\n  line-height: 1.5;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n", "/* 标注列表样式 */\n.annotation-list {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.annotation-list-header {\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.annotation-list-header h5 {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #495057;\n}\n\n.annotation-list-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.annotation-list-loading,\n.annotation-list-empty {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.annotation-list-empty p {\n  margin: 4px 0;\n}\n\n/* 标注项样式 */\n.annotation-item {\n  margin-bottom: 12px;\n  padding: 16px;\n  background: #fff;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  transition: box-shadow 0.2s;\n}\n\n.annotation-item:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.annotation-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.annotation-item-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.annotation-id {\n  font-size: 12px;\n  font-weight: 600;\n  color: #6c757d;\n  background: #f8f9fa;\n  padding: 2px 6px;\n  border-radius: 3px;\n}\n\n.annotation-annotator {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n}\n\n.annotation-status {\n  font-size: 12px;\n  font-weight: 500;\n  padding: 2px 8px;\n  border-radius: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.status-draft {\n  background: #fff3cd;\n  color: #856404;\n}\n\n.status-completed {\n  background: #d4edda;\n  color: #155724;\n}\n\n.status-reviewed {\n  background: #d1ecf1;\n  color: #0c5460;\n}\n\n.annotation-item-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.annotation-item-content {\n  margin-bottom: 12px;\n}\n\n.annotation-structure {\n  margin-bottom: 8px;\n}\n\n.structure-info {\n  font-size: 12px;\n  color: #6c757d;\n  background: #f8f9fa;\n  padding: 4px 8px;\n  border-radius: 4px;\n  display: inline-block;\n}\n\n.annotation-content-preview {\n  background: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n  padding: 8px;\n  max-height: 100px;\n  overflow: hidden;\n}\n\n.annotation-content-preview pre {\n  margin: 0;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #495057;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n\n.annotation-item-footer {\n  display: flex;\n  gap: 16px;\n  font-size: 12px;\n  color: #6c757d;\n  border-top: 1px solid #f1f3f4;\n  padding-top: 8px;\n}\n\n.annotation-date {\n  display: flex;\n  align-items: center;\n}\n", ".annotation-manager {\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  margin: 20px 0;\n}\n\n.annotation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 2px solid #e9ecef;\n}\n\n.annotation-header h3 {\n  margin: 0;\n  color: #495057;\n  font-size: 1.5rem;\n}\n\n.annotation-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #545b62;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover:not(:disabled) {\n  background-color: #c82333;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.error-message {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 12px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  border: 1px solid #f5c6cb;\n}\n\n.annotation-stats {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  padding: 15px;\n  background: white;\n  border-radius: 6px;\n  border: 1px solid #dee2e6;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  min-width: 80px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #6c757d;\n  margin-bottom: 4px;\n}\n\n.stat-value {\n  font-size: 18px;\n  font-weight: bold;\n  color: #495057;\n}\n\n.no-dataset,\n.no-annotations,\n.loading {\n  text-align: center;\n  padding: 40px;\n  color: #6c757d;\n  background: white;\n  border-radius: 6px;\n  border: 1px solid #dee2e6;\n}\n\n.no-annotations p {\n  margin: 10px 0;\n}\n\n.annotation-table {\n  background: white;\n  border-radius: 6px;\n  border: 1px solid #dee2e6;\n  overflow: hidden;\n}\n\n.table-header,\n.table-row {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr 1fr;\n  gap: 15px;\n  padding: 12px 15px;\n  align-items: center;\n}\n\n.table-header {\n  background-color: #f8f9fa;\n  font-weight: bold;\n  color: #495057;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.table-row {\n  border-bottom: 1px solid #dee2e6;\n  transition: background-color 0.2s ease;\n}\n\n.table-row:hover {\n  background-color: #f8f9fa;\n}\n\n.table-row:last-child {\n  border-bottom: none;\n}\n\n.col-image {\n  font-family: monospace;\n  font-size: 13px;\n  word-break: break-all;\n}\n\n.type-badge,\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n\n.type-badge.auto_generated {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.type-badge.manual {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.status-badge.completed {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.status-badge.draft {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.col-created {\n  font-size: 13px;\n  color: #6c757d;\n}\n\n.col-actions {\n  display: flex;\n  justify-content: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .annotation-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n\n  .annotation-actions {\n    justify-content: center;\n  }\n\n  .annotation-stats {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .table-header,\n  .table-row {\n    grid-template-columns: 1fr;\n    gap: 8px;\n  }\n\n  .table-header {\n    display: none;\n  }\n\n  .table-row {\n    padding: 15px;\n    border: 1px solid #dee2e6;\n    margin-bottom: 10px;\n    border-radius: 6px;\n  }\n\n  .table-row > div {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 5px 0;\n  }\n\n  .table-row > div::before {\n    content: attr(data-label);\n    font-weight: bold;\n    color: #495057;\n  }\n\n  .col-image::before { content: \"图片: \"; }\n  .col-annotator::before { content: \"标注员: \"; }\n  .col-type::before { content: \"类型: \"; }\n  .col-status::before { content: \"状态: \"; }\n  .col-created::before { content: \"创建时间: \"; }\n  .col-actions::before { content: \"操作: \"; }\n}\n", "/* 标注面板样式 */\n.annotation-panel {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: #fff;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.annotation-panel-header {\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.annotation-panel-header h3 {\n  margin: 0 0 12px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.annotation-tab-navigation {\n  display: flex;\n  gap: 8px;\n  margin-top: 8px;\n}\n\n.annotation-tab-navigation .tab-button {\n  padding: 8px 16px;\n  border: 1px solid #dee2e6;\n  background: #fff;\n  color: #495057;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.annotation-tab-navigation .tab-button:hover {\n  background: #f8f9fa;\n  border-color: #adb5bd;\n}\n\n.annotation-tab-navigation .tab-button.active {\n  background: #007bff;\n  color: #fff;\n  border-color: #007bff;\n}\n\n.annotation-tab-navigation .tab-button.active:hover {\n  background: #0056b3;\n  border-color: #0056b3;\n}\n\n.annotation-panel-info {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: #6c757d;\n}\n\n.annotation-panel-empty {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.annotation-error {\n  margin: 16px;\n  padding: 12px;\n  background: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  color: #721c24;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.annotation-error button {\n  background: none;\n  border: none;\n  color: #721c24;\n  cursor: pointer;\n  font-weight: bold;\n}\n\n.annotation-panel-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.annotation-panel-actions {\n  padding: 16px;\n  display: flex;\n  gap: 8px;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n/* 按钮样式 */\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.2s;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-primary {\n  background: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #0056b3;\n}\n\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background: #545b62;\n}\n\n.btn-small {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.btn-danger {\n  background: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover:not(:disabled) {\n  background: #c82333;\n}\n", ".app {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8f9fa;\n}\n\n.app-header {\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\n  color: white;\n  padding: 20px 0;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.app-title {\n  margin: 0 0 8px 0;\n  font-size: 28px;\n  font-weight: 700;\n}\n\n.app-subtitle {\n  margin: 0;\n  font-size: 16px;\n  opacity: 0.9;\n  font-weight: 300;\n}\n\n.app-main {\n  flex: 1;\n  padding: 20px 0;\n}\n\n.app-section {\n  margin-bottom: 20px;\n}\n\n/* 标签导航样式 */\n.tab-navigation {\n  display: flex;\n  gap: 2px;\n  background: #e9ecef;\n  padding: 4px;\n  border-radius: 8px;\n  width: fit-content;\n}\n\n.tab-button {\n  padding: 12px 24px;\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  font-size: 14px;\n  font-weight: 500;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.tab-button:hover {\n  background: #dee2e6;\n  color: #495057;\n}\n\n.tab-button.active {\n  background: #007bff;\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n}\n\n.app-content {\n  display: flex;\n  gap: 20px;\n  min-height: 600px;\n}\n\n.app-sidebar {\n  width: 30%;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.app-detail {\n  width: 70%;\n  flex: 1;\n}\n\n.app-footer {\n  background-color: #343a40;\n  color: white;\n  padding: 16px 0;\n  text-align: center;\n  margin-top: auto;\n}\n\n.app-footer p {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.8;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .app-content {\n    flex-direction: column;\n  }\n\n  .app-sidebar,\n  .app-detail {\n    width: 100%;\n  }\n}\n\n@media (max-width: 992px) {\n  .app-content {\n    gap: 16px;\n  }\n\n  .app-sidebar {\n    height: 250px;\n  }\n}\n\n@media (max-width: 768px) {\n  .app-header {\n    padding: 16px 0;\n  }\n  \n  .app-title {\n    font-size: 24px;\n  }\n  \n  .app-subtitle {\n    font-size: 14px;\n  }\n  \n  .app-main {\n    padding: 16px 0;\n  }\n  \n  .container {\n    padding: 0 16px;\n  }\n}\n\n@media (max-width: 576px) {\n  .app-title {\n    font-size: 20px;\n  }\n  \n  .app-subtitle {\n    font-size: 13px;\n  }\n  \n  .app-main {\n    padding: 12px 0;\n  }\n  \n  .container {\n    padding: 0 12px;\n  }\n  \n  .app-content {\n    gap: 16px;\n  }\n}\n\n/* 加载状态 */\n.app-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n  color: #666;\n}\n\n.app-loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-right: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 错误状态 */\n.app-error {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n  padding: 16px;\n  border-radius: 8px;\n  margin: 20px 0;\n}\n\n.app-error-title {\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.app-error-message {\n  margin: 0;\n}\n\n/* 空状态 */\n.app-empty {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n}\n\n.app-empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.5;\n}\n\n.app-empty-title {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.app-empty-message {\n  font-size: 14px;\n  opacity: 0.8;\n}\n\n/* 性能优化相关样式 */\n.app-virtualized {\n  height: 100%;\n  width: 100%;\n}\n\n.app-lazy-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  color: #666;\n  font-size: 14px;\n}\n\n.app-lazy-loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-right: 8px;\n}\n\n/* 滚动优化 */\n.app-scroll-container {\n  scroll-behavior: smooth;\n}\n\n.app-scroll-container::-webkit-scrollbar {\n  width: 8px;\n}\n\n.app-scroll-container::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n.app-scroll-container::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n.app-scroll-container::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 统计面板样式 */\n.stats-panel {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 16px;\n}\n\n.stats-panel-header {\n  padding-bottom: 12px;\n  border-bottom: 1px solid #e9ecef;\n  margin-bottom: 16px;\n}\n\n.stats-panel-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n\n.stat-item {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 12px;\n  text-align: center;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.stat-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #007bff;\n}\n\n.stat-value.success {\n  color: #28a745;\n}\n\n.stat-value.warning {\n  color: #ffc107;\n}\n\n.stat-value.danger {\n  color: #dc3545;\n}\n"], "names": [], "sourceRoot": ""}