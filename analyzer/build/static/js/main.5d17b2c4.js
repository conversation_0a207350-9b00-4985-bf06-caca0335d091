/*! For license information please see main.5d17b2c4.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,l={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!i.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:l,_owner:o.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),i=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var k=Array.isArray,w=Object.prototype.hasOwnProperty,j={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,l={},s=null,o=null;if(null!=t)for(a in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(s=""+t.key),t)w.call(t,a)&&!N.hasOwnProperty(a)&&(l[a]=t[a]);var i=arguments.length-2;if(1===i)l.children=r;else if(1<i){for(var c=Array(i),u=0;u<i;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(a in i=e.defaultProps)void 0===l[a]&&(l[a]=i[a]);return{$$typeof:n,type:e,key:s,ref:o,props:l,_owner:j.current}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function E(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function R(e,t,a,l,s){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var i=!1;if(null===e)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case n:case r:i=!0}}if(i)return s=s(i=e),e=""===l?"."+E(i,0):l,k(s)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),R(s,t,a,"",function(e){return e})):null!=s&&(_(s)&&(s=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,a+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+e)),t.push(s)),1;if(i=0,l=""===l?".":l+":",k(e))for(var c=0;c<e.length;c++){var u=l+E(o=e[c],c);i+=R(o,t,a,u,s)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(o=e.next()).done;)i+=R(o=o.value,t,a,u=l+E(o,c++),s);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function O(e,t,n){if(null==e)return e;var r=[],a=0;return R(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P={current:null},T={transition:null},M={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:T,ReactCurrentOwner:j};function A(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:O,forEach:function(e,t,n){O(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return O(e,function(){t++}),t},toArray:function(e){return O(e,function(e){return e})||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=s,t.PureComponent=b,t.StrictMode=l,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=A,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),l=e.key,s=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,o=j.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(c in t)w.call(t,c)&&!N.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==i?i[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){i=Array(c);for(var u=0;u<c;u++)i[u]=arguments[u+2];a.children=i}return{$$typeof:n,type:e.type,key:l,ref:s,props:a,_owner:o}},t.createContext=function(e){return(e={$$typeof:i,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=A,t.useCallback=function(e,t){return P.current.useCallback(e,t)},t.useContext=function(e){return P.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return P.current.useDeferredValue(e)},t.useEffect=function(e,t){return P.current.useEffect(e,t)},t.useId=function(){return P.current.useId()},t.useImperativeHandle=function(e,t,n){return P.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return P.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return P.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return P.current.useMemo(e,t)},t.useReducer=function(e,t,n){return P.current.useReducer(e,t,n)},t.useRef=function(e){return P.current.useRef(e)},t.useState=function(e){return P.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return P.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return P.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,s=a>>>1;r<s;){var o=2*(r+1)-1,i=e[o],c=o+1,u=e[c];if(0>l(i,n))c<a&&0>l(u,i)?(e[r]=u,e[c]=n,r=c):(e[r]=i,e[o]=n,r=o);else{if(!(c<a&&0>l(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,i=o.now();t.unstable_now=function(){return o.now()-i}}var c=[],u=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function k(e){if(g=!1,x(e),!m)if(null!==r(c))m=!0,T(w);else{var t=r(u);null!==t&&M(k,t.startTime-e)}}function w(e,n){m=!1,g&&(g=!1,v(_),_=-1),h=!0;var l=p;try{for(x(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!R());){var s=f.callback;if("function"===typeof s){f.callback=null,p=f.priorityLevel;var o=s(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof o?f.callback=o:f===r(c)&&a(c),x(n)}else a(c);f=r(c)}if(null!==f)var i=!0;else{var d=r(u);null!==d&&M(k,d.startTime-n),i=!1}return i}finally{f=null,p=l,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var j,N=!1,S=null,_=-1,C=5,E=-1;function R(){return!(t.unstable_now()-E<C)}function O(){if(null!==S){var e=t.unstable_now();E=e;var n=!0;try{n=S(!0,e)}finally{n?j():(N=!1,S=null)}}else N=!1}if("function"===typeof b)j=function(){b(O)};else if("undefined"!==typeof MessageChannel){var L=new MessageChannel,P=L.port2;L.port1.onmessage=O,j=function(){P.postMessage(null)}}else j=function(){y(O,0)};function T(e){S=e,N||(N=!0,j())}function M(e,n){_=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,T(w))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var s=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?s+l:s:l=s,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>s?(e.sortIndex=l,n(u,e),null===r(c)&&e===r(u)&&(g?(v(_),_=-1):g=!0,M(k,l-s))):(e.sortIndex=o,n(c,e),m||h||(m=!0,T(w))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,o={};function i(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(o[e]=t,e=0;e<t.length;e++)s.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,l,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),w=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),C=Symbol.for("react.context"),E=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),O=Symbol.for("react.suspense_list"),L=Symbol.for("react.memo"),P=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var T=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var M=Symbol.iterator;function A(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=M&&e[M]||e["@@iterator"])?e:null}var D,z=Object.assign;function F(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var I=!1;function U(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),l=r.stack.split("\n"),s=a.length-1,o=l.length-1;1<=s&&0<=o&&a[s]!==l[o];)o--;for(;1<=s&&0<=o;s--,o--)if(a[s]!==l[o]){if(1!==s||1!==o)do{if(s--,0>--o||a[s]!==l[o]){var i="\n"+a[s].replace(" at new "," at ");return e.displayName&&i.includes("<anonymous>")&&(i=i.replace("<anonymous>",e.displayName)),i}}while(1<=s&&0<=o);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case j:return"Fragment";case w:return"Portal";case S:return"Profiler";case N:return"StrictMode";case R:return"Suspense";case O:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case E:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case L:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case P:t=e._payload,e=e._init;try{return V(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===N?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function K(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function $(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=K(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){Y(e,t);var n=K(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,K(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+K(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:K(n)}}function le(e,t){var n=K(t.value),r=K(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function se(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ie(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){he.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ye=z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,je=null,Ne=null;function Se(e){if(e=ba(e)){if("function"!==typeof we)throw Error(l(280));var t=e.stateNode;t&&(t=ka(t),we(e.stateNode,e.type,t))}}function _e(e){je?Ne?Ne.push(e):Ne=[e]:je=e}function Ce(){if(je){var e=je,t=Ne;if(Ne=je=null,Se(e),t)for(e=0;e<t.length;e++)Se(t[e])}}function Ee(e,t){return e(t)}function Re(){}var Oe=!1;function Le(e,t,n){if(Oe)return e(t,n);Oe=!0;try{return Ee(e,t,n)}finally{Oe=!1,(null!==je||null!==Ne)&&(Re(),Ce())}}function Pe(e,t){var n=e.stateNode;if(null===n)return null;var r=ka(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Te=!1;if(u)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){Te=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(ue){Te=!1}function Ae(e,t,n,r,a,l,s,o,i){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var De=!1,ze=null,Fe=!1,Ie=null,Ue={onError:function(e){De=!0,ze=e}};function Be(e,t,n,r,a,l,s,o,i){De=!1,ze=null,Ae.apply(Ue,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ke(e){if(Ve(e)!==e)throw Error(l(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var s=a.alternate;if(null===s){if(null!==(r=a.return)){n=r;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===n)return Ke(a),e;if(s===r)return Ke(a),t;s=s.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=s;else{for(var o=!1,i=a.child;i;){if(i===n){o=!0,n=a,r=s;break}if(i===r){o=!0,r=a,n=s;break}i=i.sibling}if(!o){for(i=s.child;i;){if(i===n){o=!0,n=s,r=a;break}if(i===r){o=!0,r=s,n=a;break}i=i.sibling}if(!o)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?$e(e):null}function $e(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=$e(e);if(null!==t)return t;e=e.sibling}return null}var qe=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ye=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ot(e)/it|0)|0},ot=Math.log,it=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,s=268435455&n;if(0!==s){var o=s&~a;0!==o?r=dt(o):0!==(l&=s)&&(r=dt(l))}else 0!==(s=n&~a)?r=dt(s):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-st(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var kt,wt,jt,Nt,St,_t=!1,Ct=[],Et=null,Rt=null,Ot=null,Lt=new Map,Pt=new Map,Tt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Et=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Lt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pt.delete(t.pointerId)}}function Dt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function zt(e){var t=va(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void St(e.priority,function(){jt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function It(e,t,n){Ft(e)&&n.delete(t)}function Ut(){_t=!1,null!==Et&&Ft(Et)&&(Et=null),null!==Rt&&Ft(Rt)&&(Rt=null),null!==Ot&&Ft(Ot)&&(Ot=null),Lt.forEach(It),Pt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,_t||(_t=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ut)))}function Vt(e){function t(t){return Bt(t,e)}if(0<Ct.length){Bt(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Et&&Bt(Et,e),null!==Rt&&Bt(Rt,e),null!==Ot&&Bt(Ot,e),Lt.forEach(t),Pt.forEach(t),n=0;n<Tt.length;n++)(r=Tt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&null===(n=Tt[0]).blockedOn;)zt(n),null===n.blockedOn&&Tt.shift()}var Wt=x.ReactCurrentBatchConfig,Kt=!0;function Ht(e,t,n,r){var a=bt,l=Wt.transition;Wt.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=a,Wt.transition=l}}function $t(e,t,n,r){var a=bt,l=Wt.transition;Wt.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=a,Wt.transition=l}}function qt(e,t,n,r){if(Kt){var a=Jt(e,t,n,r);if(null===a)Kr(e,t,r,Qt,n),At(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Et=Dt(Et,e,t,n,r,a),!0;case"dragenter":return Rt=Dt(Rt,e,t,n,r,a),!0;case"mouseover":return Ot=Dt(Ot,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Lt.set(l,Dt(Lt.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Pt.set(l,Dt(Pt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Mt.indexOf(e)){for(;null!==a;){var l=ba(a);if(null!==l&&kt(l),null===(l=Jt(e,t,n,r))&&Kr(e,t,r,Qt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Kr(e,t,r,null,n)}}var Qt=null;function Jt(e,t,n,r){if(Qt=null,null!==(e=va(e=ke(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===a[l-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(a):a[s]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,sn,on,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=z({},cn,{view:0,detail:0}),fn=an(dn),pn=z({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==on&&(on&&"mousemove"===e.type?(ln=e.screenX-on.screenX,sn=e.screenY-on.screenY):sn=ln=0,on=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),hn=an(pn),mn=an(z({},pn,{dataTransfer:0})),gn=an(z({},dn,{relatedTarget:0})),yn=an(z({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=z({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),xn=an(z({},cn,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=jn[e])&&!!t[e]}function Sn(){return Nn}var _n=z({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Cn=an(_n),En=an(z({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Rn=an(z({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sn})),On=an(z({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Ln=z({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Pn=an(Ln),Tn=[9,13,27,32],Mn=u&&"CompositionEvent"in window,An=null;u&&"documentMode"in document&&(An=document.documentMode);var Dn=u&&"TextEvent"in window&&!An,zn=u&&(!Mn||An&&8<An&&11>=An),Fn=String.fromCharCode(32),In=!1;function Un(e,t){switch(e){case"keyup":return-1!==Tn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Hn(e,t,n,r){_e(r),0<(t=$r(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $n=null,qn=null;function Qn(e){Fr(e,0)}function Jn(e){if(q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Yn=!1;if(u){var Gn;if(u){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Yn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){$n&&($n.detachEvent("onpropertychange",nr),qn=$n=null)}function nr(e){if("value"===e.propertyName&&Jn(qn)){var t=[];Hn(t,qn,e,ke(e)),Le(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,($n=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(qn)}function lr(e,t){if("click"===e)return Jn(t)}function sr(e,t){if("input"===e||"change"===e)return Jn(t)}var or="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function ir(e,t){if(or(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!or(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=ur(n,l);var s=ur(n,r);a&&s&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&ir(vr,r)||(vr=r,0<(r=$r(yr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},jr={},Nr={};function Sr(e){if(jr[e])return jr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Nr)return jr[e]=n[t];return e}u&&(Nr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var _r=Sr("animationend"),Cr=Sr("animationiteration"),Er=Sr("animationstart"),Rr=Sr("transitionend"),Or=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pr(e,t){Or.set(e,t),i(t,[e])}for(var Tr=0;Tr<Lr.length;Tr++){var Mr=Lr[Tr];Pr(Mr.toLowerCase(),"on"+(Mr[0].toUpperCase()+Mr.slice(1)))}Pr(_r,"onAnimationEnd"),Pr(Cr,"onAnimationIteration"),Pr(Er,"onAnimationStart"),Pr("dblclick","onDoubleClick"),Pr("focusin","onFocus"),Pr("focusout","onBlur"),Pr(Rr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),i("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),i("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),i("onBeforeInput",["compositionend","keypress","textInput","paste"]),i("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),i("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),i("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));function zr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,s,o,i,c){if(Be.apply(this,arguments),De){if(!De)throw Error(l(198));var u=ze;De=!1,ze=null,Fe||(Fe=!0,Ie=u)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var o=r[s],i=o.instance,c=o.currentTarget;if(o=o.listener,i!==l&&a.isPropagationStopped())break e;zr(a,o,c),l=i}else for(s=0;s<r.length;s++){if(i=(o=r[s]).instance,c=o.currentTarget,o=o.listener,i!==l&&a.isPropagationStopped())break e;zr(a,o,c),l=i}}}if(Fe)throw e=Ie,Fe=!1,Ie=null,e}function Ir(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[Br]){e[Br]=!0,s.forEach(function(t){"selectionchange"!==t&&(Dr.has(t)||Ur(t,!1,e),Ur(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ur("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Xt(t)){case 1:var a=Ht;break;case 4:a=$t;break;default:a=qt}n=a.bind(null,t,n,e),a=void 0,!Te||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Kr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var s=r.tag;if(3===s||4===s){var o=r.stateNode.containerInfo;if(o===a||8===o.nodeType&&o.parentNode===a)break;if(4===s)for(s=r.return;null!==s;){var i=s.tag;if((3===i||4===i)&&((i=s.stateNode.containerInfo)===a||8===i.nodeType&&i.parentNode===a))return;s=s.return}for(;null!==o;){if(null===(s=va(o)))return;if(5===(i=s.tag)||6===i){r=l=s;continue e}o=o.parentNode}}r=r.return}Le(function(){var r=l,a=ke(n),s=[];e:{var o=Or.get(e);if(void 0!==o){var i=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":i=Cn;break;case"focusin":c="focus",i=gn;break;case"focusout":c="blur",i=gn;break;case"beforeblur":case"afterblur":i=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":i=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":i=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":i=Rn;break;case _r:case Cr:case Er:i=yn;break;case Rr:i=On;break;case"scroll":i=fn;break;case"wheel":i=Pn;break;case"copy":case"cut":case"paste":i=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":i=En}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==o?o+"Capture":null:o;u=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Pe(h,f))&&u.push(Hr(h,m,p)))),d)break;h=h.return}0<u.length&&(o=new i(o,c,null,n,a),s.push({event:o,listeners:u}))}}if(0===(7&t)){if(i="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!va(c)&&!c[ha])&&(i||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,i?(i=r,null!==(c=(c=n.relatedTarget||n.toElement)?va(c):null)&&(c!==(d=Ve(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(i=null,c=r),i!==c)){if(u=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=En,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==i?o:xa(i),p=null==c?o:xa(c),(o=new u(m,h+"leave",i,n,a)).target=d,o.relatedTarget=p,m=null,va(a)===r&&((u=new u(f,h+"enter",c,n,a)).target=p,u.relatedTarget=d,m=u),d=m,i&&c)e:{for(f=c,h=0,p=u=i;p;p=qr(p))h++;for(p=0,m=f;m;m=qr(m))p++;for(;0<h-p;)u=qr(u),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=qr(u),f=qr(f)}u=null}else u=null;null!==i&&Qr(s,o,i,u,!1),null!==c&&null!==d&&Qr(s,d,c,u,!0)}if("select"===(i=(o=r?xa(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===i&&"file"===o.type)var g=Xn;else if(Kn(o))if(Yn)g=sr;else{g=ar;var y=rr}else(i=o.nodeName)&&"input"===i.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(g=lr);switch(g&&(g=g(e,r))?Hn(s,g,n,a):(y&&y(e,o,r),"focusout"===e&&(y=o._wrapperState)&&y.controlled&&"number"===o.type&&ee(o,"number",o.value)),y=r?xa(r):window,e){case"focusin":(Kn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(s,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(s,n,a)}var v;if(Mn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Vn?Un(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(zn&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Vn&&(v=en()):(Gt="value"in(Yt=a)?Yt.value:Yt.textContent,Vn=!0)),0<(y=$r(r,b)).length&&(b=new xn(b,e,null,n,a),s.push({event:b,listeners:y}),v?b.data=v:null!==(v=Bn(n))&&(b.data=v))),(v=Dn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,Fn);case"textInput":return(e=t.data)===Fn&&In?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!Mn&&Un(e,t)?(e=en(),Zt=Gt=Yt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=$r(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),s.push({event:a,listeners:r}),a.data=v))}Fr(s,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $r(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Pe(e,n))&&r.unshift(Hr(e,l,a)),null!=(l=Pe(e,t))&&r.push(Hr(e,l,a))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,a){for(var l=t._reactName,s=[];null!==n&&n!==r;){var o=n,i=o.alternate,c=o.stateNode;if(null!==i&&i===r)break;5===o.tag&&null!==c&&(o=c,a?null!=(i=Pe(n,l))&&s.unshift(Hr(n,i,o)):a||null!=(i=Pe(n,l))&&s.push(Hr(n,i,o))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Jr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Xr,"")}function Gr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(l(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,sa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(oa)}:ra;function oa(e){setTimeout(function(){throw e})}function ia(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Vt(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function ka(e){return e[pa]||null}var wa=[],ja=-1;function Na(e){return{current:e}}function Sa(e){0>ja||(e.current=wa[ja],wa[ja]=null,ja--)}function _a(e,t){ja++,wa[ja]=e.current,e.current=t}var Ca={},Ea=Na(Ca),Ra=Na(!1),Oa=Ca;function La(e,t){var n=e.type.contextTypes;if(!n)return Ca;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Pa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ta(){Sa(Ra),Sa(Ea)}function Ma(e,t,n){if(Ea.current!==Ca)throw Error(l(168));_a(Ea,t),_a(Ra,n)}function Aa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,W(e)||"Unknown",a));return z({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ca,Oa=Ea.current,_a(Ea,e),_a(Ra,Ra.current),!0}function za(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Aa(e,t,Oa),r.__reactInternalMemoizedMergedChildContext=e,Sa(Ra),Sa(Ea),_a(Ea,e)):Sa(Ra),_a(Ra,n)}var Fa=null,Ia=!1,Ua=!1;function Ba(e){null===Fa?Fa=[e]:Fa.push(e)}function Va(){if(!Ua&&null!==Fa){Ua=!0;var e=0,t=bt;try{var n=Fa;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Fa=null,Ia=!1}catch(a){throw null!==Fa&&(Fa=Fa.slice(e+1)),qe(Ze,Va),a}finally{bt=t,Ua=!1}}return null}var Wa=[],Ka=0,Ha=null,$a=0,qa=[],Qa=0,Ja=null,Xa=1,Ya="";function Ga(e,t){Wa[Ka++]=$a,Wa[Ka++]=Ha,Ha=e,$a=t}function Za(e,t,n){qa[Qa++]=Xa,qa[Qa++]=Ya,qa[Qa++]=Ja,Ja=e;var r=Xa;e=Ya;var a=32-st(r)-1;r&=~(1<<a),n+=1;var l=32-st(t)+a;if(30<l){var s=a-a%5;l=(r&(1<<s)-1).toString(32),r>>=s,a-=s,Xa=1<<32-st(t)+a|n<<a|r,Ya=l+e}else Xa=1<<l|n<<a|r,Ya=e}function el(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function tl(e){for(;e===Ha;)Ha=Wa[--Ka],Wa[Ka]=null,$a=Wa[--Ka],Wa[Ka]=null;for(;e===Ja;)Ja=qa[--Qa],qa[Qa]=null,Ya=qa[--Qa],qa[Qa]=null,Xa=qa[--Qa],qa[Qa]=null}var nl=null,rl=null,al=!1,ll=null;function sl(e,t){var n=Lc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ol(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Xa,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function il(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function cl(e){if(al){var t=rl;if(t){var n=t;if(!ol(e,t)){if(il(e))throw Error(l(418));t=ca(n.nextSibling);var r=nl;t&&ol(e,t)?sl(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(il(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function ul(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return ul(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(il(e))throw fl(),Error(l(418));for(;t;)sl(e,t),t=ca(t.nextSibling)}if(ul(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ca(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ca(e.nextSibling)}function pl(){rl=nl=null,al=!1}function hl(e){null===ll?ll=[e]:ll.push(e)}var ml=x.ReactCurrentBatchConfig;function gl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,s=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===s?t.ref:(t=function(e){var t=a.refs;null===e?delete t[s]:t[s]=e},t._stringRef=s,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function yl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function vl(e){return(0,e._init)(e._payload)}function bl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Tc(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=2),t}function i(e,t,n,r){return null===t||6!==t.tag?((t=zc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var l=n.type;return l===j?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===P&&vl(l)===t.type)?((r=a(t,n.props)).ref=gl(e,t,n),r.return=e,r):((r=Mc(n.type,n.key,n.props,null,e.mode,r)).ref=gl(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Fc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Ac(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=zc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Mc(t.type,t.key,t.props,null,e.mode,n)).ref=gl(e,null,t),n.return=e,n;case w:return(t=Fc(t,e.mode,n)).return=e,t;case P:return f(e,(0,t._init)(t._payload),n)}if(te(t)||A(t))return(t=Ac(t,e.mode,n,null)).return=e,t;yl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:i(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===a?c(e,t,n,r):null;case w:return n.key===a?u(e,t,n,r):null;case P:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||A(n))return null!==a?null:d(e,t,n,r,null);yl(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return i(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case P:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||A(r))return d(t,e=e.get(n)||null,r,a,null);yl(t,r)}return null}function m(a,l,o,i){for(var c=null,u=null,d=l,m=l=0,g=null;null!==d&&m<o.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(a,d,o[m],i);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),l=s(y,l,m),null===u?c=y:u.sibling=y,u=y,d=g}if(m===o.length)return n(a,d),al&&Ga(a,m),c;if(null===d){for(;m<o.length;m++)null!==(d=f(a,o[m],i))&&(l=s(d,l,m),null===u?c=d:u.sibling=d,u=d);return al&&Ga(a,m),c}for(d=r(a,d);m<o.length;m++)null!==(g=h(d,a,m,o[m],i))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),l=s(g,l,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach(function(e){return t(a,e)}),al&&Ga(a,m),c}function g(a,o,i,c){var u=A(i);if("function"!==typeof u)throw Error(l(150));if(null==(i=u.call(i)))throw Error(l(151));for(var d=u=null,m=o,g=o=0,y=null,v=i.next();null!==m&&!v.done;g++,v=i.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(a,m,v.value,c);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(a,m),o=s(b,o,g),null===d?u=b:d.sibling=b,d=b,m=y}if(v.done)return n(a,m),al&&Ga(a,g),u;if(null===m){for(;!v.done;g++,v=i.next())null!==(v=f(a,v.value,c))&&(o=s(v,o,g),null===d?u=v:d.sibling=v,d=v);return al&&Ga(a,g),u}for(m=r(a,m);!v.done;g++,v=i.next())null!==(v=h(m,a,g,v.value,c))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),o=s(v,o,g),null===d?u=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(a,e)}),al&&Ga(a,g),u}return function e(r,l,s,i){if("object"===typeof s&&null!==s&&s.type===j&&null===s.key&&(s=s.props.children),"object"===typeof s&&null!==s){switch(s.$$typeof){case k:e:{for(var c=s.key,u=l;null!==u;){if(u.key===c){if((c=s.type)===j){if(7===u.tag){n(r,u.sibling),(l=a(u,s.props.children)).return=r,r=l;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===P&&vl(c)===u.type){n(r,u.sibling),(l=a(u,s.props)).ref=gl(r,u,s),l.return=r,r=l;break e}n(r,u);break}t(r,u),u=u.sibling}s.type===j?((l=Ac(s.props.children,r.mode,i,s.key)).return=r,r=l):((i=Mc(s.type,s.key,s.props,null,r.mode,i)).ref=gl(r,l,s),i.return=r,r=i)}return o(r);case w:e:{for(u=s.key;null!==l;){if(l.key===u){if(4===l.tag&&l.stateNode.containerInfo===s.containerInfo&&l.stateNode.implementation===s.implementation){n(r,l.sibling),(l=a(l,s.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Fc(s,r.mode,i)).return=r,r=l}return o(r);case P:return e(r,l,(u=s._init)(s._payload),i)}if(te(s))return m(r,l,s,i);if(A(s))return g(r,l,s,i);yl(r,s)}return"string"===typeof s&&""!==s||"number"===typeof s?(s=""+s,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,s)).return=r,r=l):(n(r,l),(l=zc(s,r.mode,i)).return=r,r=l),o(r)):n(r,l)}}var xl=bl(!0),kl=bl(!1),wl=Na(null),jl=null,Nl=null,Sl=null;function _l(){Sl=Nl=jl=null}function Cl(e){var t=wl.current;Sa(wl),e._currentValue=t}function El(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Rl(e,t){jl=e,Sl=Nl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bo=!0),e.firstContext=null)}function Ol(e){var t=e._currentValue;if(Sl!==e)if(e={context:e,memoizedValue:t,next:null},null===Nl){if(null===jl)throw Error(l(308));Nl=e,jl.dependencies={lanes:0,firstContext:e}}else Nl=Nl.next=e;return t}var Ll=null;function Pl(e){null===Ll?Ll=[e]:Ll.push(e)}function Tl(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Pl(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ml(e,r)}function Ml(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Al=!1;function Dl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fl(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Il(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ei)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ml(e,n)}return null===(a=r.interleaved)?(t.next=t,Pl(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ml(e,n)}function Ul(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vl(e,t,n,r){var a=e.updateQueue;Al=!1;var l=a.firstBaseUpdate,s=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var i=o,c=i.next;i.next=null,null===s?l=c:s.next=c,s=i;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==s&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=i))}if(null!==l){var d=a.baseState;for(s=0,u=c=i=null,o=l;;){var f=o.lane,p=o.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var h=e,m=o;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=z({},d,f);break e;case 2:Al=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[o]:f.push(o))}else p={eventTime:p,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=p,i=d):u=u.next=p,s|=f;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(f=o).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(i=d),a.baseState=i,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{s|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Di|=s,e.lanes=s,e.memoizedState=d}}function Wl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var Kl={},Hl=Na(Kl),$l=Na(Kl),ql=Na(Kl);function Ql(e){if(e===Kl)throw Error(l(174));return e}function Jl(e,t){switch(_a(ql,t),_a($l,e),_a(Hl,Kl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ie(null,"");break;default:t=ie(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Sa(Hl),_a(Hl,t)}function Xl(){Sa(Hl),Sa($l),Sa(ql)}function Yl(e){Ql(ql.current);var t=Ql(Hl.current),n=ie(t,e.type);t!==n&&(_a($l,e),_a(Hl,n))}function Gl(e){$l.current===e&&(Sa(Hl),Sa($l))}var Zl=Na(0);function es(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ts=[];function ns(){for(var e=0;e<ts.length;e++)ts[e]._workInProgressVersionPrimary=null;ts.length=0}var rs=x.ReactCurrentDispatcher,as=x.ReactCurrentBatchConfig,ls=0,ss=null,os=null,is=null,cs=!1,us=!1,ds=0,fs=0;function ps(){throw Error(l(321))}function hs(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!or(e[n],t[n]))return!1;return!0}function ms(e,t,n,r,a,s){if(ls=s,ss=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,rs.current=null===e||null===e.memoizedState?Gs:Zs,e=n(r,a),us){s=0;do{if(us=!1,ds=0,25<=s)throw Error(l(301));s+=1,is=os=null,t.updateQueue=null,rs.current=eo,e=n(r,a)}while(us)}if(rs.current=Ys,t=null!==os&&null!==os.next,ls=0,is=os=ss=null,cs=!1,t)throw Error(l(300));return e}function gs(){var e=0!==ds;return ds=0,e}function ys(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===is?ss.memoizedState=is=e:is=is.next=e,is}function vs(){if(null===os){var e=ss.alternate;e=null!==e?e.memoizedState:null}else e=os.next;var t=null===is?ss.memoizedState:is.next;if(null!==t)is=t,os=e;else{if(null===e)throw Error(l(310));e={memoizedState:(os=e).memoizedState,baseState:os.baseState,baseQueue:os.baseQueue,queue:os.queue,next:null},null===is?ss.memoizedState=is=e:is=is.next=e}return is}function bs(e,t){return"function"===typeof t?t(e):t}function xs(e){var t=vs(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=os,a=r.baseQueue,s=n.pending;if(null!==s){if(null!==a){var o=a.next;a.next=s.next,s.next=o}r.baseQueue=a=s,n.pending=null}if(null!==a){s=a.next,r=r.baseState;var i=o=null,c=null,u=s;do{var d=u.lane;if((ls&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(i=c=f,o=r):c=c.next=f,ss.lanes|=d,Di|=d}u=u.next}while(null!==u&&u!==s);null===c?o=r:c.next=i,or(r,t.memoizedState)||(bo=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{s=a.lane,ss.lanes|=s,Di|=s,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ks(e){var t=vs(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,s=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{s=e(s,o.action),o=o.next}while(o!==a);or(s,t.memoizedState)||(bo=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function ws(){}function js(e,t){var n=ss,r=vs(),a=t(),s=!or(r.memoizedState,a);if(s&&(r.memoizedState=a,bo=!0),r=r.queue,As(_s.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||null!==is&&1&is.memoizedState.tag){if(n.flags|=2048,Os(9,Ss.bind(null,n,r,a,t),void 0,null),null===Ri)throw Error(l(349));0!==(30&ls)||Ns(n,t,a)}return a}function Ns(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ss(e,t,n,r){t.value=n,t.getSnapshot=r,Cs(t)&&Es(e)}function _s(e,t,n){return n(function(){Cs(t)&&Es(e)})}function Cs(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!or(e,n)}catch(r){return!0}}function Es(e){var t=Ml(e,1);null!==t&&nc(t,e,1,-1)}function Rs(e){var t=ys();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bs,lastRenderedState:e},t.queue=e,e=e.dispatch=qs.bind(null,ss,e),[t.memoizedState,e]}function Os(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ls(){return vs().memoizedState}function Ps(e,t,n,r){var a=ys();ss.flags|=e,a.memoizedState=Os(1|t,n,void 0,void 0===r?null:r)}function Ts(e,t,n,r){var a=vs();r=void 0===r?null:r;var l=void 0;if(null!==os){var s=os.memoizedState;if(l=s.destroy,null!==r&&hs(r,s.deps))return void(a.memoizedState=Os(t,n,l,r))}ss.flags|=e,a.memoizedState=Os(1|t,n,l,r)}function Ms(e,t){return Ps(8390656,8,e,t)}function As(e,t){return Ts(2048,8,e,t)}function Ds(e,t){return Ts(4,2,e,t)}function zs(e,t){return Ts(4,4,e,t)}function Fs(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Is(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ts(4,4,Fs.bind(null,t,e),n)}function Us(){}function Bs(e,t){var n=vs();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vs(e,t){var n=vs();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ws(e,t,n){return 0===(21&ls)?(e.baseState&&(e.baseState=!1,bo=!0),e.memoizedState=n):(or(n,t)||(n=mt(),ss.lanes|=n,Di|=n,e.baseState=!0),t)}function Ks(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{bt=n,as.transition=r}}function Hs(){return vs().memoizedState}function $s(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Qs(e))Js(t,n);else if(null!==(n=Tl(e,t,n,r))){nc(n,e,r,ec()),Xs(n,t,r)}}function qs(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qs(e))Js(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,o=l(s,n);if(a.hasEagerState=!0,a.eagerState=o,or(o,s)){var i=t.interleaved;return null===i?(a.next=a,Pl(t)):(a.next=i.next,i.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=Tl(e,t,a,r))&&(nc(n,e,r,a=ec()),Xs(n,t,r))}}function Qs(e){var t=e.alternate;return e===ss||null!==t&&t===ss}function Js(e,t){us=cs=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xs(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Ys={readContext:Ol,useCallback:ps,useContext:ps,useEffect:ps,useImperativeHandle:ps,useInsertionEffect:ps,useLayoutEffect:ps,useMemo:ps,useReducer:ps,useRef:ps,useState:ps,useDebugValue:ps,useDeferredValue:ps,useTransition:ps,useMutableSource:ps,useSyncExternalStore:ps,useId:ps,unstable_isNewReconciler:!1},Gs={readContext:Ol,useCallback:function(e,t){return ys().memoizedState=[e,void 0===t?null:t],e},useContext:Ol,useEffect:Ms,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ps(4194308,4,Fs.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ps(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ps(4,2,e,t)},useMemo:function(e,t){var n=ys();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ys();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=$s.bind(null,ss,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ys().memoizedState=e},useState:Rs,useDebugValue:Us,useDeferredValue:function(e){return ys().memoizedState=e},useTransition:function(){var e=Rs(!1),t=e[0];return e=Ks.bind(null,e[1]),ys().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ss,a=ys();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Ri)throw Error(l(349));0!==(30&ls)||Ns(r,t,n)}a.memoizedState=n;var s={value:n,getSnapshot:t};return a.queue=s,Ms(_s.bind(null,r,s,e),[e]),r.flags|=2048,Os(9,Ss.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ys(),t=Ri.identifierPrefix;if(al){var n=Ya;t=":"+t+"R"+(n=(Xa&~(1<<32-st(Xa)-1)).toString(32)+n),0<(n=ds++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fs++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zs={readContext:Ol,useCallback:Bs,useContext:Ol,useEffect:As,useImperativeHandle:Is,useInsertionEffect:Ds,useLayoutEffect:zs,useMemo:Vs,useReducer:xs,useRef:Ls,useState:function(){return xs(bs)},useDebugValue:Us,useDeferredValue:function(e){return Ws(vs(),os.memoizedState,e)},useTransition:function(){return[xs(bs)[0],vs().memoizedState]},useMutableSource:ws,useSyncExternalStore:js,useId:Hs,unstable_isNewReconciler:!1},eo={readContext:Ol,useCallback:Bs,useContext:Ol,useEffect:As,useImperativeHandle:Is,useInsertionEffect:Ds,useLayoutEffect:zs,useMemo:Vs,useReducer:ks,useRef:Ls,useState:function(){return ks(bs)},useDebugValue:Us,useDeferredValue:function(e){var t=vs();return null===os?t.memoizedState=e:Ws(t,os.memoizedState,e)},useTransition:function(){return[ks(bs)[0],vs().memoizedState]},useMutableSource:ws,useSyncExternalStore:js,useId:Hs,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var n in t=z({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function no(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:z({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ro={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Fl(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Il(e,l,a))&&(nc(t,e,a,r),Ul(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Fl(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Il(e,l,a))&&(nc(t,e,a,r),Ul(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Fl(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Il(e,a,r))&&(nc(t,e,r,n),Ul(t,e,r))}};function ao(e,t,n,r,a,l,s){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!ir(n,r)||!ir(a,l))}function lo(e,t,n){var r=!1,a=Ca,l=t.contextType;return"object"===typeof l&&null!==l?l=Ol(l):(a=Pa(t)?Oa:Ea.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?La(e,a):Ca),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ro,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function so(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ro.enqueueReplaceState(t,t.state,null)}function oo(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Dl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Ol(l):(l=Pa(t)?Oa:Ea.current,a.context=La(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(no(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ro.enqueueReplaceState(a,a.state,null),Vl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function io(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fo="function"===typeof WeakMap?WeakMap:Map;function po(e,t,n){(n=Fl(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ki||(Ki=!0,Hi=r),uo(0,t)},n}function ho(e,t,n){(n=Fl(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){uo(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){uo(0,t),"function"!==typeof r&&(null===$i?$i=new Set([this]):$i.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mo(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fo;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Sc.bind(null,e,t,n),t.then(e,e))}function go(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yo(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fl(-1,1)).tag=2,Il(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vo=x.ReactCurrentOwner,bo=!1;function xo(e,t,n,r){t.child=null===e?kl(t,null,n,r):xl(t,e.child,n,r)}function ko(e,t,n,r,a){n=n.render;var l=t.ref;return Rl(t,a),r=ms(e,t,n,r,l,a),n=gs(),null===e||bo?(al&&n&&el(t),t.flags|=1,xo(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ko(e,t,a))}function wo(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Pc(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Mc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,jo(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:ir)(s,r)&&e.ref===t.ref)return Ko(e,t,a)}return t.flags|=1,(e=Tc(l,r)).ref=t.ref,e.return=t,t.child=e}function jo(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(ir(l,r)&&e.ref===t.ref){if(bo=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Ko(e,t,a);0!==(131072&e.flags)&&(bo=!0)}}return _o(e,t,n,r,a)}function No(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_a(Ti,Pi),Pi|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_a(Ti,Pi),Pi|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,_a(Ti,Pi),Pi|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,_a(Ti,Pi),Pi|=r;return xo(e,t,a,n),t.child}function So(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _o(e,t,n,r,a){var l=Pa(n)?Oa:Ea.current;return l=La(t,l),Rl(t,a),n=ms(e,t,n,r,l,a),r=gs(),null===e||bo?(al&&r&&el(t),t.flags|=1,xo(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ko(e,t,a))}function Co(e,t,n,r,a){if(Pa(n)){var l=!0;Da(t)}else l=!1;if(Rl(t,a),null===t.stateNode)Wo(e,t),lo(t,n,r),oo(t,n,r,a),r=!0;else if(null===e){var s=t.stateNode,o=t.memoizedProps;s.props=o;var i=s.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ol(c):c=La(t,c=Pa(n)?Oa:Ea.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof s.getSnapshotBeforeUpdate;d||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(o!==r||i!==c)&&so(t,s,r,c),Al=!1;var f=t.memoizedState;s.state=f,Vl(t,r,s,a),i=t.memoizedState,o!==r||f!==i||Ra.current||Al?("function"===typeof u&&(no(t,n,u,r),i=t.memoizedState),(o=Al||ao(t,n,o,r,f,i,c))?(d||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(t.flags|=4194308)):("function"===typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=i),s.props=r,s.state=i,s.context=c,r=o):("function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,zl(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:to(t.type,o),s.props=c,d=t.pendingProps,f=s.context,"object"===typeof(i=n.contextType)&&null!==i?i=Ol(i):i=La(t,i=Pa(n)?Oa:Ea.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(o!==d||f!==i)&&so(t,s,r,i),Al=!1,f=t.memoizedState,s.state=f,Vl(t,r,s,a);var h=t.memoizedState;o!==d||f!==h||Ra.current||Al?("function"===typeof p&&(no(t,n,p,r),h=t.memoizedState),(c=Al||ao(t,n,c,r,f,h,i)||!1)?(u||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,h,i),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,h,i)),"function"===typeof s.componentDidUpdate&&(t.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),s.props=r,s.state=h,s.context=i,r=c):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Eo(e,t,n,r,l,a)}function Eo(e,t,n,r,a,l){So(e,t);var s=0!==(128&t.flags);if(!r&&!s)return a&&za(t,n,!1),Ko(e,t,l);r=t.stateNode,vo.current=t;var o=s&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&s?(t.child=xl(t,e.child,null,l),t.child=xl(t,null,o,l)):xo(e,t,o,l),t.memoizedState=r.state,a&&za(t,n,!0),t.child}function Ro(e){var t=e.stateNode;t.pendingContext?Ma(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ma(0,t.context,!1),Jl(e,t.containerInfo)}function Oo(e,t,n,r,a){return pl(),hl(a),t.flags|=256,xo(e,t,n,r),t.child}var Lo,Po,To,Mo,Ao={dehydrated:null,treeContext:null,retryLane:0};function Do(e){return{baseLanes:e,cachePool:null,transitions:null}}function zo(e,t,n){var r,a=t.pendingProps,s=Zl.current,o=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&s)),r?(o=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(s|=1),_a(Zl,1&s),null===e)return cl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(i=a.children,e=a.fallback,o?(a=t.mode,o=t.child,i={mode:"hidden",children:i},0===(1&a)&&null!==o?(o.childLanes=0,o.pendingProps=i):o=Dc(i,a,0,null),e=Ac(e,a,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Do(n),t.memoizedState=Ao,e):Fo(t,i));if(null!==(s=e.memoizedState)&&null!==(r=s.dehydrated))return function(e,t,n,r,a,s,o){if(n)return 256&t.flags?(t.flags&=-257,Io(e,t,o,r=co(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(s=r.fallback,a=t.mode,r=Dc({mode:"visible",children:r.children},a,0,null),(s=Ac(s,a,o,null)).flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,0!==(1&t.mode)&&xl(t,e.child,null,o),t.child.memoizedState=Do(o),t.memoizedState=Ao,s);if(0===(1&t.mode))return Io(e,t,o,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var i=r.dgst;return r=i,Io(e,t,o,r=co(s=Error(l(419)),r,void 0))}if(i=0!==(o&e.childLanes),bo||i){if(null!==(r=Ri)){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|o))?0:a)&&a!==s.retryLane&&(s.retryLane=a,Ml(e,a),nc(r,e,a,-1))}return mc(),Io(e,t,o,r=co(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=s.treeContext,rl=ca(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(qa[Qa++]=Xa,qa[Qa++]=Ya,qa[Qa++]=Ja,Xa=e.id,Ya=e.overflow,Ja=t),t=Fo(t,r.children),t.flags|=4096,t)}(e,t,i,a,r,s,n);if(o){o=a.fallback,i=t.mode,r=(s=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&i)&&t.child!==s?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Tc(s,c)).subtreeFlags=14680064&s.subtreeFlags,null!==r?o=Tc(r,o):(o=Ac(o,i,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,i=null===(i=e.child.memoizedState)?Do(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Ao,a}return e=(o=e.child).sibling,a=Tc(o,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Fo(e,t){return(t=Dc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Io(e,t,n,r){return null!==r&&hl(r),xl(t,e.child,null,n),(e=Fo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Uo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),El(e.return,t,n)}function Bo(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Vo(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xo(e,t,r.children,n),0!==(2&(r=Zl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Uo(e,n,t);else if(19===e.tag)Uo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(_a(Zl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===es(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bo(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===es(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bo(t,!0,n,null,l);break;case"together":Bo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wo(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ko(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Di|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Tc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Tc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ho(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $o(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qo(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $o(t),null;case 1:case 17:return Pa(t.type)&&Ta(),$o(t),null;case 3:return r=t.stateNode,Xl(),Sa(Ra),Sa(Ea),ns(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(sc(ll),ll=null))),Po(e,t),$o(t),null;case 5:Gl(t);var a=Ql(ql.current);if(n=t.type,null!==e&&null!=t.stateNode)To(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return $o(t),null}if(e=Ql(Hl.current),dl(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[fa]=t,r[pa]=s,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(a=0;a<Ar.length;a++)Ir(Ar[a],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":X(r,s),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Ir("invalid",r);break;case"textarea":ae(r,s),Ir("invalid",r)}for(var i in ve(n,s),a=null,s)if(s.hasOwnProperty(i)){var c=s[i];"children"===i?"string"===typeof c?r.textContent!==c&&(!0!==s.suppressHydrationWarning&&Gr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==s.suppressHydrationWarning&&Gr(r.textContent,c,e),a=["children",""+c]):o.hasOwnProperty(i)&&null!=c&&"onScroll"===i&&Ir("scroll",r)}switch(n){case"input":$(r),Z(r,s,!0);break;case"textarea":$(r),se(r);break;case"select":case"option":break;default:"function"===typeof s.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{i=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=oe(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),"select"===n&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[fa]=t,e[pa]=r,Lo(e,t,!1,!1),t.stateNode=e;e:{switch(i=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),a=r;break;case"iframe":case"object":case"embed":Ir("load",e),a=r;break;case"video":case"audio":for(a=0;a<Ar.length;a++)Ir(Ar[a],e);a=r;break;case"source":Ir("error",e),a=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),a=r;break;case"details":Ir("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Ir("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=z({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ir("invalid",e)}for(s in ve(n,a),c=a)if(c.hasOwnProperty(s)){var u=c[s];"style"===s?ge(e,u):"dangerouslySetInnerHTML"===s?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===s?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(o.hasOwnProperty(s)?null!=u&&"onScroll"===s&&Ir("scroll",e):null!=u&&b(e,s,u,i))}switch(n){case"input":$(e),Z(e,r,!1);break;case"textarea":$(e),se(e);break;case"option":null!=r.value&&e.setAttribute("value",""+K(r.value));break;case"select":e.multiple=!!r.multiple,null!=(s=r.value)?ne(e,!!r.multiple,s,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return $o(t),null;case 6:if(e&&null!=t.stateNode)Mo(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Ql(ql.current),Ql(Hl.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(s=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}s&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return $o(t),null;case 13:if(Sa(Zl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),pl(),t.flags|=98560,s=!1;else if(s=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!s)throw Error(l(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(l(317));s[fa]=t}else pl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;$o(t),s=!1}else null!==ll&&(sc(ll),ll=null),s=!0;if(!s)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zl.current)?0===Mi&&(Mi=3):mc())),null!==t.updateQueue&&(t.flags|=4),$o(t),null);case 4:return Xl(),Po(e,t),null===e&&Vr(t.stateNode.containerInfo),$o(t),null;case 10:return Cl(t.type._context),$o(t),null;case 19:if(Sa(Zl),null===(s=t.memoizedState))return $o(t),null;if(r=0!==(128&t.flags),null===(i=s.rendering))if(r)Ho(s,!1);else{if(0!==Mi||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(i=es(e))){for(t.flags|=128,Ho(s,!1),null!==(r=i.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(s=n).flags&=14680066,null===(i=s.alternate)?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _a(Zl,1&Zl.current|2),t.child}e=e.sibling}null!==s.tail&&Ye()>Vi&&(t.flags|=128,r=!0,Ho(s,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=es(i))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ho(s,!0),null===s.tail&&"hidden"===s.tailMode&&!i.alternate&&!al)return $o(t),null}else 2*Ye()-s.renderingStartTime>Vi&&1073741824!==n&&(t.flags|=128,r=!0,Ho(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(null!==(n=s.last)?n.sibling=i:t.child=i,s.last=i)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Ye(),t.sibling=null,n=Zl.current,_a(Zl,r?1&n|2:1&n),t):($o(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Pi)&&($o(t),6&t.subtreeFlags&&(t.flags|=8192)):$o(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Qo(e,t){switch(tl(t),t.tag){case 1:return Pa(t.type)&&Ta(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xl(),Sa(Ra),Sa(Ea),ns(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Gl(t),null;case 13:if(Sa(Zl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Sa(Zl),null;case 4:return Xl(),null;case 10:return Cl(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Lo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Po=function(){},To=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Ql(Hl.current);var l,s=null;switch(n){case"input":a=J(e,a),r=J(e,r),s=[];break;case"select":a=z({},a,{value:void 0}),r=z({},r,{value:void 0}),s=[];break;case"textarea":a=re(e,a),r=re(e,r),s=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(u in ve(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var i=a[u];for(l in i)i.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(o.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(i=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==i&&(null!=c||null!=i))if("style"===u)if(i){for(l in i)!i.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in c)c.hasOwnProperty(l)&&i[l]!==c[l]&&(n||(n={}),n[l]=c[l])}else n||(s||(s=[]),s.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,i=i?i.__html:void 0,null!=c&&i!==c&&(s=s||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(s=s||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(o.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ir("scroll",e),s||i===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}},Mo=function(e,t,n,r){n!==r&&(t.flags|=4)};var Jo=!1,Xo=!1,Yo="function"===typeof WeakSet?WeakSet:Set,Go=null;function Zo(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Nc(e,t,r)}else n.current=null}function ei(e,t,n){try{n()}catch(r){Nc(e,t,r)}}var ti=!1;function ni(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&ei(t,n,l)}a=a.next}while(a!==r)}}function ri(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ai(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function li(e){var t=e.alternate;null!==t&&(e.alternate=null,li(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function si(e){return 5===e.tag||3===e.tag||4===e.tag}function oi(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||si(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ii(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ii(e,t,n),e=e.sibling;null!==e;)ii(e,t,n),e=e.sibling}function ci(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ci(e,t,n),e=e.sibling;null!==e;)ci(e,t,n),e=e.sibling}var ui=null,di=!1;function fi(e,t,n){for(n=n.child;null!==n;)pi(e,t,n),n=n.sibling}function pi(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(o){}switch(n.tag){case 5:Xo||Zo(n,t);case 6:var r=ui,a=di;ui=null,fi(e,t,n),di=a,null!==(ui=r)&&(di?(e=ui,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ui.removeChild(n.stateNode));break;case 18:null!==ui&&(di?(e=ui,n=n.stateNode,8===e.nodeType?ia(e.parentNode,n):1===e.nodeType&&ia(e,n),Vt(e)):ia(ui,n.stateNode));break;case 4:r=ui,a=di,ui=n.stateNode.containerInfo,di=!0,fi(e,t,n),ui=r,di=a;break;case 0:case 11:case 14:case 15:if(!Xo&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,s=l.destroy;l=l.tag,void 0!==s&&(0!==(2&l)||0!==(4&l))&&ei(n,t,s),a=a.next}while(a!==r)}fi(e,t,n);break;case 1:if(!Xo&&(Zo(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){Nc(n,t,o)}fi(e,t,n);break;case 21:fi(e,t,n);break;case 22:1&n.mode?(Xo=(r=Xo)||null!==n.memoizedState,fi(e,t,n),Xo=r):fi(e,t,n);break;default:fi(e,t,n)}}function hi(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yo),t.forEach(function(t){var r=Ec.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function mi(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var s=e,o=t,i=o;e:for(;null!==i;){switch(i.tag){case 5:ui=i.stateNode,di=!1;break e;case 3:case 4:ui=i.stateNode.containerInfo,di=!0;break e}i=i.return}if(null===ui)throw Error(l(160));pi(s,o,a),ui=null,di=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){Nc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gi(t,e),t=t.sibling}function gi(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mi(t,e),yi(e),4&r){try{ni(3,e,e.return),ri(3,e)}catch(g){Nc(e,e.return,g)}try{ni(5,e,e.return)}catch(g){Nc(e,e.return,g)}}break;case 1:mi(t,e),yi(e),512&r&&null!==n&&Zo(n,n.return);break;case 5:if(mi(t,e),yi(e),512&r&&null!==n&&Zo(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Nc(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var s=e.memoizedProps,o=null!==n?n.memoizedProps:s,i=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===i&&"radio"===s.type&&null!=s.name&&Y(a,s),be(i,o);var u=be(i,s);for(o=0;o<c.length;o+=2){var d=c[o],f=c[o+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,u)}switch(i){case"input":G(a,s);break;case"textarea":le(a,s);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!s.multiple;var h=s.value;null!=h?ne(a,!!s.multiple,h,!1):p!==!!s.multiple&&(null!=s.defaultValue?ne(a,!!s.multiple,s.defaultValue,!0):ne(a,!!s.multiple,s.multiple?[]:"",!1))}a[pa]=s}catch(g){Nc(e,e.return,g)}}break;case 6:if(mi(t,e),yi(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,s=e.memoizedProps;try{a.nodeValue=s}catch(g){Nc(e,e.return,g)}}break;case 3:if(mi(t,e),yi(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(g){Nc(e,e.return,g)}break;case 4:default:mi(t,e),yi(e);break;case 13:mi(t,e),yi(e),8192&(a=e.child).flags&&(s=null!==a.memoizedState,a.stateNode.isHidden=s,!s||null!==a.alternate&&null!==a.alternate.memoizedState||(Bi=Ye())),4&r&&hi(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xo=(u=Xo)||d,mi(t,e),Xo=u):mi(t,e),yi(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Go=e,d=e.child;null!==d;){for(f=Go=d;null!==Go;){switch(h=(p=Go).child,p.tag){case 0:case 11:case 14:case 15:ni(4,p,p.return);break;case 1:Zo(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Nc(r,n,g)}}break;case 5:Zo(p,p.return);break;case 22:if(null!==p.memoizedState){ki(f);continue}}null!==h?(h.return=p,Go=h):ki(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(s=a.style).setProperty?s.setProperty("display","none","important"):s.display="none":(i=f.stateNode,o=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,i.style.display=me("display",o))}catch(g){Nc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){Nc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:mi(t,e),yi(e),4&r&&hi(e);case 21:}}function yi(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(si(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ci(e,oi(e),a);break;case 3:case 4:var s=r.stateNode.containerInfo;ii(e,oi(e),s);break;default:throw Error(l(161))}}catch(o){Nc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vi(e,t,n){Go=e,bi(e,t,n)}function bi(e,t,n){for(var r=0!==(1&e.mode);null!==Go;){var a=Go,l=a.child;if(22===a.tag&&r){var s=null!==a.memoizedState||Jo;if(!s){var o=a.alternate,i=null!==o&&null!==o.memoizedState||Xo;o=Jo;var c=Xo;if(Jo=s,(Xo=i)&&!c)for(Go=a;null!==Go;)i=(s=Go).child,22===s.tag&&null!==s.memoizedState?wi(a):null!==i?(i.return=s,Go=i):wi(a);for(;null!==l;)Go=l,bi(l,t,n),l=l.sibling;Go=a,Jo=o,Xo=c}xi(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Go=l):xi(e)}}function xi(e){for(;null!==Go;){var t=Go;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xo||ri(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xo)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:to(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;null!==s&&Wl(t,s,r);break;case 3:var o=t.updateQueue;if(null!==o){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wl(t,o,n)}break;case 5:var i=t.stateNode;if(null===n&&4&t.flags){n=i;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(l(163))}Xo||512&t.flags&&ai(t)}catch(p){Nc(t,t.return,p)}}if(t===e){Go=null;break}if(null!==(n=t.sibling)){n.return=t.return,Go=n;break}Go=t.return}}function ki(e){for(;null!==Go;){var t=Go;if(t===e){Go=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Go=n;break}Go=t.return}}function wi(e){for(;null!==Go;){var t=Go;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ri(4,t)}catch(i){Nc(t,n,i)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(i){Nc(t,a,i)}}var l=t.return;try{ai(t)}catch(i){Nc(t,l,i)}break;case 5:var s=t.return;try{ai(t)}catch(i){Nc(t,s,i)}}}catch(i){Nc(t,t.return,i)}if(t===e){Go=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Go=o;break}Go=t.return}}var ji,Ni=Math.ceil,Si=x.ReactCurrentDispatcher,_i=x.ReactCurrentOwner,Ci=x.ReactCurrentBatchConfig,Ei=0,Ri=null,Oi=null,Li=0,Pi=0,Ti=Na(0),Mi=0,Ai=null,Di=0,zi=0,Fi=0,Ii=null,Ui=null,Bi=0,Vi=1/0,Wi=null,Ki=!1,Hi=null,$i=null,qi=!1,Qi=null,Ji=0,Xi=0,Yi=null,Gi=-1,Zi=0;function ec(){return 0!==(6&Ei)?Ye():-1!==Gi?Gi:Gi=Ye()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Ei)&&0!==Li?Li&-Li:null!==ml.transition?(0===Zi&&(Zi=mt()),Zi):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nc(e,t,n,r){if(50<Xi)throw Xi=0,Yi=null,Error(l(185));yt(e,n,r),0!==(2&Ei)&&e===Ri||(e===Ri&&(0===(2&Ei)&&(zi|=n),4===Mi&&oc(e,Li)),rc(e,r),1===n&&0===Ei&&0===(1&t.mode)&&(Vi=Ye()+500,Ia&&Va()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-st(l),o=1<<s,i=a[s];-1===i?0!==(o&n)&&0===(o&r)||(a[s]=pt(o,t)):i<=t&&(e.expiredLanes|=o),l&=~o}}(e,t);var r=ft(e,e===Ri?Li:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Ia=!0,Ba(e)}(ic.bind(null,e)):Ba(ic.bind(null,e)),sa(function(){0===(6&Ei)&&Va()}),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Rc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Gi=-1,Zi=0,0!==(6&Ei))throw Error(l(327));var n=e.callbackNode;if(wc()&&e.callbackNode!==n)return null;var r=ft(e,e===Ri?Li:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var a=Ei;Ei|=2;var s=hc();for(Ri===e&&Li===t||(Wi=null,Vi=Ye()+500,fc(e,t));;)try{vc();break}catch(i){pc(e,i)}_l(),Si.current=s,Ei=a,null!==Oi?t=0:(Ri=null,Li=0,t=Mi)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=lc(e,a))),1===t)throw n=Ai,fc(e,0),oc(e,r),rc(e,Ye()),n;if(6===t)oc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!or(l(),a))return!1}catch(o){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gc(e,r))&&(0!==(s=ht(e))&&(r=s,t=lc(e,s))),1===t))throw n=Ai,fc(e,0),oc(e,r),rc(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:kc(e,Ui,Wi);break;case 3:if(oc(e,r),(130023424&r)===r&&10<(t=Bi+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(kc.bind(null,e,Ui,Wi),t);break}kc(e,Ui,Wi);break;case 4:if(oc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var o=31-st(r);s=1<<o,(o=t[o])>a&&(a=o),r&=~s}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ni(r/1960))-r)){e.timeoutHandle=ra(kc.bind(null,e,Ui,Wi),r);break}kc(e,Ui,Wi);break;default:throw Error(l(329))}}}return rc(e,Ye()),e.callbackNode===n?ac.bind(null,e):null}function lc(e,t){var n=Ii;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Ui,Ui=n,null!==t&&sc(t)),e}function sc(e){null===Ui?Ui=e:Ui.push.apply(Ui,e)}function oc(e,t){for(t&=~Fi,t&=~zi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function ic(e){if(0!==(6&Ei))throw Error(l(327));wc();var t=ft(e,0);if(0===(1&t))return rc(e,Ye()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=lc(e,r))}if(1===n)throw n=Ai,fc(e,0),oc(e,t),rc(e,Ye()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,kc(e,Ui,Wi),rc(e,Ye()),null}function cc(e,t){var n=Ei;Ei|=1;try{return e(t)}finally{0===(Ei=n)&&(Vi=Ye()+500,Ia&&Va())}}function uc(e){null!==Qi&&0===Qi.tag&&0===(6&Ei)&&wc();var t=Ei;Ei|=1;var n=Ci.transition,r=bt;try{if(Ci.transition=null,bt=1,e)return e()}finally{bt=r,Ci.transition=n,0===(6&(Ei=t))&&Va()}}function dc(){Pi=Ti.current,Sa(Ti)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Oi)for(n=Oi.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ta();break;case 3:Xl(),Sa(Ra),Sa(Ea),ns();break;case 5:Gl(r);break;case 4:Xl();break;case 13:case 19:Sa(Zl);break;case 10:Cl(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Ri=e,Oi=e=Tc(e.current,null),Li=Pi=t,Mi=0,Ai=null,Fi=zi=Di=0,Ui=Ii=null,null!==Ll){for(t=0;t<Ll.length;t++)if(null!==(r=(n=Ll[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var s=l.next;l.next=a,r.next=s}n.pending=r}Ll=null}return e}function pc(e,t){for(;;){var n=Oi;try{if(_l(),rs.current=Ys,cs){for(var r=ss.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cs=!1}if(ls=0,is=os=ss=null,us=!1,ds=0,_i.current=null,null===n||null===n.return){Mi=1,Ai=t,Oi=null;break}e:{var s=e,o=n.return,i=n,c=t;if(t=Li,i.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=i,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=go(o);if(null!==h){h.flags&=-257,yo(h,o,i,0,t),1&h.mode&&mo(s,u,t),c=u;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){mo(s,u,t),mc();break e}c=Error(l(426))}else if(al&&1&i.mode){var y=go(o);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),yo(y,o,i,0,t),hl(io(c,i));break e}}s=c=io(c,i),4!==Mi&&(Mi=2),null===Ii?Ii=[s]:Ii.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t,Bl(s,po(0,c,t));break e;case 1:i=c;var v=s.type,b=s.stateNode;if(0===(128&s.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===$i||!$i.has(b)))){s.flags|=65536,t&=-t,s.lanes|=t,Bl(s,ho(s,i,t));break e}}s=s.return}while(null!==s)}xc(n)}catch(x){t=x,Oi===n&&null!==n&&(Oi=n=n.return);continue}break}}function hc(){var e=Si.current;return Si.current=Ys,null===e?Ys:e}function mc(){0!==Mi&&3!==Mi&&2!==Mi||(Mi=4),null===Ri||0===(268435455&Di)&&0===(268435455&zi)||oc(Ri,Li)}function gc(e,t){var n=Ei;Ei|=2;var r=hc();for(Ri===e&&Li===t||(Wi=null,fc(e,t));;)try{yc();break}catch(a){pc(e,a)}if(_l(),Ei=n,Si.current=r,null!==Oi)throw Error(l(261));return Ri=null,Li=0,Mi}function yc(){for(;null!==Oi;)bc(Oi)}function vc(){for(;null!==Oi&&!Je();)bc(Oi)}function bc(e){var t=ji(e.alternate,e,Pi);e.memoizedProps=e.pendingProps,null===t?xc(e):Oi=t,_i.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=qo(n,t,Pi)))return void(Oi=n)}else{if(null!==(n=Qo(n,t)))return n.flags&=32767,void(Oi=n);if(null===e)return Mi=6,void(Oi=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Oi=t);Oi=t=e}while(null!==t);0===Mi&&(Mi=5)}function kc(e,t,n){var r=bt,a=Ci.transition;try{Ci.transition=null,bt=1,function(e,t,n,r){do{wc()}while(null!==Qi);if(0!==(6&Ei))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-st(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,s),e===Ri&&(Oi=Ri=null,Li=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||qi||(qi=!0,Rc(tt,function(){return wc(),null})),s=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||s){s=Ci.transition,Ci.transition=null;var o=bt;bt=1;var i=Ei;Ei|=4,_i.current=null,function(e,t){if(ea=Kt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(k){n=null;break e}var o=0,i=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(i=o+a),f!==s||0!==r&&3!==f.nodeType||(c=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++u===a&&(i=o),p===s&&++d===r&&(c=o),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===i||-1===c?null:{start:i,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Kt=!1,Go=t;null!==Go;)if(e=(t=Go).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Go=e;else for(;null!==Go;){t=Go;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:to(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(k){Nc(t,t.return,k)}if(null!==(e=t.sibling)){e.return=t.return,Go=e;break}Go=t.return}m=ti,ti=!1}(e,n),gi(n,e),hr(ta),Kt=!!ea,ta=ea=null,e.current=n,vi(n,e,a),Xe(),Ei=i,bt=o,Ci.transition=s}else e.current=n;if(qi&&(qi=!1,Qi=e,Ji=a),s=e.pendingLanes,0===s&&($i=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Ki)throw Ki=!1,e=Hi,Hi=null,e;0!==(1&Ji)&&0!==e.tag&&wc(),s=e.pendingLanes,0!==(1&s)?e===Yi?Xi++:(Xi=0,Yi=e):Xi=0,Va()}(e,t,n,r)}finally{Ci.transition=a,bt=r}return null}function wc(){if(null!==Qi){var e=xt(Ji),t=Ci.transition,n=bt;try{if(Ci.transition=null,bt=16>e?16:e,null===Qi)var r=!1;else{if(e=Qi,Qi=null,Ji=0,0!==(6&Ei))throw Error(l(331));var a=Ei;for(Ei|=4,Go=e.current;null!==Go;){var s=Go,o=s.child;if(0!==(16&Go.flags)){var i=s.deletions;if(null!==i){for(var c=0;c<i.length;c++){var u=i[c];for(Go=u;null!==Go;){var d=Go;switch(d.tag){case 0:case 11:case 15:ni(8,d,s)}var f=d.child;if(null!==f)f.return=d,Go=f;else for(;null!==Go;){var p=(d=Go).sibling,h=d.return;if(li(d),d===u){Go=null;break}if(null!==p){p.return=h,Go=p;break}Go=h}}}var m=s.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Go=s}}if(0!==(2064&s.subtreeFlags)&&null!==o)o.return=s,Go=o;else e:for(;null!==Go;){if(0!==(2048&(s=Go).flags))switch(s.tag){case 0:case 11:case 15:ni(9,s,s.return)}var v=s.sibling;if(null!==v){v.return=s.return,Go=v;break e}Go=s.return}}var b=e.current;for(Go=b;null!==Go;){var x=(o=Go).child;if(0!==(2064&o.subtreeFlags)&&null!==x)x.return=o,Go=x;else e:for(o=b;null!==Go;){if(0!==(2048&(i=Go).flags))try{switch(i.tag){case 0:case 11:case 15:ri(9,i)}}catch(w){Nc(i,i.return,w)}if(i===o){Go=null;break e}var k=i.sibling;if(null!==k){k.return=i.return,Go=k;break e}Go=i.return}}if(Ei=a,Va(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(w){}r=!0}return r}finally{bt=n,Ci.transition=t}}return!1}function jc(e,t,n){e=Il(e,t=po(0,t=io(n,t),1),1),t=ec(),null!==e&&(yt(e,1,t),rc(e,t))}function Nc(e,t,n){if(3===e.tag)jc(e,e,n);else for(;null!==t;){if(3===t.tag){jc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===$i||!$i.has(r))){t=Il(t,e=ho(t,e=io(n,e),1),1),e=ec(),null!==t&&(yt(t,1,e),rc(t,e));break}}t=t.return}}function Sc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Ri===e&&(Li&n)===n&&(4===Mi||3===Mi&&(130023424&Li)===Li&&500>Ye()-Bi?fc(e,0):Fi|=n),rc(e,t)}function _c(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Ml(e,t))&&(yt(e,t,n),rc(e,n))}function Cc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_c(e,n)}function Ec(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),_c(e,n)}function Rc(e,t){return qe(e,t)}function Oc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lc(e,t,n,r){return new Oc(e,t,n,r)}function Pc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Tc(e,t){var n=e.alternate;return null===n?((n=Lc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Mc(e,t,n,r,a,s){var o=2;if(r=e,"function"===typeof e)Pc(e)&&(o=1);else if("string"===typeof e)o=5;else e:switch(e){case j:return Ac(n.children,a,s,t);case N:o=8,a|=8;break;case S:return(e=Lc(12,n,t,2|a)).elementType=S,e.lanes=s,e;case R:return(e=Lc(13,n,t,a)).elementType=R,e.lanes=s,e;case O:return(e=Lc(19,n,t,a)).elementType=O,e.lanes=s,e;case T:return Dc(n,a,s,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case _:o=10;break e;case C:o=9;break e;case E:o=11;break e;case L:o=14;break e;case P:o=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Lc(o,n,t,a)).elementType=e,t.type=r,t.lanes=s,t}function Ac(e,t,n,r){return(e=Lc(7,e,r,t)).lanes=n,e}function Dc(e,t,n,r){return(e=Lc(22,e,r,t)).elementType=T,e.lanes=n,e.stateNode={isHidden:!1},e}function zc(e,t,n){return(e=Lc(6,e,null,t)).lanes=n,e}function Fc(e,t,n){return(t=Lc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ic(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Uc(e,t,n,r,a,l,s,o,i){return e=new Ic(e,t,n,o,i),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Lc(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Dl(l),e}function Bc(e){if(!e)return Ca;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Pa(n))return Aa(e,n,t)}return t}function Vc(e,t,n,r,a,l,s,o,i){return(e=Uc(n,r,!0,e,0,l,0,o,i)).context=Bc(null),n=e.current,(l=Fl(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Il(n,l,a),e.current.lanes=a,yt(e,a,r),rc(e,r),e}function Wc(e,t,n,r){var a=t.current,l=ec(),s=tc(a);return n=Bc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fl(l,s)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Il(a,t,s))&&(nc(e,a,s,l),Ul(e,a,s)),s}function Kc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function $c(e,t){Hc(e,t),(e=e.alternate)&&Hc(e,t)}ji=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ra.current)bo=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bo=!1,function(e,t,n){switch(t.tag){case 3:Ro(t),pl();break;case 5:Yl(t);break;case 1:Pa(t.type)&&Da(t);break;case 4:Jl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;_a(wl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(_a(Zl,1&Zl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?zo(e,t,n):(_a(Zl,1&Zl.current),null!==(e=Ko(e,t,n))?e.sibling:null);_a(Zl,1&Zl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Vo(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),_a(Zl,Zl.current),r)break;return null;case 22:case 23:return t.lanes=0,No(e,t,n)}return Ko(e,t,n)}(e,t,n);bo=0!==(131072&e.flags)}else bo=!1,al&&0!==(1048576&t.flags)&&Za(t,$a,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wo(e,t),e=t.pendingProps;var a=La(t,Ea.current);Rl(t,n),a=ms(null,t,r,e,a,n);var s=gs();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pa(r)?(s=!0,Da(t)):s=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Dl(t),a.updater=ro,t.stateNode=a,a._reactInternals=t,oo(t,r,e,n),t=Eo(null,t,r,!0,s,n)):(t.tag=0,al&&s&&el(t),xo(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wo(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Pc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===E)return 11;if(e===L)return 14}return 2}(r),e=to(r,e),a){case 0:t=_o(null,t,r,e,n);break e;case 1:t=Co(null,t,r,e,n);break e;case 11:t=ko(null,t,r,e,n);break e;case 14:t=wo(null,t,r,to(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,_o(e,t,r,a=t.elementType===r?a:to(r,a),n);case 1:return r=t.type,a=t.pendingProps,Co(e,t,r,a=t.elementType===r?a:to(r,a),n);case 3:e:{if(Ro(t),null===e)throw Error(l(387));r=t.pendingProps,a=(s=t.memoizedState).element,zl(e,t),Vl(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Oo(e,t,r,n,a=io(Error(l(423)),t));break e}if(r!==a){t=Oo(e,t,r,n,a=io(Error(l(424)),t));break e}for(rl=ca(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=kl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=Ko(e,t,n);break e}xo(e,t,r,n)}t=t.child}return t;case 5:return Yl(t),null===e&&cl(t),r=t.type,a=t.pendingProps,s=null!==e?e.memoizedProps:null,o=a.children,na(r,a)?o=null:null!==s&&na(r,s)&&(t.flags|=32),So(e,t),xo(e,t,o,n),t.child;case 6:return null===e&&cl(t),null;case 13:return zo(e,t,n);case 4:return Jl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xl(t,null,r,n):xo(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ko(e,t,r,a=t.elementType===r?a:to(r,a),n);case 7:return xo(e,t,t.pendingProps,n),t.child;case 8:case 12:return xo(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,s=t.memoizedProps,o=a.value,_a(wl,r._currentValue),r._currentValue=o,null!==s)if(or(s.value,o)){if(s.children===a.children&&!Ra.current){t=Ko(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var i=s.dependencies;if(null!==i){o=s.child;for(var c=i.firstContext;null!==c;){if(c.context===r){if(1===s.tag){(c=Fl(-1,n&-n)).tag=2;var u=s.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}s.lanes|=n,null!==(c=s.alternate)&&(c.lanes|=n),El(s.return,n,t),i.lanes|=n;break}c=c.next}}else if(10===s.tag)o=s.type===t.type?null:s.child;else if(18===s.tag){if(null===(o=s.return))throw Error(l(341));o.lanes|=n,null!==(i=o.alternate)&&(i.lanes|=n),El(o,n,t),o=s.sibling}else o=s.child;if(null!==o)o.return=s;else for(o=s;null!==o;){if(o===t){o=null;break}if(null!==(s=o.sibling)){s.return=o.return,o=s;break}o=o.return}s=o}xo(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Rl(t,n),r=r(a=Ol(a)),t.flags|=1,xo(e,t,r,n),t.child;case 14:return a=to(r=t.type,t.pendingProps),wo(e,t,r,a=to(r.type,a),n);case 15:return jo(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:to(r,a),Wo(e,t),t.tag=1,Pa(r)?(e=!0,Da(t)):e=!1,Rl(t,n),lo(t,r,a),oo(t,r,a,n),Eo(null,t,r,!0,e,n);case 19:return Vo(e,t,n);case 22:return No(e,t,n)}throw Error(l(156,t.tag))};var qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Qc(e){this._internalRoot=e}function Jc(e){this._internalRoot=e}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gc(){}function Zc(e,t,n,r,a){var l=n._reactRootContainer;if(l){var s=l;if("function"===typeof a){var o=a;a=function(){var e=Kc(s);o.call(e)}}Wc(t,s,e,a)}else s=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=Kc(s);l.call(e)}}var s=Vc(t,r,e,0,null,!1,0,"",Gc);return e._reactRootContainer=s,e[ha]=s.current,Vr(8===e.nodeType?e.parentNode:e),uc(),s}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var o=r;r=function(){var e=Kc(i);o.call(e)}}var i=Uc(e,0,!1,null,0,!1,0,"",Gc);return e._reactRootContainer=i,e[ha]=i.current,Vr(8===e.nodeType?e.parentNode:e),uc(function(){Wc(t,i,n,r)}),i}(n,t,e,a,r);return Kc(s)}Jc.prototype.render=Qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Wc(e,t,null,null)},Jc.prototype.unmount=Qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Wc(null,e,null,null)}),t[ha]=null}},Jc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&0!==t&&t<Tt[n].priority;n++);Tt.splice(n,0,e),0===n&&zt(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),rc(t,Ye()),0===(6&Ei)&&(Vi=Ye()+500,Va()))}break;case 13:uc(function(){var t=Ml(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),$c(e,1)}},wt=function(e){if(13===e.tag){var t=Ml(e,134217728);if(null!==t)nc(t,e,134217728,ec());$c(e,134217728)}},jt=function(e){if(13===e.tag){var t=tc(e),n=Ml(e,t);if(null!==n)nc(n,e,t,ec());$c(e,t)}},Nt=function(){return bt},St=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},we=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ka(r);if(!a)throw Error(l(90));q(r),G(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ee=cc,Re=uc;var eu={usingClientEntryPoint:!1,Events:[ba,xa,ka,_e,Ce,cc]},tu={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),lt=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xc(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:w,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xc(e))throw Error(l(299));var n=!1,r="",a=qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Uc(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Vr(8===e.nodeType?e.parentNode:e),new Qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Yc(t))throw Error(l(200));return Zc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xc(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,s="",o=qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onRecoverableError&&(o=n.onRecoverableError)),t=Vc(t,null,e,1,null!=n?n:null,a,0,s,o),e[ha]=t.current,Vr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Jc(t)},t.render=function(e,t,n){if(!Yc(t))throw Error(l(200));return Zc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yc(e))throw Error(l(40));return!!e._reactRootContainer&&(uc(function(){Zc(null,null,e,!1,function(){e._reactRootContainer=null,e[ha]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yc(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Zc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>me,hasStandardBrowserEnv:()=>ye,hasStandardBrowserWebWorkerEnv:()=>ve,navigator:()=>ge,origin:()=>be});var a=n(43),l=n(391);function s(e,t){return function(){return e.apply(t,arguments)}}const{toString:o}=Object.prototype,{getPrototypeOf:i}=Object,{iterator:c,toStringTag:u}=Symbol,d=(f=Object.create(null),e=>{const t=o.call(e);return f[t]||(f[t]=t.slice(8,-1).toLowerCase())});var f;const p=e=>(e=e.toLowerCase(),t=>d(t)===e),h=e=>t=>typeof t===e,{isArray:m}=Array,g=h("undefined");const y=p("ArrayBuffer");const v=h("string"),b=h("function"),x=h("number"),k=e=>null!==e&&"object"===typeof e,w=e=>{if("object"!==d(e))return!1;const t=i(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(u in e)&&!(c in e)},j=p("Date"),N=p("File"),S=p("Blob"),_=p("FileList"),C=p("URLSearchParams"),[E,R,O,L]=["ReadableStream","Request","Response","Headers"].map(p);function P(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),m(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let s;for(n=0;n<l;n++)s=r[n],t.call(null,e[s],s,e)}}function T(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const M="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,A=e=>!g(e)&&e!==M;const D=(z="undefined"!==typeof Uint8Array&&i(Uint8Array),e=>z&&e instanceof z);var z;const F=p("HTMLFormElement"),I=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),U=p("RegExp"),B=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};P(n,(n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)}),Object.defineProperties(e,r)};const V=p("AsyncFunction"),W=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],M.addEventListener("message",e=>{let{source:t,data:a}=e;t===M&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),M.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,b(M.postMessage)),K="undefined"!==typeof queueMicrotask?queueMicrotask.bind(M):"undefined"!==typeof process&&process.nextTick||W,H={isArray:m,isArrayBuffer:y,isBuffer:function(e){return null!==e&&!g(e)&&null!==e.constructor&&!g(e.constructor)&&b(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||b(e.append)&&("formdata"===(t=d(e))||"object"===t&&b(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&y(e.buffer),t},isString:v,isNumber:x,isBoolean:e=>!0===e||!1===e,isObject:k,isPlainObject:w,isReadableStream:E,isRequest:R,isResponse:O,isHeaders:L,isUndefined:g,isDate:j,isFile:N,isBlob:S,isRegExp:U,isFunction:b,isStream:e=>k(e)&&b(e.pipe),isURLSearchParams:C,isTypedArray:D,isFileList:_,forEach:P,merge:function e(){const{caseless:t}=A(this)&&this||{},n={},r=(r,a)=>{const l=t&&T(n,a)||a;w(n[l])&&w(r)?n[l]=e(n[l],r):w(r)?n[l]=e({},r):m(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&P(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return P(t,(t,r)=>{n&&b(t)?e[r]=s(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,s;const o={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)s=a[l],r&&!r(s,e,t)||o[s]||(t[s]=e[s],o[s]=!0);e=!1!==n&&i(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:d,kindOfTest:p,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(m(e))return e;let t=e.length;if(!x(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[c]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:F,hasOwnProperty:I,hasOwnProp:I,reduceDescriptors:B,freezeMethods:e=>{B(e,(t,n)=>{if(b(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];b(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return m(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:T,global:M,isContextDefined:A,isSpecCompliantForm:function(e){return!!(e&&b(e.append)&&"FormData"===e[u]&&e[c])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(k(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=m(e)?[]:{};return P(e,(e,t)=>{const l=n(e,r+1);!g(l)&&(a[t]=l)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:V,isThenable:e=>e&&(k(e)||b(e))&&b(e.then)&&b(e.catch),setImmediate:W,asap:K,isIterable:e=>null!=e&&b(e[c])};function $(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}H.inherits($,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:H.toJSONObject(this.config),code:this.code,status:this.status}}});const q=$.prototype,Q={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Q[e]={value:e}}),Object.defineProperties($,Q),Object.defineProperty(q,"isAxiosError",{value:!0}),$.from=(e,t,n,r,a,l)=>{const s=Object.create(q);return H.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),$.call(s,e.message,t,n,r,a),s.cause=e,s.name=e.name,l&&Object.assign(s,l),s};const J=$;function X(e){return H.isPlainObject(e)||H.isArray(e)}function Y(e){return H.endsWith(e,"[]")?e.slice(0,-2):e}function G(e,t,n){return e?e.concat(t).map(function(e,t){return e=Y(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Z=H.toFlatObject(H,{},null,function(e){return/^is[A-Z]/.test(e)});const ee=function(e,t,n){if(!H.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=H.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!H.isUndefined(t[e])})).metaTokens,a=n.visitor||c,l=n.dots,s=n.indexes,o=(n.Blob||"undefined"!==typeof Blob&&Blob)&&H.isSpecCompliantForm(t);if(!H.isFunction(a))throw new TypeError("visitor must be a function");function i(e){if(null===e)return"";if(H.isDate(e))return e.toISOString();if(H.isBoolean(e))return e.toString();if(!o&&H.isBlob(e))throw new J("Blob is not supported. Use a Buffer instead.");return H.isArrayBuffer(e)||H.isTypedArray(e)?o&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let o=e;if(e&&!a&&"object"===typeof e)if(H.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(H.isArray(e)&&function(e){return H.isArray(e)&&!e.some(X)}(e)||(H.isFileList(e)||H.endsWith(n,"[]"))&&(o=H.toArray(e)))return n=Y(n),o.forEach(function(e,r){!H.isUndefined(e)&&null!==e&&t.append(!0===s?G([n],r,l):null===s?n:n+"[]",i(e))}),!1;return!!X(e)||(t.append(G(a,n,l),i(e)),!1)}const u=[],d=Object.assign(Z,{defaultVisitor:c,convertValue:i,isVisitable:X});if(!H.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!H.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),H.forEach(n,function(n,l){!0===(!(H.isUndefined(n)||null===n)&&a.call(t,n,H.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])}),u.pop()}}(e),t};function te(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ne(e,t){this._pairs=[],e&&ee(e,this,t)}const re=ne.prototype;re.append=function(e,t){this._pairs.push([e,t])},re.toString=function(e){const t=e?function(t){return e.call(this,t,te)}:te;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const ae=ne;function le(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function se(e,t,n){if(!t)return e;const r=n&&n.encode||le;H.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):H.isURLSearchParams(t)?t.toString():new ae(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const oe=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){H.forEach(this.handlers,function(t){null!==t&&e(t)})}},ie={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};function ce(e){return ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ce(e)}function ue(e){var t=function(e,t){if("object"!=ce(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ce(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ce(t)?t:t+""}function de(e,t,n){return(t=ue(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(n),!0).forEach(function(t){de(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const he={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ae,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},me="undefined"!==typeof window&&"undefined"!==typeof document,ge="object"===typeof navigator&&navigator||void 0,ye=me&&(!ge||["ReactNative","NativeScript","NS"].indexOf(ge.product)<0),ve="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,be=me&&window.location.href||"http://localhost",xe=pe(pe({},r),he);const ke=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const s=Number.isFinite(+l),o=a>=e.length;if(l=!l&&H.isArray(r)?r.length:l,o)return H.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!s;r[l]&&H.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&H.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!s}if(H.isFormData(e)&&H.isFunction(e.entries)){const n={};return H.forEachEntry(e,(e,r)=>{t(function(e){return H.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const we={transitional:ie,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=H.isObject(e);a&&H.isHTMLForm(e)&&(e=new FormData(e));if(H.isFormData(e))return r?JSON.stringify(ke(e)):e;if(H.isArrayBuffer(e)||H.isBuffer(e)||H.isStream(e)||H.isFile(e)||H.isBlob(e)||H.isReadableStream(e))return e;if(H.isArrayBufferView(e))return e.buffer;if(H.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ee(e,new xe.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return xe.isNode&&H.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((l=H.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ee(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(H.isString(e))try{return(t||JSON.parse)(e),H.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||we.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(H.isResponse(e)||H.isReadableStream(e))return e;if(e&&H.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(a){if(n){if("SyntaxError"===a.name)throw J.from(a,J.ERR_BAD_RESPONSE,this,null,this.response);throw a}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:xe.classes.FormData,Blob:xe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};H.forEach(["delete","get","head","post","put","patch"],e=>{we.headers[e]={}});const je=we,Ne=H.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Se=Symbol("internals");function _e(e){return e&&String(e).trim().toLowerCase()}function Ce(e){return!1===e||null==e?e:H.isArray(e)?e.map(Ce):String(e)}function Ee(e,t,n,r,a){return H.isFunction(r)?r.call(this,t,n):(a&&(t=n),H.isString(t)?H.isString(r)?-1!==t.indexOf(r):H.isRegExp(r)?r.test(t):void 0:void 0)}class Re{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=_e(t);if(!a)throw new Error("header name must be a non-empty string");const l=H.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=Ce(e))}const l=(e,t)=>H.forEach(e,(e,n)=>a(e,n,t));if(H.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(H.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Ne[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(H.isObject(e)&&H.isIterable(e)){let n,r,a={};for(const t of e){if(!H.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?H.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=_e(e)){const n=H.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(H.isFunction(t))return t.call(this,e,n);if(H.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=_e(e)){const n=H.findKey(this,e);return!(!n||void 0===this[n]||t&&!Ee(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=_e(e)){const a=H.findKey(n,e);!a||t&&!Ee(0,n[a],a,t)||(delete n[a],r=!0)}}return H.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Ee(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return H.forEach(this,(r,a)=>{const l=H.findKey(n,a);if(l)return t[l]=Ce(r),void delete t[a];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();s!==a&&delete t[a],t[s]=Ce(r),n[s]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return H.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&H.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[Se]=this[Se]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=_e(e);t[r]||(!function(e,t){const n=H.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return H.isArray(e)?e.forEach(r):r(e),this}}Re.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),H.reduceDescriptors(Re.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),H.freezeMethods(Re);const Oe=Re;function Le(e,t){const n=this||je,r=t||n,a=Oe.from(r.headers);let l=r.data;return H.forEach(e,function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)}),a.normalize(),l}function Pe(e){return!(!e||!e.__CANCEL__)}function Te(e,t,n){J.call(this,null==e?"canceled":e,J.ERR_CANCELED,t,n),this.name="CanceledError"}H.inherits(Te,J,{__CANCEL__:!0});const Me=Te;function Ae(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new J("Request failed with status code "+n.status,[J.ERR_BAD_REQUEST,J.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const De=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,s=0;return t=void 0!==t?t:1e3,function(o){const i=Date.now(),c=r[s];a||(a=i),n[l]=o,r[l]=i;let u=s,d=0;for(;u!==l;)d+=n[u++],u%=e;if(l=(l+1)%e,l===s&&(s=(s+1)%e),i-a<t)return;const f=c&&i-c;return f?Math.round(1e3*d/f):void 0}};const ze=function(e,t){let n,r,a=0,l=1e3/t;const s=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var o=arguments.length,i=new Array(o),c=0;c<o;c++)i[c]=arguments[c];t>=l?s(i,e):(n=i,r||(r=setTimeout(()=>{r=null,s(n)},l-t)))},()=>n&&s(n)]},Fe=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=De(50,250);return ze(n=>{const l=n.loaded,s=n.lengthComputable?n.total:void 0,o=l-r,i=a(o);r=l;e({loaded:l,total:s,progress:s?l/s:void 0,bytes:o,rate:i||void 0,estimated:i&&s&&l<=s?(s-l)/i:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})},n)},Ie=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ue=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return H.asap(()=>e(...n))},Be=xe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,xe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(xe.origin),xe.navigator&&/(msie|trident)/i.test(xe.navigator.userAgent)):()=>!0,Ve=xe.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const s=[e+"="+encodeURIComponent(t)];H.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),H.isString(r)&&s.push("path="+r),H.isString(a)&&s.push("domain="+a),!0===l&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function We(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ke=e=>e instanceof Oe?pe({},e):e;function He(e,t){t=t||{};const n={};function r(e,t,n,r){return H.isPlainObject(e)&&H.isPlainObject(t)?H.merge.call({caseless:r},e,t):H.isPlainObject(t)?H.merge({},t):H.isArray(t)?t.slice():t}function a(e,t,n,a){return H.isUndefined(t)?H.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!H.isUndefined(t))return r(void 0,t)}function s(e,t){return H.isUndefined(t)?H.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function o(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const i={url:l,method:l,data:l,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:o,headers:(e,t,n)=>a(Ke(e),Ke(t),0,!0)};return H.forEach(Object.keys(Object.assign({},e,t)),function(r){const l=i[r]||a,s=l(e[r],t[r],r);H.isUndefined(s)&&l!==o||(n[r]=s)}),n}const $e=e=>{const t=He({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:s,headers:o,auth:i}=t;if(t.headers=o=Oe.from(o),t.url=se(We(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),i&&o.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):""))),H.isFormData(r))if(xe.hasStandardBrowserEnv||xe.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if(!1!==(n=o.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...t].join("; "))}if(xe.hasStandardBrowserEnv&&(a&&H.isFunction(a)&&(a=a(t)),a||!1!==a&&Be(t.url))){const e=l&&s&&Ve.read(s);e&&o.set(l,e)}return t},qe="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=$e(e);let a=r.data;const l=Oe.from(r.headers).normalize();let s,o,i,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Oe.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Ae(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new J("Request aborted",J.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new J("Network Error",J.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||ie;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new J(t,a.clarifyTimeoutError?J.ETIMEDOUT:J.ECONNABORTED,e,m)),m=null},void 0===a&&l.setContentType(null),"setRequestHeader"in m&&H.forEach(l.toJSON(),function(e,t){m.setRequestHeader(t,e)}),H.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([i,u]=Fe(p,!0),m.addEventListener("progress",i)),f&&m.upload&&([o,c]=Fe(f),m.upload.addEventListener("progress",o),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(s=t=>{m&&(n(!t||t.type?new Me(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===xe.protocols.indexOf(y)?n(new J("Unsupported protocol "+y+":",J.ERR_BAD_REQUEST,e)):m.send(a||null)})},Qe=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof J?t:new Me(t instanceof Error?t.message:t))}};let l=t&&setTimeout(()=>{l=null,a(new J("timeout ".concat(t," of ms exceeded"),J.ETIMEDOUT))},t);const s=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:o}=r;return o.unsubscribe=()=>H.asap(s),o}};function Je(e,t){this.v=e,this.k=t}function Xe(e){return function(){return new Ye(e.apply(this,arguments))}}function Ye(e){var t,n;function r(t,n){try{var l=e[t](n),s=l.value,o=s instanceof Je;Promise.resolve(o?s.v:s).then(function(n){if(o){var i="return"===t?"return":"next";if(!s.k||n.done)return r(i,n);n=e[i](n).value}a(l.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(l,s){var o={key:e,arg:a,resolve:l,reject:s,next:null};n?n=n.next=o:(t=n=o,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function Ge(e){return new Je(e,0)}function Ze(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new Je(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function et(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new tt(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function tt(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return tt=function(e){this.s=e,this.n=e.next},tt.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new tt(e)}Ye.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Ye.prototype.next=function(e){return this._invoke("next",e)},Ye.prototype.throw=function(e){return this._invoke("throw",e)},Ye.prototype.return=function(e){return this._invoke("return",e)};const nt=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},rt=function(){var e=Xe(function*(e,t){var n,r=!1,a=!1;try{for(var l,s=et(at(e));r=!(l=yield Ge(s.next())).done;r=!1){const e=l.value;yield*Ze(et(nt(e,t)))}}catch(o){a=!0,n=o}finally{try{r&&null!=s.return&&(yield Ge(s.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),at=function(){var e=Xe(function*(e){if(e[Symbol.asyncIterator])return void(yield*Ze(et(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Ge(t.read());if(e)break;yield n}}finally{yield Ge(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),lt=(e,t,n,r)=>{const a=rt(e,t);let l,s=0,o=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return o(),void e.close();let l=r.byteLength;if(n){let e=s+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw o(t),t}},cancel:e=>(o(e),a.return())},{highWaterMark:2})},st="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,ot=st&&"function"===typeof ReadableStream,it=st&&("function"===typeof TextEncoder?(ct=new TextEncoder,e=>ct.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var ct;const ut=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(a){return!1}},dt=ot&&ut(()=>{let e=!1;const t=new Request(xe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ft=ot&&ut(()=>H.isReadableStream(new Response("").body)),pt={stream:ft&&(e=>e.body)};var ht;st&&(ht=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!pt[e]&&(pt[e]=H.isFunction(ht[e])?t=>t[e]():(t,n)=>{throw new J("Response type '".concat(e,"' is not supported"),J.ERR_NOT_SUPPORT,n)})}));const mt=async(e,t)=>{const n=H.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(H.isBlob(e))return e.size;if(H.isSpecCompliantForm(e)){const t=new Request(xe.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return H.isArrayBufferView(e)||H.isArrayBuffer(e)?e.byteLength:(H.isURLSearchParams(e)&&(e+=""),H.isString(e)?(await it(e)).byteLength:void 0)})(t):n},gt={http:null,xhr:qe,fetch:st&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:s,onDownloadProgress:o,onUploadProgress:i,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=$e(e);c=c?(c+"").toLowerCase():"text";let p,h=Qe([a,l&&l.toAbortSignal()],s);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(i&&dt&&"get"!==n&&"head"!==n&&0!==(g=await mt(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(H.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Ie(g,Fe(Ue(i)));r=lt(n.body,65536,e,t)}}H.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,pe(pe({},f),{},{signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let l=await fetch(p,f);const s=ft&&("stream"===c||"response"===c);if(ft&&(o||s&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=l[t]});const t=H.toFiniteNumber(l.headers.get("content-length")),[n,r]=o&&Ie(t,Fe(Ue(o),!0))||[];l=new Response(lt(l.body,65536,n,()=>{r&&r(),m&&m()}),e)}c=c||"text";let y=await pt[H.findKey(pt,c)||"text"](l,e);return!s&&m&&m(),await new Promise((t,n)=>{Ae(t,n,{data:y,headers:Oe.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:p})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new J("Network Error",J.ERR_NETWORK,e,p),{cause:y.cause||y});throw J.from(y,y&&y.code,e,p)}})};H.forEach(gt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const yt=e=>"- ".concat(e),vt=e=>H.isFunction(e)||null===e||!1===e,bt=e=>{e=H.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!vt(n)&&(r=gt[(t=String(n)).toLowerCase()],void 0===r))throw new J("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(yt).join("\n"):" "+yt(e[0]):"as no adapter specified";throw new J("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function xt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Me(null,e)}function kt(e){xt(e),e.headers=Oe.from(e.headers),e.data=Le.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return bt(e.adapter||je.adapter)(e).then(function(t){return xt(e),t.data=Le.call(e,e.transformResponse,t),t.headers=Oe.from(t.headers),t},function(t){return Pe(t)||(xt(e),t&&t.response&&(t.response.data=Le.call(e,e.transformResponse,t.response),t.response.headers=Oe.from(t.response.headers))),Promise.reject(t)})}const wt="1.10.0",jt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{jt[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Nt={};jt.transitional=function(e,t,n){function r(e,t){return"[Axios v"+wt+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new J(r(a," has been removed"+(t?" in "+t:"")),J.ERR_DEPRECATED);return t&&!Nt[a]&&(Nt[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},jt.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const St={assertOptions:function(e,t,n){if("object"!==typeof e)throw new J("options must be an object",J.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],s=t[l];if(s){const t=e[l],n=void 0===t||s(t,l,e);if(!0!==n)throw new J("option "+l+" must be "+n,J.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new J("Unknown option "+l,J.ERR_BAD_OPTION)}},validators:jt},_t=St.validators;class Ct{constructor(e){this.defaults=e||{},this.interceptors={request:new oe,response:new oe}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=He(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&St.assertOptions(n,{silentJSONParsing:_t.transitional(_t.boolean),forcedJSONParsing:_t.transitional(_t.boolean),clarifyTimeoutError:_t.transitional(_t.boolean)},!1),null!=r&&(H.isFunction(r)?t.paramsSerializer={serialize:r}:St.assertOptions(r,{encode:_t.function,serialize:_t.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),St.assertOptions(t,{baseUrl:_t.spelling("baseURL"),withXsrfToken:_t.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&H.merge(a.common,a[t.method]);a&&H.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=Oe.concat(l,a);const s=[];let o=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,s.unshift(e.fulfilled,e.rejected))});const i=[];let c;this.interceptors.response.forEach(function(e){i.push(e.fulfilled,e.rejected)});let u,d=0;if(!o){const e=[kt.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,i),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=s.length;let f=t;for(d=0;d<u;){const e=s[d++],t=s[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{c=kt.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,u=i.length;d<u;)c=c.then(i[d++],i[d++]);return c}getUri(e){return se(We((e=He(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}H.forEach(["delete","get","head","options"],function(e){Ct.prototype[e]=function(t,n){return this.request(He(n||{},{method:e,url:t,data:(n||{}).data}))}}),H.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(He(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Ct.prototype[e]=t(),Ct.prototype[e+"Form"]=t(!0)});const Et=Ct;class Rt{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new Me(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Rt(function(t){e=t}),cancel:e}}}const Ot=Rt;const Lt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Lt).forEach(e=>{let[t,n]=e;Lt[n]=t});const Pt=Lt;const Tt=function e(t){const n=new Et(t),r=s(Et.prototype.request,n);return H.extend(r,Et.prototype,n,{allOwnKeys:!0}),H.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(He(t,n))},r}(je);Tt.Axios=Et,Tt.CanceledError=Me,Tt.CancelToken=Ot,Tt.isCancel=Pe,Tt.VERSION=wt,Tt.toFormData=ee,Tt.AxiosError=J,Tt.Cancel=Tt.CanceledError,Tt.all=function(e){return Promise.all(e)},Tt.spread=function(e){return function(t){return e.apply(null,t)}},Tt.isAxiosError=function(e){return H.isObject(e)&&!0===e.isAxiosError},Tt.mergeConfig=He,Tt.AxiosHeaders=Oe,Tt.formToJSON=e=>ke(H.isHTMLForm(e)?new FormData(e):e),Tt.getAdapter=bt,Tt.HttpStatusCode=Pt,Tt.default=Tt;const Mt="http://localhost:8000",At=Tt.create({baseURL:Mt,timeout:3e4,headers:{"Content-Type":"application/json"},withCredentials:!1});At.interceptors.request.use(e=>{var t;return console.log("API Request: ".concat(null===(t=e.method)||void 0===t?void 0:t.toUpperCase()," ").concat(e.baseURL).concat(e.url)),e},e=>(console.error("API Request Error:",e),Promise.reject(e))),At.interceptors.response.use(e=>(console.log("API Response: ".concat(e.status," ").concat(e.config.baseURL).concat(e.config.url)),e),e=>(console.error("API Response Error:",e),e.response?(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data)):e.request?console.error("No response received:",e.request):console.error("Request setup error:",e.message),Promise.reject(e)));const Dt=async e=>{try{return(await At.delete("/api/annotations/".concat(e))).data}catch(t){throw console.error("Failed to delete annotation ".concat(e,":"),t),t}},zt=async e=>{try{return(await At.get("/api/annotations",{params:{dataset_name:e}})).data}catch(t){throw console.error("\u83b7\u53d6\u6570\u636e\u96c6\u6807\u6ce8\u5931\u8d25:",t),t}};var Ft=n(579);const It=e=>{let{selectedDataset:t,onDatasetChange:n,disabled:r=!1}=e;const[l,s]=(0,a.useState)([]),[o,i]=(0,a.useState)(!0),[c,u]=(0,a.useState)(null);(0,a.useEffect)(()=>{d()},[]);const d=async()=>{try{i(!0),u(null);const e=await(async()=>{try{return(await At.get("/api/datasets")).data.map(e=>e.name)}catch(c){throw console.error("Failed to fetch dataset list:",c),c}})();if(s(e),!t&&e.length>0){const t=e.find(e=>"kingsoft"===e);n(t||e[0])}}catch(e){console.error("Failed to load datasets:",e),u("\u52a0\u8f7d\u6570\u636e\u96c6\u5217\u8868\u5931\u8d25")}finally{i(!1)}},f=()=>{d()};return o?(0,Ft.jsx)("div",{className:"dataset-selector",children:(0,Ft.jsxs)("div",{className:"dataset-selector-header",children:[(0,Ft.jsx)("label",{className:"dataset-selector-label",children:"\u6570\u636e\u96c6:"}),(0,Ft.jsx)("div",{className:"dataset-selector-loading",children:"\u52a0\u8f7d\u4e2d..."})]})}):c?(0,Ft.jsx)("div",{className:"dataset-selector",children:(0,Ft.jsxs)("div",{className:"dataset-selector-header",children:[(0,Ft.jsx)("label",{className:"dataset-selector-label",children:"\u6570\u636e\u96c6:"}),(0,Ft.jsxs)("div",{className:"dataset-selector-error",children:[c,(0,Ft.jsx)("button",{className:"dataset-selector-refresh-btn",onClick:f,title:"\u91cd\u65b0\u52a0\u8f7d",children:"\u21bb"})]})]})}):(0,Ft.jsxs)("div",{className:"dataset-selector",children:[(0,Ft.jsxs)("div",{className:"dataset-selector-header",children:[(0,Ft.jsx)("label",{className:"dataset-selector-label",htmlFor:"dataset-select",children:"\u6570\u636e\u96c6:"}),(0,Ft.jsxs)("div",{className:"dataset-selector-controls",children:[(0,Ft.jsxs)("select",{id:"dataset-select",className:"dataset-selector-select",value:t||"",onChange:e=>{const t=e.target.value;n(t)},disabled:r,children:[(0,Ft.jsx)("option",{value:"",disabled:!0,children:"\u8bf7\u9009\u62e9\u6570\u636e\u96c6"}),l.map(e=>(0,Ft.jsx)("option",{value:e,children:e},e))]}),(0,Ft.jsx)("button",{className:"dataset-selector-refresh-btn",onClick:f,disabled:r,title:"\u5237\u65b0\u6570\u636e\u96c6\u5217\u8868",children:"\u21bb"})]})]}),t&&(0,Ft.jsxs)("div",{className:"dataset-selector-info",children:[(0,Ft.jsxs)("span",{className:"dataset-selector-current",children:["\u5f53\u524d\u6570\u636e\u96c6: ",(0,Ft.jsx)("strong",{children:t})]}),(0,Ft.jsxs)("span",{className:"dataset-selector-count",children:["\u5171 ",l.length," \u4e2a\u6570\u636e\u96c6"]})]})]})},Ut=e=>{if(!e||!e.trim())return"<div style='color:gray;'>\u65e0\u8868\u683c\u5185\u5bb9</div>";try{const t=e.trim().split("\n"),n=[];let r=!1;for(const e of t){const t=e.trim();if(t.startsWith("|")&&t.endsWith("|")){if(!t.replace(/\|/g,"").replace(/-/g,"").replace(/ /g,""))continue;n.push(t),r=!0}else if(r)break}if(!n.length)return"<div style='color:gray;'>\u65e0\u6709\u6548\u8868\u683c\u5185\u5bb9</div>";const a=[];let l=0;for(const e of n){const t=e.slice(1,-1).split("|").map(e=>e.trim());a.push(t),l=Math.max(l,t.length)}a.forEach(e=>{for(;e.length<l;)e.push("")});const s=["<table border='1' style='border-collapse: collapse; width: 100%;'>"];return a.forEach((e,t)=>{0===t?(s.push("<thead><tr>"),e.forEach(e=>{s.push("<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>".concat(e,"</th>"))}),s.push("</tr></thead><tbody>")):(s.push("<tr>"),e.forEach(e=>{s.push("<td style='border: 1px solid #ddd; padding: 8px;'>".concat(e,"</td>"))}),s.push("</tr>"))}),s.push("</tbody></table>"),s.join("")}catch(t){return"<div style='color:red;'>\u8868\u683c\u89e3\u6790\u5931\u8d25: ".concat(t.message,"</div>")}},Bt=e=>e.replace(/\.(png|jpg|jpeg|gif|webp|pdf)$/i,""),Vt=(e,t)=>{if(!e||!t)return 0;try{const n=Wt(String(e)),r=Wt(String(t));return n&&r?$t(n,r):qt(e,t)}catch(n){return console.warn("\u8868\u683c\u89e3\u6790\u5931\u8d25\uff0c\u4f7f\u7528\u7b80\u5355\u7b97\u6cd5:",n),qt(e,t)}},Wt=e=>{try{return e.includes("<table")?Kt(e):e.includes("|")?Ht(e):null}catch(t){return null}},Kt=e=>{const t=(new DOMParser).parseFromString(e,"text/html").querySelector("table");if(!t)return null;const n=[];return t.querySelectorAll("tr").forEach(e=>{const t=[];e.querySelectorAll("th, td").forEach(e=>{t.push(e.textContent.trim())}),t.length>0&&n.push(t)}),n},Ht=e=>{const t=e.split("\n").map(e=>e.trim()).filter(e=>e).filter(e=>e.startsWith("|")&&e.endsWith("|"));if(t.length<2)return null;const n=t.filter(e=>!e.match(/^\|[\s\-\|]+\|$/)),r=[];return n.forEach(e=>{const t=e.split("|").slice(1,-1).map(e=>e.trim());t.length>0&&r.push(t)}),r},$t=(e,t)=>{if(!e||!t)return 0;if(e.length!==t.length)return Math.max(0,100-10*Math.abs(e.length-t.length));let n=0,r=0;for(let a=0;a<e.length;a++){const l=e[a]||[],s=t[a]||[],o=Math.max(l.length,s.length);for(let e=0;e<o;e++){n++;(l[e]||"").toLowerCase().trim()===(s[e]||"").toLowerCase().trim()&&r++}}return n>0?r/n*100:0},qt=(e,t)=>{if(!e||!t)return 0;const n=String(e).toLowerCase().trim(),r=String(t).toLowerCase().trim();if(n===r)return 100;const a=Qt(n,r),l=Math.max(n.length,r.length);return 0===l?100:Math.max(0,(l-a)/l*100)},Qt=(e,t)=>{const n=[];for(let r=0;r<=t.length;r++)n[r]=[r];for(let r=0;r<=e.length;r++)n[0][r]=r;for(let r=1;r<=t.length;r++)for(let a=1;a<=e.length;a++)t.charAt(r-1)===e.charAt(a-1)?n[r][a]=n[r-1][a-1]:n[r][a]=Math.min(n[r-1][a-1]+1,n[r][a-1]+1,n[r-1][a]+1);return n[t.length][e.length]},Jt=function(e){if(!e)return"";switch(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"markdown"){case"markdown":if("string"===typeof e)return e;if("object"===typeof e)try{if(e.results&&Array.isArray(e.results)&&e.results.length>0){const t=e.results[0];if(t.result&&"object"===typeof t.result){if(!1===t.result.success)return"MonkeyOCR Local\u5904\u7406\u5931\u8d25: ".concat(t.result.html||t.result.markdown||"\u672a\u77e5\u9519\u8bef");if(t.result.html)return t.result.html;if(t.result.markdown)return t.result.markdown}}if(e.result&&"object"===typeof e.result){if(e.result.html)return e.result.html;if(e.result.markdown)return e.result.markdown;const t=e.result.data||[];if(Array.isArray(t)&&t.length>0)return t[0].markdown||""}if(e.result&&"string"===typeof e.result)return e.result;const t=e.data||[];if(Array.isArray(t)&&t.length>0)return t[0].markdown||"";if("object"===typeof t)return t.markdown||""}catch(t){console.error("\u63d0\u53d6markdown\u5185\u5bb9\u5931\u8d25:",t)}return"";case"plain":if("string"===typeof e)return e;if("object"===typeof e)try{if(e.result&&e.result.data){const t=e.result.data||[];if(Array.isArray(t)&&t.length>0)return t[0].plain||""}const t=e.data||[];if(Array.isArray(t)&&t.length>0)return t[0].plain||"";if("object"===typeof t)return t.plain||""}catch(t){console.error("\u63d0\u53d6plain\u5185\u5bb9\u5931\u8d25:",t)}return"";case"kdc":if("object"===typeof e)try{if(e.result&&e.result.data){const t=e.result.data||[];if(Array.isArray(t)&&t.length>0){const e=t[0].doc||{};if(e&&"object"===typeof e)return JSON.stringify(e,null,2)}}const t=e.data||[];let n=null;if(Array.isArray(t)&&t.length>0?n=t[0].doc||{}:"object"===typeof t&&(n=t.doc||{}),n)return JSON.stringify(n,null,2)}catch(t){console.error("\u63d0\u53d6kdc\u5185\u5bb9\u5931\u8d25:",t)}return"";case"html":if("string"===typeof e)return e;if("object"===typeof e){if(e.result&&!1===e.result.success)return"MonkeyOCR\u5904\u7406\u5931\u8d25: ".concat(e.result.html||"\u672a\u77e5\u9519\u8bef");if(e.result&&e.result.is_timeout){const t=e.result.processing_time||0;return"MonkeyOCR\u5904\u7406\u8d85\u65f6 (".concat(t.toFixed(1),"\u79d2)")}if(e.result){if(e.result.html)return e.result.html;if(e.result.text_content&&Array.isArray(e.result.text_content)&&e.result.text_content.length>0)return e.result.text_content[0]}if(e.html)return e.html;if(e.text_content&&Array.isArray(e.text_content)&&e.text_content.length>0)return e.text_content[0]}return"";case"vl_llm":if("object"===typeof e)try{const t=e.result||{};let n=[];if(t.content&&t.content.choices?(n=t.content.choices,console.log("VL LLM using structure 1 (result.content.choices)")):t.choices&&(n=t.choices,console.log("VL LLM using structure 2 (result.choices)")),n.length>0){let e=(n[0].message||{}).content||"";return e.startsWith("```markdown")?e=e.replace("```markdown","").replace(/```$/,"").trim():e.startsWith("```")&&(e=e.replace(/^```/,"").replace(/```$/,"").trim()),console.log("VL LLM content extracted successfully, length:",e.length),e}}catch(t){console.error("\u63d0\u53d6VL LLM\u5185\u5bb9\u5931\u8d25:",t)}return"";default:return"string"===typeof e?e:JSON.stringify(e,null,2)}},Xt=function(e){if(!e)return"";switch(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"markdown"){case"markdown":if("string"===typeof e)return e;if("object"===typeof e)try{if(e.results&&Array.isArray(e.results)&&e.results.length>0){const t=e.results[0];if(t.result&&"object"===typeof t.result){if(!1===t.result.success)return"MonkeyOCR Local\u5904\u7406\u5931\u8d25: ".concat(t.result.html||t.result.markdown||"\u672a\u77e5\u9519\u8bef");if("latex"===t.result.type)return t.result.latex||t.result.html;if("html"===t.result.type)return t.result.html;if(t.result.markdown)return t.result.markdown;if(t.result.html)return t.result.html}}if(e.result&&"object"===typeof e.result){if("latex"===e.result.type)return e.result.latex||e.result.html;if("html"===e.result.type)return e.result.html;if("string"===typeof e.result)return e.result;if(e.result.markdown)return e.result.markdown;if(e.result.html)return e.result.html;const t=e.result.data||[];if(Array.isArray(t)&&t.length>0)return t[0].markdown||""}const t=e.data||[];if(Array.isArray(t)&&t.length>0)return t[0].markdown||"";if("object"===typeof t)return t.markdown||""}catch(t){console.error("\u63d0\u53d6markdown\u5185\u5bb9\u5931\u8d25:",t)}return"";case"latex":if("string"===typeof e)return e;if("object"===typeof e){if(e.results&&Array.isArray(e.results)&&e.results.length>0){const t=e.results[0];if(t.result&&t.result.latex)return t.result.latex;if(t.result&&t.result.html)return t.result.html}if(e.result&&e.result.html)return e.result.html}return"";case"plain":if("string"===typeof e)return e;if("object"===typeof e)try{if(e.result&&e.result.data){const t=e.result.data||[];if(Array.isArray(t)&&t.length>0)return t[0].plain||""}const t=e.data||[];if(Array.isArray(t)&&t.length>0)return t[0].plain||"";if("object"===typeof t)return t.plain||""}catch(t){console.error("\u63d0\u53d6plain\u5185\u5bb9\u5931\u8d25:",t)}return"";case"kdc":if("object"===typeof e)try{if(e.result&&e.result.data){const t=e.result.data||[];if(Array.isArray(t)&&t.length>0){const e=t[0].doc||{};if(e&&"object"===typeof e)return{data:[{doc:e}]}}}const t=e.data||[];let n=null;if(Array.isArray(t)&&t.length>0?n=t[0].doc||{}:"object"===typeof t&&(n=t.doc||{}),n)return{data:[{doc:n}]}}catch(t){console.error("\u63d0\u53d6kdc\u5185\u5bb9\u5931\u8d25:",t)}return"";case"html":if("string"===typeof e)return e;if("object"===typeof e){if(e.result&&!1===e.result.success)return"MonkeyOCR\u5904\u7406\u5931\u8d25: ".concat(e.result.html||"\u672a\u77e5\u9519\u8bef");if(e.result&&e.result.is_timeout){const t=e.result.processing_time||0;return"MonkeyOCR\u5904\u7406\u8d85\u65f6 (".concat(t.toFixed(1),"\u79d2)")}if(e.result){if(e.result.html)return e.result.html;if(e.result.text_content&&Array.isArray(e.result.text_content)&&e.result.text_content.length>0)return e.result.text_content[0]}if(e.html)return e.html;if(e.text_content&&Array.isArray(e.text_content)&&e.text_content.length>0)return e.text_content[0]}return"";case"vl_llm":if("object"===typeof e)try{const t=e.result||{};let n=[];if(t.content&&t.content.choices?n=t.content.choices:t.choices&&(n=t.choices),n.length>0){let e=(n[0].message||{}).content||"";return e.startsWith("```markdown")?e=e.replace("```markdown","").replace(/```$/,"").trim():e.startsWith("```")&&(e=e.replace(/^```/,"").replace(/```$/,"").trim()),e}}catch(t){console.error("\u63d0\u53d6VL LLM\u6e32\u67d3\u5185\u5bb9\u5931\u8d25:",t)}return"";default:return e}},Yt=e=>{let{caseData:t,onClick:n,selected:r=!1}=e;const{index:a,fileName:l,baseName:s,imagePath:o,vlLLMResult:i,kdcMarkdown:c,kdcPlain:u,kdcKdc:d,monkeyOCR:f,monkeyOCRV2:p,monkeyOCRLocal:h}=t,m=!!o,g=(e,t)=>{if(!e)return"empty";switch(t){case"kdc":if(e.result&&e.result.data){const t=e.result.data||[];return Array.isArray(t)&&t.length>0&&t[0].markdown?"success":"error"}const t=e.data||[];return Array.isArray(t)&&t.length>0&&t[0].markdown?"success":"error";case"plain":if(e.result&&e.result.data){const t=e.result.data||[];return Array.isArray(t)&&t.length>0&&t[0].plain?"success":"error"}const r=e.data||[];return Array.isArray(r)&&r.length>0&&r[0].plain?"success":"error";case"kdckdc":if(e.result&&e.result.data){const t=e.result.data||[];return Array.isArray(t)&&t.length>0&&t[0].doc?"success":"error"}const a=e.data||[];return Array.isArray(a)&&a.length>0&&a[0].doc?"success":"error";case"monkey":return e.result?!1===e.result.success||e.result.is_timeout?"error":e.result.html&&e.result.html.trim()&&!e.result.html.includes("MonkeyOCR\u6587\u4ef6\u4e0a\u4f20\u5931\u8d25")?"success":"error":e.html&&e.html.trim()&&!e.html.includes("MonkeyOCR\u6587\u4ef6\u4e0a\u4f20\u5931\u8d25")?"success":"error";case"monkey_local":if(e.results&&Array.isArray(e.results)&&e.results.length>0){const t=e.results[0];if(t.result){if(!1===t.result.success)return"error";if(t.result.html&&t.result.html.trim()||t.result.markdown&&t.result.markdown.trim())return"success"}return"error"}if(e.result){if(!1===e.result.success)return"error";if(e.result.html&&e.result.html.trim()||e.result.markdown&&e.result.markdown.trim())return"success"}return"error";case"vl_llm":if(e.result){let t=[];if(e.result.content&&e.result.content.choices?t=e.result.content.choices:e.result.choices&&(t=e.result.choices),t.length>0){var n;const e=null===(n=t[0].message)||void 0===n?void 0:n.content;return e&&e.trim()?"success":"error"}}return"error";default:return"empty"}};return(0,Ft.jsxs)("div",{className:"case-item ".concat(r?"selected":""),onClick:()=>{n&&n(t)},children:[(0,Ft.jsxs)("div",{className:"case-item-index",children:["#",a]}),(0,Ft.jsxs)("div",{className:"case-item-info",children:[(0,Ft.jsx)("div",{className:"case-item-filename",title:l,children:l}),(0,Ft.jsxs)("div",{className:"case-item-meta",children:[(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat(m?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"\u56fe\u7247"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(f,"monkey")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"Monkey(table)"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(p,"monkey")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"Monkey(parse)"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(h,"monkey_local")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"Monkey(local)"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(u,"plain")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"KDC Plain"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(d,"kdckdc")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"KDC KDC"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(c,"kdc")?"has-image":"no-image")}),(0,Ft.jsx)("span",{children:"KDC MD"})]}),(0,Ft.jsxs)("div",{className:"case-item-status",children:[(0,Ft.jsx)("div",{className:"status-indicator ".concat("success"===g(i,"vl_llm")?"has-vl-llm":"no-vl-llm")}),(0,Ft.jsx)("span",{children:"VL-LLM"})]})]})]})]})},Gt=e=>{let{cases:t=[]}=e;const n=(0,a.useMemo)(()=>{if(!t.length)return null;const e=t.length,n={kdcMarkdown:{success:0,accuracy:0},kdcPlain:{success:0,accuracy:0},kdcKdc:{success:0,accuracy:0},monkeyOCR:{success:0,accuracy:0},monkeyOCRV2:{success:0,accuracy:0},vlLLM:{success:0,accuracy:0},monkeyOCRLocal:{success:0,accuracy:0},tableDetection:{detected:0,total:0}};return t.forEach(e=>{if(e.kdcMarkdown&&e.kdcMarkdown.result&&(n.kdcMarkdown.success++,e.kdcMarkdown.accuracy&&(n.kdcMarkdown.accuracy+=e.kdcMarkdown.accuracy)),e.kdcPlain&&e.kdcPlain.result&&(n.kdcPlain.success++,e.kdcPlain.accuracy&&(n.kdcPlain.accuracy+=e.kdcPlain.accuracy)),e.kdcKdc&&e.kdcKdc.result&&(n.kdcKdc.success++,e.kdcKdc.accuracy&&(n.kdcKdc.accuracy+=e.kdcKdc.accuracy)),e.monkeyOCR&&e.monkeyOCR.result&&(n.monkeyOCR.success++,e.monkeyOCR.accuracy&&(n.monkeyOCR.accuracy+=e.monkeyOCR.accuracy)),e.monkeyOCRV2&&e.monkeyOCRV2.result&&(n.monkeyOCRV2.success++,e.monkeyOCRV2.accuracy&&(n.monkeyOCRV2.accuracy+=e.monkeyOCRV2.accuracy)),e.vlLLMResult&&e.vlLLMResult.result){let r=!1;const a=e.vlLLMResult.result;let l=[];if(a.content&&a.content.choices?l=a.content.choices:a.choices&&(l=a.choices),l.length>0){var t;const e=null===(t=l[0].message)||void 0===t?void 0:t.content;r=!(!e||!e.trim())}r&&(n.vlLLM.success++,e.vlLLMResult.accuracy&&(n.vlLLM.accuracy+=e.vlLLMResult.accuracy))}e.monkeyOCRLocal&&e.monkeyOCRLocal.result&&(n.monkeyOCRLocal.success++,e.monkeyOCRLocal.accuracy&&(n.monkeyOCRLocal.accuracy+=e.monkeyOCRLocal.accuracy)),n.tableDetection.total++,e.features&&e.features.kdc&&e.features.kdc.table_detection&&e.features.kdc.table_detection.has_table&&n.tableDetection.detected++}),Object.keys(n).forEach(t=>{if("tableDetection"===t){const e=n[t].detected/n[t].total*100;n[t]={detectionRate:e.toFixed(1),detected:n[t].detected,total:n[t].total}}else{const r=n[t].success/e*100,a=n[t].success?n[t].accuracy/n[t].success:0;n[t]={successRate:r.toFixed(1),avgAccuracy:a.toFixed(1)}}}),n},[t]);return n?(0,Ft.jsxs)("div",{className:"stats-panel",children:[(0,Ft.jsx)("div",{className:"stats-panel-header",children:(0,Ft.jsx)("h3",{children:"\u7edf\u8ba1\u6307\u6807"})}),(0,Ft.jsxs)("div",{className:"stats-grid",children:[(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"KDC Markdown \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.kdcMarkdown.successRate>=80?"success":"warning"),children:[n.kdcMarkdown.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"KDC Plain \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.kdcPlain.successRate>=80?"success":"warning"),children:[n.kdcPlain.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"MonkeyOCR(table) \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.monkeyOCR.successRate>=80?"success":"warning"),children:[n.monkeyOCR.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"MonkeyOCR(parse) \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.monkeyOCRV2.successRate>=80?"success":"warning"),children:[n.monkeyOCRV2.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"VL-LLM \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.vlLLM.successRate>=80?"success":"warning"),children:[n.vlLLM.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"MonkeyOCR(local) \u6210\u529f\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.monkeyOCRLocal.successRate>=80?"success":"warning"),children:[n.monkeyOCRLocal.successRate,"%"]})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("div",{className:"stat-label",children:"KDC \u8868\u683c\u68c0\u6d4b\u7387"}),(0,Ft.jsxs)("div",{className:"stat-value ".concat(n.tableDetection.detectionRate>=50?"success":"warning"),children:[n.tableDetection.detectionRate,"% (",n.tableDetection.detected,"/",n.tableDetection.total,")"]})]})]})]}):(0,Ft.jsxs)("div",{className:"stats-panel",children:[(0,Ft.jsx)("div",{className:"stats-panel-header",children:(0,Ft.jsx)("h3",{children:"\u7edf\u8ba1\u6307\u6807"})}),(0,Ft.jsx)("div",{className:"stats-empty",children:"\u6682\u65e0\u6570\u636e"})]})},Zt=e=>{let{selectedDataset:t,onCaseSelect:n,selectedCase:r,onCasesUpdate:l}=e;const[s,o]=(0,a.useState)([]),[i,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)(null),[f,p]=(0,a.useState)({current:0,total:0}),[h,m]=(0,a.useState)(null),[g,y]=(0,a.useState)(!1),[v,b]=(0,a.useState)(null),x=(0,a.useRef)(null),[k,w]=(0,a.useState)(""),[j,N]=(0,a.useState)(""),[S,_]=(0,a.useState)([]),C=(0,a.useCallback)(async function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(!t)return o([]),void(l&&l([]));try{var a,s,i;e&&(c(!0),d(null),p({current:0,total:3})),e&&p({current:1,total:3});const f=await(async e=>{try{const t=await At.get("/api/datasets/".concat(e,"/parse_results"));return console.log("Successfully loaded parse results:",t.data),t.data}catch(u){throw console.error("Failed to fetch parse results for ".concat(e,":"),u),u}})(t),h=(null===f||void 0===f||null===(a=f.metadata)||void 0===a?void 0:a.status)||"unknown",v=(null===f||void 0===f?void 0:f.progress)||{};m({status:h,current:v.completed_count||0,total:(null===f||void 0===f||null===(s=f.metadata)||void 0===s?void 0:s.total_files)||0,currentFile:v.current_file||"",currentParser:v.current_parser||""}),e&&p({current:2,total:2});const x=await(async e=>{try{return(await At.get("/api/datasets/".concat(e,"/images"))).data.map(e=>e.filename)}catch(u){throw console.error("Failed to fetch image list for ".concat(e,":"),u),u}})(t);console.log("\u56fe\u7247\u5217\u8868:",x),console.log("\u89e3\u6790\u7ed3\u679c:",f),console.log("\u89e3\u6790\u72b6\u6001:",h,"(".concat(v.completed_count||0,"/").concat((null===f||void 0===f||null===(i=f.metadata)||void 0===i?void 0:i.total_files)||0,")"));const w=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!e)return[];const n=e.parse_results||{},{kdc_markdown:r=[],kdc_plain:a=[],kdc_kdc:l=[],monkey_ocr:s=[],monkey_ocr_latex:o=[],vl_llm:i=[],monkey_ocr_local:c=[]}=n,{uploaded_files:u=[],kdc_results:d=[],kdc_plain_results:f=[],kdc_kdc_results:p=[],monkey_ocr_results:h=[],monkey_ocr_results_v2:m=[],vl_llm_results:g=[],monkey_ocr_local_results:y=[]}=e,v=r.length>0?r:d,b=a.length>0?a:f,x=l.length>0?l:p,k=s.length>0?s:h,w=o.length>0?o:m,j=i.length>0?i:g,N=c.length>0?c:y,S=t.length>0?t:u,_=Array.isArray(S)?S.slice().sort((e,t)=>{const n="string"===typeof e?e:e.original_image||e.fname||e.filename||e.name||"",r="string"===typeof t?t:t.original_image||t.fname||t.filename||t.name||"";return n.localeCompare(r)}):S;console.log("Processing parse data:",{imageList:t.length,uploaded_files:u.length,sortedSourceList:_.length,kdc_results:v.length,kdc_plain_results:b.length,kdc_kdc_results:x.length,monkey_ocr_results:k.length,monkey_ocr_results_v2:w.length,vl_llm_results:j.length,monkey_ocr_local_results:N.length});const C=(e,t,n)=>{const r=new Map;return e.forEach(e=>{const a=t(e),l=n?n(e):null;if(a){const t=Bt(a);r.set(t,e),r.set(a,e),r.set(a.replace(/\.(pdf|png|jpg|jpeg)$/i,""),e)}if(l){const t=Bt(l);r.set(t,e),r.set(l,e),r.set(l.replace(/\.(pdf|png|jpg|jpeg)$/i,""),e)}}),r},E=C(v,e=>e.filename||e.fname,e=>e.original_image),R=C(b,e=>e.filename||e.fname,e=>e.original_image),O=C(x,e=>e.filename||e.fname,e=>e.original_image),L=C(k,e=>e.filename||e.fname,e=>e.original_image),P=C(w,e=>e.filename||e.fname,e=>e.original_image),T=C(j,e=>e.filename||e.fname,e=>e.original_image),M=C(N,e=>e.filename||e.fname,e=>e.original_image);return console.log("Created file name maps:",{kdcMap:E.size,kdcPlainMap:R.size,kdcKdcMap:O.size,monkeyOcrMap:L.size,monkeyOcrV2Map:P.size,vlLlmMap:T.size,monkeyOcrLocalMap:M.size}),_.map((e,t)=>{const n="string"===typeof e?e:e.original_image||e.fname||e.filename||e.name||"file_".concat(t),r=n,a=Bt(r),l=a;console.log("Processing file ".concat(t,":"),{imageName:n,fileName:r,baseName:a,pdfBaseName:l});const s=(e,t,n)=>{let r=e.get(t);return r||(r=e.get(n),r||(r=e.get(t+".pdf"),r||(r=e.get(n.replace(/\.(pdf)$/i,".png")),r||null)))},o=s(E,a,r),i=s(R,a,r),c=s(O,a,r),u=s(L,a,r),d=s(P,a,r),f=s(T,a,r),p=s(M,a,r);console.log("Found results for ".concat(n," (baseName: ").concat(a,"):"),{kdc:!!o,kdcPlain:!!i,kdcKdc:!!c,monkeyOCR:!!u,monkeyOCRV2:!!d,vlLLM:!!f,monkeyOCRLocal:!!p}),f&&console.log("VL LLM Result found:",f.filename,"hasResult:",!!f.result),o&&console.log("  KDC matched: ".concat(o.filename||o.fname," -> ").concat(o.original_image||"N/A")),f&&console.log("  VL LLM matched: ".concat(f.filename||f.fname," -> ").concat(f.original_image||"N/A"));const h=c&&c.features?c.features:null;return{id:"case_".concat(t),index:t+1,fileName:n,baseName:a,imageName:n,pdfFileName:a+".pdf",file:"string"===typeof e?{name:e}:e,kdcMarkdown:o||null,kdcPlain:i||null,kdcKdc:c||null,monkeyOCR:u||null,monkeyOCRV2:d||null,vlLLMResult:f||null,monkeyOCRLocal:p||null,features:{kdc:h}}})}(f,x),N=w.map((e,n)=>{const r=encodeURIComponent(e.fileName);return pe(pe({},e),{},{imagePath:"http://localhost:10000/dataset/".concat(t,"/images/").concat(r)})});console.log("\u751f\u6210\u7684\u6848\u4f8b\u5217\u8868:",N),o(N),E(N,k,j),l&&l(N),b((new Date).toLocaleTimeString()),"in_progress"===h?g||(y(!0),console.log("\ud83d\udd04 \u68c0\u6d4b\u5230\u89e3\u6790\u4efb\u52a1\u8fdb\u884c\u4e2d\uff0c\u542f\u52a8\u81ea\u52a8\u5237\u65b0")):"completed"===h&&g&&(y(!1),console.log("\u2705 \u89e3\u6790\u4efb\u52a1\u5df2\u5b8c\u6210\uff0c\u505c\u6b62\u81ea\u52a8\u5237\u65b0")),N.length>0&&n&&!r&&n(N[0])}catch(f){console.error("Failed to load cases:",f),d("\u52a0\u8f7d\u6570\u636e\u5931\u8d25: ".concat(f.message)),o([]),l&&l([]),y(!1)}finally{e&&(c(!1),p({current:0,total:0}))}},[t,n,r,l,g,k]),E=(0,a.useCallback)((e,t,n)=>{let r=e;if(t&&""!==t){const e=parseInt(t,10);isNaN(e)||(r=r.filter(t=>{var n;const r=null===(n=t.features)||void 0===n?void 0:n.kdc;if(!r||!r.textbox_count_distribution)return!1;return(r.textbox_count_distribution.max_textboxes_per_cell||0)>e}))}n&&""!==n&&"all"!==n&&(r=r.filter(e=>{var t;const r=null===(t=e.features)||void 0===t?void 0:t.kdc;if(!r||!r.table_detection)return"not_detected"===n;const a=r.table_detection.has_table;return"detected"===n?a:"not_detected"!==n||!a})),_(r)},[]);(0,a.useEffect)(()=>{if(g&&t)return x.current=setInterval(()=>{console.log("\ud83d\udd04 \u81ea\u52a8\u5237\u65b0\u89e3\u6790\u7ed3\u679c..."),C(!1)},1e4),()=>{x.current&&(clearInterval(x.current),x.current=null)}},[g,t,C]),(0,a.useEffect)(()=>()=>{x.current&&clearInterval(x.current)},[]),(0,a.useEffect)(()=>{C()},[t]);const R=()=>{C()};return t?(0,Ft.jsxs)("div",{className:"case-list",children:[(0,Ft.jsxs)("div",{className:"case-list-header",children:[(0,Ft.jsx)("h3",{children:"\u6d4b\u8bd5\u6848\u4f8b"}),(0,Ft.jsxs)("div",{className:"case-list-controls",children:[(0,Ft.jsx)("span",{className:"case-list-count",children:s.length>0?k||j?"\u7b5b\u9009\u51fa ".concat(S.length," / ").concat(s.length," \u4e2a\u6848\u4f8b"):"\u5171 ".concat(s.length," \u4e2a\u6848\u4f8b"):""}),(0,Ft.jsxs)("div",{className:"case-list-buttons",children:["in_progress"===(null===h||void 0===h?void 0:h.status)&&(0,Ft.jsx)("button",{className:"case-list-auto-refresh-btn ".concat(g?"active":""),onClick:()=>{y(!g)},title:g?"\u5173\u95ed\u81ea\u52a8\u5237\u65b0":"\u5f00\u542f\u81ea\u52a8\u5237\u65b0",children:g?"\ud83d\udd04":"\u23f8\ufe0f"}),(0,Ft.jsx)("button",{className:"case-list-refresh-btn",onClick:R,disabled:i,title:"\u624b\u52a8\u5237\u65b0",children:"\u21bb"})]})]})]}),h&&(0,Ft.jsx)("div",{className:"parse-status ".concat(h.status),children:(0,Ft.jsx)("div",{className:"parse-status-content",children:"in_progress"===h.status?(0,Ft.jsxs)(Ft.Fragment,{children:[(0,Ft.jsx)("span",{className:"status-icon",children:"\ud83d\udd04"}),(0,Ft.jsxs)("span",{className:"status-text",children:["\u89e3\u6790\u8fdb\u884c\u4e2d: ",h.current,"/",h.total,h.currentFile&&" - \u5f53\u524d\u6587\u4ef6: ".concat(h.currentFile)]}),g&&(0,Ft.jsxs)("span",{className:"auto-refresh-indicator",children:["(\u81ea\u52a8\u5237\u65b0\u4e2d ",v&&"- \u6700\u540e\u66f4\u65b0: ".concat(v),")"]})]}):"completed"===h.status?(0,Ft.jsxs)(Ft.Fragment,{children:[(0,Ft.jsx)("span",{className:"status-icon",children:"\u2705"}),(0,Ft.jsxs)("span",{className:"status-text",children:["\u89e3\u6790\u5df2\u5b8c\u6210: ",h.total,"/",h.total]})]}):(0,Ft.jsxs)(Ft.Fragment,{children:[(0,Ft.jsx)("span",{className:"status-icon",children:"\u2753"}),(0,Ft.jsx)("span",{className:"status-text",children:"\u72b6\u6001\u672a\u77e5"})]})})}),!i&&!u&&s.length>0&&(0,Ft.jsxs)("div",{className:"case-list-filter",children:[(0,Ft.jsxs)("div",{className:"filter-group",children:[(0,Ft.jsx)("label",{htmlFor:"textbox-threshold",children:"\u6587\u672c\u6846\u6570\u91cf\u9608\u503c\u8fc7\u6ee4\uff1a"}),(0,Ft.jsxs)("div",{className:"filter-input-group",children:[(0,Ft.jsx)("input",{id:"textbox-threshold",type:"number",min:"0",placeholder:"\u8f93\u5165\u9608\u503c",value:k,onChange:e=>{const t=e.target.value;w(t),E(s,t,j)},className:"filter-input"}),(0,Ft.jsx)("button",{className:"filter-clear-btn",onClick:()=>{w(""),N(""),_(s)},disabled:!k&&!j,title:"\u6e05\u9664\u8fc7\u6ee4\u5668",children:"\u2715"})]}),(0,Ft.jsx)("div",{className:"filter-help",children:"\u7b5b\u9009\u51fa\u5355\u5143\u683c\u6700\u5927\u6587\u672c\u6846\u6570\u91cf\u8d85\u8fc7\u9608\u503c\u7684\u6848\u4f8b"})]}),(0,Ft.jsxs)("div",{className:"filter-group",children:[(0,Ft.jsx)("label",{htmlFor:"table-detection-filter",children:"\u8868\u683c\u68c0\u6d4b\u8fc7\u6ee4\uff1a"}),(0,Ft.jsx)("div",{className:"filter-input-group",children:(0,Ft.jsxs)("select",{id:"table-detection-filter",value:j,onChange:e=>{const t=e.target.value;N(t),E(s,k,t)},className:"filter-select",children:[(0,Ft.jsx)("option",{value:"",children:"\u5168\u90e8\u6848\u4f8b"}),(0,Ft.jsx)("option",{value:"detected",children:"\u68c0\u6d4b\u5230\u8868\u683c"}),(0,Ft.jsx)("option",{value:"not_detected",children:"\u672a\u68c0\u6d4b\u5230\u8868\u683c"})]})}),(0,Ft.jsx)("div",{className:"filter-help",children:"\u6839\u636eKDC\u662f\u5426\u68c0\u6d4b\u5230\u8868\u683c\u6765\u7b5b\u9009\u6848\u4f8b"})]})]}),i&&(0,Ft.jsxs)("div",{className:"case-list-loading",children:[(0,Ft.jsx)("div",{className:"loading-spinner"}),(0,Ft.jsxs)("div",{className:"loading-text",children:["\u6b63\u5728\u52a0\u8f7d\u6848\u4f8b\u6570\u636e... (",f.current,"/",f.total,")"]}),(0,Ft.jsx)("div",{className:"loading-progress",children:(0,Ft.jsx)("div",{className:"loading-progress-bar",style:{width:"".concat(f.current/f.total*100,"%")}})})]}),u&&(0,Ft.jsxs)("div",{className:"case-list-error",children:[(0,Ft.jsx)("div",{className:"error-message",children:u}),(0,Ft.jsx)("button",{className:"error-retry-btn",onClick:R,children:"\u91cd\u8bd5"})]}),!i&&!u&&0===s.length&&(0,Ft.jsx)("div",{className:"case-list-empty",children:"in_progress"===(null===h||void 0===h?void 0:h.status)?"\u89e3\u6790\u6b63\u5728\u8fdb\u884c\u4e2d\uff0c\u6682\u65e0\u53ef\u663e\u793a\u7684\u6848\u4f8b":"\u5f53\u524d\u6570\u636e\u96c6\u6ca1\u6709\u627e\u5230\u6d4b\u8bd5\u6848\u4f8b"}),!i&&!u&&s.length>0&&(0,Ft.jsxs)("div",{className:"case-list-container",children:[(0,Ft.jsx)("div",{className:"case-list-stats",children:(0,Ft.jsx)(Gt,{cases:k||j?S:s})}),(0,Ft.jsx)("div",{className:"case-list-content",children:(k||j?S:s).map(e=>(0,Ft.jsx)(Yt,{caseData:e,selected:r&&r.id===e.id,onClick:()=>(e=>{n&&n(e)})(e)},e.id))}),(k||j)&&0===S.length&&(0,Ft.jsx)("div",{className:"case-list-empty",children:k&&j?"\u6ca1\u6709\u627e\u5230\u540c\u65f6\u6ee1\u8db3\u6587\u672c\u6846\u6570\u91cf\u8d85\u8fc7 ".concat(k," \u4e14\u8868\u683c\u68c0\u6d4b\u6761\u4ef6\u7684\u6848\u4f8b"):k?"\u6ca1\u6709\u627e\u5230\u5355\u5143\u683c\u6700\u5927\u6587\u672c\u6846\u6570\u91cf\u8d85\u8fc7 ".concat(k," \u7684\u6848\u4f8b"):"\u6ca1\u6709\u627e\u5230\u7b26\u5408\u8868\u683c\u68c0\u6d4b\u6761\u4ef6\u7684\u6848\u4f8b"})]})]}):(0,Ft.jsxs)("div",{className:"case-list",children:[(0,Ft.jsx)("div",{className:"case-list-header",children:(0,Ft.jsx)("h3",{children:"\u6d4b\u8bd5\u6848\u4f8b"})}),(0,Ft.jsx)("div",{className:"case-list-empty",children:"\u8bf7\u5148\u9009\u62e9\u4e00\u4e2a\u6570\u636e\u96c6"})]})},en=e=>{let{imageUrl:t,onClose:n,title:r=""}=e;const[l,s]=(0,a.useState)(1),[o,i]=(0,a.useState)({x:0,y:0}),[c,u]=(0,a.useState)(!1),[d,f]=(0,a.useState)({x:0,y:0}),p=(0,a.useCallback)(e=>{"Escape"===e.key&&n()},[n]);(0,a.useEffect)(()=>(document.addEventListener("keydown",p),document.body.style.overflow="hidden",()=>{document.removeEventListener("keydown",p),document.body.style.overflow="unset"}),[p]);const h=()=>{n()},m=(0,a.useCallback)(e=>{e.preventDefault();const t=e.deltaY>0?-.1:.1;s(e=>Math.max(.1,Math.min(5,e+t)))},[]),g=(0,a.useCallback)(e=>{l>1&&(u(!0),f({x:e.clientX-o.x,y:e.clientY-o.y}))},[l,o]),y=(0,a.useCallback)(e=>{c&&i({x:e.clientX-d.x,y:e.clientY-d.y})},[c,d]),v=(0,a.useCallback)(()=>{u(!1)},[]);return(0,a.useEffect)(()=>{if(c)return document.addEventListener("mousemove",y),document.addEventListener("mouseup",v),()=>{document.removeEventListener("mousemove",y),document.removeEventListener("mouseup",v)}},[c,y,v]),t?(0,Ft.jsx)("div",{className:"image-modal-overlay",onClick:e=>{e.target===e.currentTarget&&n()},children:(0,Ft.jsxs)("div",{className:"image-modal-content",children:[(0,Ft.jsx)("button",{className:"image-modal-close",onClick:h,title:"\u5173\u95ed (ESC)",children:"\xd7"}),r&&(0,Ft.jsx)("div",{className:"image-modal-title",children:r}),(0,Ft.jsxs)("div",{className:"image-modal-image-container",onWheel:m,onMouseDown:g,style:{cursor:l>1?c?"grabbing":"grab":"default"},children:[(0,Ft.jsx)("img",{src:t,alt:r||"\u56fe\u7247\u9884\u89c8",className:"image-modal-image",style:{transform:"scale(".concat(l,") translate(").concat(o.x/l,"px, ").concat(o.y/l,"px)"),transition:c?"none":"transform 0.1s ease-out"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"},draggable:!1}),(0,Ft.jsx)("div",{className:"image-modal-error",style:{display:"none"},children:"\u56fe\u7247\u52a0\u8f7d\u5931\u8d25"})]}),(0,Ft.jsxs)("div",{className:"image-modal-controls",children:[(0,Ft.jsxs)("div",{className:"zoom-controls",children:[(0,Ft.jsx)("button",{className:"image-modal-btn zoom-btn",onClick:()=>{s(e=>Math.max(.1,e-.2))},title:"\u7f29\u5c0f",children:"-"}),(0,Ft.jsxs)("span",{className:"zoom-level",children:[Math.round(100*l),"%"]}),(0,Ft.jsx)("button",{className:"image-modal-btn zoom-btn",onClick:()=>{s(e=>Math.min(5,e+.2))},title:"\u653e\u5927",children:"+"}),(0,Ft.jsx)("button",{className:"image-modal-btn",onClick:()=>{s(1),i({x:0,y:0})},title:"\u91cd\u7f6e\u7f29\u653e",children:"\u91cd\u7f6e"})]}),(0,Ft.jsxs)("div",{className:"action-controls",children:[(0,Ft.jsx)("button",{className:"image-modal-btn",onClick:()=>window.open(t,"_blank"),title:"\u5728\u65b0\u7a97\u53e3\u4e2d\u6253\u5f00",children:"\u65b0\u7a97\u53e3\u6253\u5f00"}),(0,Ft.jsx)("button",{className:"image-modal-btn",onClick:h,children:"\u5173\u95ed"})]})]})]})}):null},tn=e=>{let{kdcData:t,placeholder:n="\u65e0KDC\u6e32\u67d3\u7ed3\u679c"}=e;const r=(0,a.useRef)(null),l=(0,a.useRef)(null),[s,o]=(0,a.useState)(null),[i,c]=(0,a.useState)(!0),[u,d]=(0,a.useState)(!1),[f,p]=(0,a.useState)([]),[h,m]=(0,a.useState)({width:14750,height:6510}),g={textbox:{background:"rgba(135, 206, 250, ALPHA)",border:"#4169E1"},table:{background:"rgba(144, 238, 144, ALPHA)",border:"#32CD32"},component:{background:"rgba(255, 182, 193, ALPHA)",border:"#FF69B4"},unknown:{background:"rgba(255, 255, 0, ALPHA)",border:"#FFD700"}},y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(!e||"object"!==typeof e)return n;if(e.blocks&&Array.isArray(e.blocks))for(const r of e.blocks)if(r&&"object"===typeof r)if(r.textbox&&r.bounding_box){const e=r.bounding_box,a=r.textbox;if(a.blocks&&Array.isArray(a.blocks))for(const r of a.blocks)if(r.para&&r.para.runs){let a="",l=10,s=!1;for(const e of r.para.runs)e.text&&(a+=e.text,e.prop&&(e.prop.size&&(l=e.prop.size),e.prop.bold&&(s=e.prop.bold)));a.trim()&&n.push({text:a.trim(),x1:e.x1||0,y1:e.y1||0,x2:e.x2||0,y2:e.y2||0,level:t,type:"textbox",fontSize:l,isBold:s})}}else if(r.component&&r.bounding_box){const e=r.bounding_box,a=r.component;n.push({text:"image"===a.type?"\ud83d\uddbc\ufe0f Image":"\ud83d\udce6 ".concat(a.type||"Component"),x1:e.x1||0,y1:e.y1||0,x2:e.x2||0,y2:e.y2||0,level:t,type:"component",fontSize:12,isBold:!0,componentInfo:a})}else if(r.table){const e=r.table;if(e.rows&&Array.isArray(e.rows))for(const r of e.rows)if(r.cells&&Array.isArray(r.cells))for(const e of r.cells)if(e.blocks&&Array.isArray(e.blocks)){for(const r of e.blocks)y(r,t+2,n);if(e.bounding_box){const r=e.bounding_box;n.push({text:"",x1:r.x1||0,y1:r.y1||0,x2:r.x2||0,y2:r.y2||0,level:t+1,type:"table",fontSize:10,isBold:!1,isTableCell:!0})}}}else y(r,t+1,n);if(e.textbox&&e.bounding_box){const r=e.bounding_box,a=e.textbox;if(a.blocks&&Array.isArray(a.blocks))for(const e of a.blocks)if(e.para&&e.para.runs){let a="",l=10,s=!1;for(const t of e.para.runs)t.text&&(a+=t.text,t.prop&&(t.prop.size&&(l=t.prop.size),t.prop.bold&&(s=t.prop.bold)));a.trim()&&n.push({text:a.trim(),x1:r.x1||0,y1:r.y1||0,x2:r.x2||0,y2:r.y2||0,level:t,type:"textbox",fontSize:l,isBold:s})}}if(e.children&&Array.isArray(e.children))for(const r of e.children)y(r,t+1,n);return n},v=(e,t)=>{if(!e||!f.length)return;const n=e.getContext("2d");n.fillStyle="#ffffff",n.fillRect(0,0,e.width,e.height),console.log("Drawing canvas with ".concat(f.length," text blocks, scale factor: ").concat(t)),console.log("Page size: ".concat(h.width," \xd7 ").concat(h.height,", Canvas size: ").concat(e.width," \xd7 ").concat(e.height)),f.forEach((r,a)=>{const l=Math.floor(r.x1*t),s=Math.floor(r.y1*t),o=Math.floor(r.x2*t),i=Math.floor(r.y2*t),c=o-l,u=i-s;if(console.log("Block ".concat(a,': "').concat(r.text,'" [').concat(r.type,"] at (").concat(l,", ").concat(s,") size ").concat(c,"x").concat(u)),c<=0||u<=0)return void console.warn("Block ".concat(a," has invalid dimensions, skipping"));const d=Math.min(.08+.03*r.level,.3),f=((e,t)=>{const n=g[e]||g.unknown;return{background:n.background.replace("ALPHA",t),border:n.border}})(r.type||"unknown",d);if(n.fillStyle=f.background,n.fillRect(l,s,c,u),n.strokeStyle=f.border,n.lineWidth="component"===r.type?2:1,n.strokeRect(l,s,c,u),r.isTableCell)return;const p=String(r.text||"").trim();if(!p)return void console.warn("Block ".concat(a," has no text content"));let h=12;h=c<30?8:c<60?10:c<100?12:c<150?14:c<200?16:c<300?18:20;const m=Math.floor(.5*u);h=Math.min(h,m);const y=t<.1?10:8;h=Math.max(h,y),h=Math.max(8,Math.min(h,24)),console.log("Block ".concat(a,": fontSize=").concat(h,', text="').concat(p,'", type=').concat(r.type)),n.save(),n.font="".concat(r.isBold?"bold":"normal"," ").concat(h,"px Arial"),n.fillStyle="#000000",n.textAlign="left",n.textBaseline="top";const v=Math.max(4,Math.min(.1*c,.1*u)),x=l+v,k=s+v,w=Math.max(20,c-2*v),j=Math.max(h,u-2*v),N=b(n,p,w),S=h+2,_=Math.floor(j/S),C=N.slice(0,_);if(N.length>_&&C.length>0){let e=C[C.length-1];for(;n.measureText(e+"...").width>w&&e.length>1;)e=e.slice(0,-1);C[C.length-1]=e+"..."}x>=0&&k>=0&&x<e.width&&k<e.height?(C.forEach((e,t)=>{const r=k+t*S;r+h<=i-v&&(console.log("Drawing line ".concat(t,': "').concat(e,'" at (').concat(x,", ").concat(r,")")),n.fillText(e,x,r))}),console.log("Drew ".concat(C.length," lines for block ").concat(a))):console.warn("Text position (".concat(x,", ").concat(k,") is outside canvas bounds")),n.restore(),n.save(),n.font="bold 10px Arial",n.fillStyle="#333333",n.textAlign="right",n.textBaseline="bottom";const E="".concat(r.type,"#").concat(a);n.fillText(E,o-2,i-2),n.restore()}),console.log("Finished drawing text blocks")};(0,a.useEffect)(()=>{if(console.log("KdcCanvasRenderer received kdcData:",t),console.log("KdcCanvasRenderer data check:",{hasKdcData:!!t,hasData:!(!t||!t.data),isDataArray:!!(t&&t.data&&Array.isArray(t.data)),dataLength:t&&t.data?t.data.length:0}),!t||!t.data||!Array.isArray(t.data)||0===t.data.length)return console.log("KdcCanvasRenderer: Data validation failed, setting loading to false"),void c(!1);(async()=>{try{var e,n;c(!0),o(null);const r=t.data[0];if(!r||!r.doc)return o("KDC\u6587\u6863\u7ed3\u6784\u65e0\u6548"),void c(!1);const a=r.doc,l=null===(e=a.prop)||void 0===e||null===(n=e.page_props)||void 0===n?void 0:n[0];let s=14750,i=6510;l&&l.size&&(s=l.size.width,i=l.size.height),m({width:s,height:i});const u=[],d=a.tree;if(d&&d.children)for(const e of d.children)y(e,0,u);if(p(u),0===u.length)return o("KDC\u6587\u6863\u4e2d\u6ca1\u6709\u627e\u5230\u6587\u672c\u5757"),void c(!1);console.log("Found ".concat(u.length," text blocks")),c(!1)}catch(r){console.error("KDC\u6e32\u67d3\u9519\u8bef:",r),o("\u6e32\u67d3\u5931\u8d25: ".concat(r.message)),c(!1)}})()},[t]);const b=(e,t,n)=>{if(n<=0)return[t];const r=[];let a="";if(e.measureText(t).width<=n)return[t];for(let l=0;l<t.length;l++){const s=t[l],o=a+s;e.measureText(o).width>n&&a.length>0?(r.push(a),a=s):a=o}return a.length>0&&r.push(a),0===r.length&&t.length>0&&(r.push(t.substring(0,Math.max(1,Math.floor(t.length/2)))),t.length>1&&r.push(t.substring(Math.floor(t.length/2)))),r};(0,a.useEffect)(()=>{if(f.length>0&&r.current){const e=r.current,t=h.width<1e4?1e3:1400,n=h.height<1e4?700:1e3,a=Math.min(t/h.width,n/h.height),l=.05,s=Math.max(a,l),o=Math.max(800,Math.floor(h.width*s)),i=Math.max(600,Math.floor(h.height*s));e.width=o,e.height=i,e.style.width="".concat(o,"px"),e.style.height="".concat(i,"px"),v(e,s)}},[f,h]);const x=()=>{d(!0),setTimeout(()=>{if(l.current&&f.length>0){const e=l.current,t=.85*window.innerWidth,n=.75*window.innerHeight;let r,a;h.width>1e4?(r=Math.min(t,2e3),a=Math.min(n,1400)):(r=t,a=n);const s=r/h.width,o=a/h.height;let i=.95*Math.min(s,o);const c=.1,u=3;i=Math.max(c,Math.min(i,u));const d=Math.floor(h.width*i),f=Math.floor(h.height*i);console.log("Large canvas: page ".concat(h.width,"x").concat(h.height,", modal ").concat(t,"x").concat(n,", factor ").concat(i,", canvas ").concat(d,"x").concat(f)),e.width=d,e.height=f,e.style.width="".concat(d,"px"),e.style.height="".concat(f,"px"),v(e,i)}},100)},k=()=>{d(!1)};return i?(0,Ft.jsx)("div",{className:"kdc-canvas-container",children:(0,Ft.jsx)("div",{className:"loading-message",children:"\u6b63\u5728\u6e32\u67d3KDC\u6587\u6863..."})}):s?(0,Ft.jsx)("div",{className:"kdc-canvas-container",children:(0,Ft.jsxs)("div",{className:"error-message",children:["KDC\u6e32\u67d3\u9519\u8bef: ",s]})}):t&&t.data&&Array.isArray(t.data)&&0!==t.data.length?(0,Ft.jsxs)("div",{className:"kdc-canvas-container",children:[(0,Ft.jsxs)("div",{style:{border:"1px solid #ddd",borderRadius:"4px",background:"#f8f9fa",padding:"10px"},children:[(0,Ft.jsxs)("div",{style:{marginBottom:"10px",padding:"8px",background:"#ffffff",borderRadius:"4px",border:"1px solid #e0e0e0"},children:[(0,Ft.jsx)("div",{style:{fontSize:"12px",fontWeight:"bold",marginBottom:"5px",color:"#333"},children:"\u7c7b\u578b\u56fe\u4f8b\uff1a"}),(0,Ft.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",fontSize:"11px"},children:[(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.textbox.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.textbox.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u6587\u672c\u6846 (textbox)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.table.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.table.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u8868\u683c (table)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.component.background.replace("ALPHA","0.3"),border:"2px solid ".concat(g.component.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u7ec4\u4ef6 (component)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.unknown.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.unknown.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u672a\u77e5 (unknown)"})]})]})]}),(0,Ft.jsx)("canvas",{ref:r,style:{border:"1px solid #ccc",background:"white",display:"block",margin:"0 auto",cursor:"pointer",maxWidth:"100%",height:"auto"},onClick:x}),(0,Ft.jsxs)("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666",textAlign:"center"},children:["\u539f\u59cb\u5c3a\u5bf8: ",h.width," \xd7 ",h.height,(0,Ft.jsx)("br",{}),(0,Ft.jsx)("span",{style:{color:"#007bff",cursor:"pointer"},onClick:x,children:"\ud83d\udd0d \u70b9\u51fb\u56fe\u7247\u67e5\u770b\u5927\u56fe"}),(0,Ft.jsx)("br",{}),(0,Ft.jsx)("span",{style:{fontSize:"10px",color:"#999"},children:"\ud83d\udca1 \u4e0d\u540c\u989c\u8272\u4ee3\u8868\u4e0d\u540c\u7684\u6570\u636e\u7c7b\u578b\uff0c\u53f3\u4e0b\u89d2\u663e\u793a\u7c7b\u578b\u6807\u8bc6"})]})]}),u&&(0,Ft.jsx)("div",{style:{display:"block",position:"fixed",zIndex:1e3,left:0,top:0,width:"100%",height:"100%",backgroundColor:"rgba(0,0,0,0.8)",overflow:"auto"},onClick:k,children:(0,Ft.jsxs)("div",{style:{position:"relative",margin:"2% auto",width:"95%",height:"95%",background:"white",borderRadius:"8px",padding:"20px",display:"flex",flexDirection:"column"},onClick:e=>e.stopPropagation(),children:[(0,Ft.jsx)("span",{style:{position:"absolute",top:"10px",right:"15px",color:"#aaa",fontSize:"28px",fontWeight:"bold",cursor:"pointer",zIndex:1001},onClick:k,children:"\xd7"}),(0,Ft.jsx)("h3",{style:{margin:"0 0 15px 0",textAlign:"center",color:"#333"},children:"KDC \u6587\u6863\u5927\u56fe\u9884\u89c8"}),(0,Ft.jsxs)("div",{style:{marginBottom:"10px",padding:"8px",background:"#f8f9fa",borderRadius:"4px",border:"1px solid #e0e0e0"},children:[(0,Ft.jsx)("div",{style:{fontSize:"12px",fontWeight:"bold",marginBottom:"5px",color:"#333"},children:"\u7c7b\u578b\u56fe\u4f8b\uff1a"}),(0,Ft.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"15px",fontSize:"11px"},children:[(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.textbox.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.textbox.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u6587\u672c\u6846 (textbox)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.table.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.table.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u8868\u683c (table)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.component.background.replace("ALPHA","0.3"),border:"2px solid ".concat(g.component.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u7ec4\u4ef6 (component)"})]}),(0,Ft.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,Ft.jsx)("div",{style:{width:"16px",height:"16px",backgroundColor:g.unknown.background.replace("ALPHA","0.3"),border:"1px solid ".concat(g.unknown.border),borderRadius:"2px"}}),(0,Ft.jsx)("span",{children:"\u672a\u77e5 (unknown)"})]})]})]}),(0,Ft.jsx)("div",{style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",overflow:"auto",background:"#f8f9fa",borderRadius:"4px"},children:(0,Ft.jsx)("canvas",{ref:l,style:{border:"1px solid #ccc",background:"white",maxWidth:"95%",maxHeight:"95%",boxShadow:"0 2px 10px rgba(0,0,0,0.1)"}})}),(0,Ft.jsx)("div",{style:{textAlign:"center",padding:"10px 0",color:"#666",fontSize:"12px"},children:"\ud83d\udca1 \u63d0\u793a\uff1a\u4e0d\u540c\u989c\u8272\u4ee3\u8868\u4e0d\u540c\u7684\u6570\u636e\u7c7b\u578b\uff0c\u53f3\u4e0b\u89d2\u663e\u793a\u7c7b\u578b\u6807\u8bc6 (#\u5e8f\u53f7)"})]})})]}):(0,Ft.jsx)("div",{className:"kdc-canvas-container",children:(0,Ft.jsx)("div",{className:"placeholder-message",children:n})})},nn=e=>{if(!e)return e;return e.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,t,n)=>{const r=n.startsWith("http")?n:n.replace(/^\.\//,"");return'<div class="markdown-image-container"><img src="'.concat(r,'" alt="').concat(t||"\u56fe\u7247",'" class="markdown-image" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; margin: 5px; border-radius: 4px;" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" /><div class="markdown-image-error" style="display:none; color: #dc3545; font-size: 12px; padding: 5px; text-align: center;"><span>\u56fe\u7247\u52a0\u8f7d\u5931\u8d25</span><br/><small style="color: #666;">').concat(r,"</small></div></div>")})},rn=e=>{if(!e||"string"!==typeof e)return"<div style='color:gray;'>\u65e0Markdown\u5185\u5bb9</div>";try{const t=e.split("\n"),n=[];let r=[],a=!1;for(let e=0;e<t.length;e++){const l=t[e];l.trim().startsWith("|")&&l.trim().endsWith("|")?(a||(r.length>0&&(n.push(an(r.join("\n"))),r=[]),a=!0),r.push(l)):a&&""===l.trim()?(r.length>0&&(n.push(Ut(r.join("\n"))),r=[]),a=!1):a?(r.length>0&&(n.push(Ut(r.join("\n"))),r=[]),a=!1,r.push(l)):r.push(l)}return r.length>0&&(a?n.push(Ut(r.join("\n"))):n.push(an(r.join("\n")))),n.join("")}catch(t){return"<div style='color:red;'>Markdown\u89e3\u6790\u5931\u8d25: ".concat(t.message,"</div>")}},an=e=>{if(!e.trim())return"";let t=e;return t=t.replace(/^# (.+)$/gm,'<h1 style="margin: 10px 0; color: #333;">$1</h1>'),t=t.replace(/^## (.+)$/gm,'<h2 style="margin: 8px 0; color: #333;">$1</h2>'),t=t.replace(/^### (.+)$/gm,'<h3 style="margin: 6px 0; color: #333;">$1</h3>'),t=t.replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>"),t=t.replace(/\*(.+?)\*/g,"<em>$1</em>"),t=t.replace(/\n/g,"<br/>"),'<div style="margin: 10px 0; line-height: 1.5;">'.concat(t,"</div>")},ln=e=>{if(!e||"string"!==typeof e)return"<div style='color:gray;'>\u65e0LaTeX\u8868\u683c\u5185\u5bb9</div>";try{const t=e.match(/\\begin\{tabular\}\{[^}]*\}([^]*?)\\end\{tabular\}/);if(!t)return"<div style='color:gray;'>\u672a\u627e\u5230\u6709\u6548\u7684LaTeX\u8868\u683c</div>";const n=t[1].split("\\\\").map(e=>e.trim()).filter(e=>e);if(0===n.length)return"<div style='color:gray;'>LaTeX\u8868\u683c\u5185\u5bb9\u4e3a\u7a7a</div>";const r=["<table border='1' style='border-collapse: collapse; width: 100%;'>"];let a=!0,l=!1;for(let e=0;e<n.length;e++){let t=n[e];if(!t||t.startsWith("\\")||t.includes("\\multicolumn")){if(t.includes("\\multicolumn")){const e=t.match(/\\multicolumn\{[^}]*\}\{[^}]*\}\{([^}]*)\}/);e&&r.push("<caption style='font-weight: bold; padding: 8px;'>"+e[1].replace(/\\bf\s*/,"")+"</caption>")}continue}const s=t.split("&").map(e=>e.trim());0!==s.length&&(a&&!l?(r.push("<thead><tr>"),s.forEach(e=>{const t=e.replace(/\\textbf\{([^}]*)\}/g,"$1").trim();r.push("<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>".concat(t,"</th>"))}),r.push("</tr></thead><tbody>"),l=!0,a=!1):(r.push("<tr>"),s.forEach(e=>{const t=e.replace(/\\textbf\{([^}]*)\}/g,"<strong>$1</strong>").replace(/\\text\{([^}]*)\}/g,"$1").trim();r.push("<td style='border: 1px solid #ddd; padding: 8px;'>".concat(t,"</td>"))}),r.push("</tr>")))}return l&&r.push("</tbody>"),r.push("</table>"),r.join("")}catch(t){return"<div style='color:red;'>LaTeX\u8868\u683c\u89e3\u6790\u5931\u8d25: ".concat(t.message,"</div>")}},sn=e=>{if(!e||"object"!==typeof e)return"<div style='color:gray;'>\u65e0\u6548\u7684JSON\u6570\u636e</div>";if(Array.isArray(e)){if(0===e.length)return"<div style='color:gray;'>JSON\u6570\u7ec4\u4e3a\u7a7a</div>";const t=new Set;e.forEach(e=>{"object"===typeof e&&null!==e&&Object.keys(e).forEach(e=>t.add(e))});const n=Array.from(t),r=["<table border='1' style='border-collapse: collapse; width: 100%;'>"];return r.push("<thead><tr>"),n.forEach(e=>{r.push("<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>".concat(e,"</th>"))}),r.push("</tr></thead><tbody>"),e.forEach(e=>{r.push("<tr>"),n.forEach(t=>{const n=e&&"object"===typeof e&&e[t]||"";r.push("<td style='border: 1px solid #ddd; padding: 8px;'>".concat(n,"</td>"))}),r.push("</tr>")}),r.push("</tbody></table>"),r.join("")}{const t=["<table border='1' style='border-collapse: collapse; width: 100%;'>"];return t.push("<thead><tr><th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>\u5c5e\u6027</th><th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>\u503c</th></tr></thead><tbody>"),Object.entries(e).forEach(e=>{let[n,r]=e;t.push("<tr>"),t.push("<td style='border: 1px solid #ddd; padding: 8px;'>".concat(n,"</td>")),t.push("<td style='border: 1px solid #ddd; padding: 8px;'>".concat("object"===typeof r?JSON.stringify(r):r,"</td>")),t.push("</tr>")}),t.push("</tbody></table>"),t.join("")}},on=e=>{let{content:t,type:n="markdown",className:r="",placeholder:a="\u65e0\u8868\u683c\u5185\u5bb9"}=e;const l=!t||"string"===typeof t&&""===t.trim()||"object"===typeof t&&"kdc"===n&&(!t.data||!Array.isArray(t.data)||0===t.data.length);if(console.log("TableRenderer isEmpty check:",{content:t,type:n,isEmpty:l,hasContent:!!t,isKdc:"kdc"===n,hasData:!(!t||!t.data),isDataArray:!!(t&&t.data&&Array.isArray(t.data)),dataLength:t&&t.data?t.data.length:0}),l)return(0,Ft.jsx)("div",{className:"table-renderer table-renderer-empty ".concat(r),children:(0,Ft.jsx)("div",{className:"empty-message",children:a})});return"kdc"===n?(console.log("TableRenderer KDC Debug:",{content:t,type:n,isEmpty:l}),(0,Ft.jsx)("div",{className:"table-renderer ".concat(r),children:(0,Ft.jsx)(tn,{kdcData:t,placeholder:a})})):(0,Ft.jsx)("div",{className:"table-renderer ".concat(r),children:(0,Ft.jsx)("div",{className:"table-renderer-content",dangerouslySetInnerHTML:{__html:(()=>{switch(n){case"markdown":const n=nn(t);return rn(n);case"latex":return ln(t);case"html":if("string"===typeof t){if(t.includes("\u8d85\u65f6")||t.includes("Please upload")||t.includes("upload"))return'<div style="color: #dc3545; padding: 10px; text-align: center; border: 1px solid #dc3545; border-radius: 4px; background-color: #f8d7da;">'.concat(t,"</div>");if(t.includes("<table")||t.includes("<div")||t.includes("<html")){const e="\n              <style>\n                body {\n                  margin: 0;\n                  padding: 10px;\n                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n                  font-size: 14px;\n                  line-height: 1.5;\n                }\n                table {\n                  width: 100%;\n                  border-collapse: collapse;\n                  margin: 0;\n                  border: 1px solid #dee2e6;\n                }\n                th, td {\n                  border: 1px solid #dee2e6;\n                  padding: 8px 12px;\n                  text-align: left;\n                  vertical-align: top;\n                  word-wrap: break-word;\n                }\n                th {\n                  background-color: #f8f9fa;\n                  font-weight: 600;\n                  color: #333;\n                }\n                tbody tr:nth-child(even) {\n                  background-color: #f9f9f9;\n                }\n                tbody tr:hover {\n                  background-color: #e3f2fd;\n                }\n                /* \u786e\u4fdd\u6240\u6709\u8868\u683c\u90fd\u6709\u8fb9\u6846\uff0c\u5305\u62ec\u52a8\u6001\u751f\u6210\u7684 */\n                table, table * {\n                  border-collapse: collapse !important;\n                }\n                table th, table td {\n                  border: 1px solid #dee2e6 !important;\n                }\n              </style>\n            ";let n=t;n=t.includes("<html")?t.includes("<head>")?t.replace("<head>","<head>".concat(e)):t.includes("<html>")?t.replace("<html>","<html><head>".concat(e,"</head>")):"<!DOCTYPE html><html><head>".concat(e,"</head><body>").concat(t,"</body></html>"):"<!DOCTYPE html><html><head>".concat(e,"</head><body>").concat(t,"</body></html>");const r=n.replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),a="iframe_"+Math.random().toString(36).substr(2,9);return'\n              <iframe\n                id="'.concat(a,'"\n                srcdoc="').concat(r,'"\n                style="width:100%;height:auto;min-height:800px;border:1px solid #ddd;border-radius:3px;background:white;"\n                sandbox="allow-scripts"\n                onload="\n                  const iframe = this;\n                  setTimeout(() => {\n                    try {\n                      const doc = iframe.contentDocument || iframe.contentWindow.document;\n                      const height = Math.max(doc.body.scrollHeight, doc.documentElement.scrollHeight);\n                      iframe.style.height = Math.min(height + 20, 2000) + \'px\';\n                    } catch(e) {\n                      iframe.style.height = \'800px\';\n                    }\n                  }, 200);\n                "\n              ></iframe>\n            ')}return'<pre style="white-space: pre-wrap; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">'.concat(t,"</pre>")}return t;case"json":try{const e="string"===typeof t?JSON.parse(t):t;return sn(e)}catch(e){return'<div class="error">JSON\u89e3\u6790\u5931\u8d25: '.concat(e.message,"</div>")}case"plain":return'<pre class="plain-text">'.concat(t,"</pre>");case"kdc":return null;default:if("string"===typeof t){if(t.includes("\\begin{tabular}"))return ln(t);if(t.includes("|")&&t.includes("-")){const e=nn(t);return Ut(e)}}const r=nn(t);return Ut(r)}})()}})})},cn=e=>{let{data:t,level:n,keyName:r,isRoot:l=!1}=e;const[s,o]=(0,a.useState)(n>2),i=20*n,c=()=>{o(!s)},u=function(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(null===e)return(0,Ft.jsx)("span",{className:"json-null",children:"null"});if("boolean"===typeof e)return(0,Ft.jsx)("span",{className:"json-boolean",children:e.toString()});if("number"===typeof e)return(0,Ft.jsx)("span",{className:"json-number",children:e});if("string"===typeof e)return(0,Ft.jsxs)("span",{className:"json-string",children:['"',e,'"']});if(Array.isArray(e))return 0===e.length?(0,Ft.jsx)("span",{className:"json-array-empty",children:"[]"}):(0,Ft.jsxs)("div",{className:"json-array",children:[(0,Ft.jsxs)("div",{className:"json-node-header",style:{marginLeft:"".concat(i,"px")},children:[(0,Ft.jsx)("button",{className:"json-node-toggle",onClick:c,children:(0,Ft.jsx)("span",{className:"json-arrow ".concat(s?"collapsed":"expanded"),children:"\u25b6"})}),(0,Ft.jsx)("span",{className:"json-bracket",children:"["}),(0,Ft.jsxs)("span",{className:"json-array-length",children:["(",e.length," items)"]}),s&&(0,Ft.jsx)("span",{className:"json-bracket",children:"]"})]}),!s&&(0,Ft.jsxs)("div",{className:"json-array-content",children:[e.map((e,n)=>(0,Ft.jsx)("div",{className:"json-array-item",children:(0,Ft.jsxs)("div",{style:{marginLeft:"".concat(i+20,"px")},children:[(0,Ft.jsxs)("span",{className:"json-array-index",children:["[",n,"]:"]}),u(e,n.toString(),t+1)]})},n)),(0,Ft.jsx)("div",{style:{marginLeft:"".concat(i,"px")},children:(0,Ft.jsx)("span",{className:"json-bracket",children:"]"})})]})]});if("object"===typeof e&&null!==e){const n=Object.keys(e);return 0===n.length?(0,Ft.jsx)("span",{className:"json-object-empty",children:"{}"}):(0,Ft.jsxs)("div",{className:"json-object",children:[(0,Ft.jsxs)("div",{className:"json-node-header",style:{marginLeft:"".concat(i,"px")},children:[(0,Ft.jsx)("button",{className:"json-node-toggle",onClick:c,children:(0,Ft.jsx)("span",{className:"json-arrow ".concat(s?"collapsed":"expanded"),children:"\u25b6"})}),(0,Ft.jsx)("span",{className:"json-bracket",children:"{"}),(0,Ft.jsxs)("span",{className:"json-object-length",children:["(",n.length," keys)"]}),s&&(0,Ft.jsx)("span",{className:"json-bracket",children:"}"})]}),!s&&(0,Ft.jsxs)("div",{className:"json-object-content",children:[n.map((r,a)=>(0,Ft.jsx)("div",{className:"json-object-item",children:(0,Ft.jsxs)("div",{style:{marginLeft:"".concat(i+20,"px")},children:[(0,Ft.jsxs)("span",{className:"json-key",children:['"',r,'"']}),(0,Ft.jsx)("span",{className:"json-colon",children:": "}),u(e[r],r,t+1),a<n.length-1&&(0,Ft.jsx)("span",{className:"json-comma",children:","})]})},r)),(0,Ft.jsx)("div",{style:{marginLeft:"".concat(i,"px")},children:(0,Ft.jsx)("span",{className:"json-bracket",children:"}"})})]})]})}return(0,Ft.jsx)("span",{className:"json-unknown",children:String(e)})};return l?u(t,r,n):(0,Ft.jsx)("div",{className:"json-node",style:{marginLeft:"".concat(i,"px")},children:u(t,r,n)})},un=e=>{let{data:t,placeholder:n="\u65e0JSON\u6570\u636e",collapsed:r=!0}=e;const[l,s]=(0,a.useState)(r),o=(0,a.useMemo)(()=>{if(!t)return null;try{return"object"===typeof t?t:"string"===typeof t?JSON.parse(t):null}catch(e){return console.error("JSON\u89e3\u6790\u5931\u8d25:",e),null}},[t]);return o?(0,Ft.jsxs)("div",{className:"json-viewer",children:[(0,Ft.jsx)("div",{className:"json-viewer-header",children:(0,Ft.jsxs)("button",{className:"json-viewer-toggle",onClick:()=>{s(!l)},title:l?"\u5c55\u5f00JSON":"\u6536\u7f29JSON",children:[(0,Ft.jsx)("span",{className:"json-viewer-arrow ".concat(l?"collapsed":"expanded"),children:"\u25b6"}),"JSON\u6570\u636e (",l?"\u5df2\u6536\u7f29":"\u5df2\u5c55\u5f00",")"]})}),!l&&(0,Ft.jsx)("div",{className:"json-viewer-content",children:(0,Ft.jsx)(cn,{data:o,level:0,keyName:"",isRoot:!0})})]}):(0,Ft.jsx)("div",{className:"json-viewer json-viewer-empty",children:(0,Ft.jsx)("div",{className:"json-viewer-placeholder",children:n})})},dn=e=>{let{distributionData:t,className:n=""}=e;const r=(0,a.useMemo)(()=>{if(!t||!t.cell_textbox_counts)return null;const{cell_textbox_counts:e,total_cells:n,max_textboxes_per_cell:r,avg_textboxes_per_cell:a,outlier_cells:l}=t,s=[],o=Math.max(...Object.keys(e).map(Number));for(let t=1;t<=o;t++){const r=e[t]||0;r>0&&s.push({textboxCount:t,cellCount:r,percentage:(r/n*100).toFixed(1)})}return{dataPoints:s,total_cells:n,max_textboxes_per_cell:r,avg_textboxes_per_cell:a,outlierThreshold:2*a,hasOutliers:l&&l.length>0,outlier_cells:l}},[t]);if(!r)return(0,Ft.jsx)("div",{className:"textbox-distribution-chart ".concat(n),children:(0,Ft.jsx)("div",{className:"chart-placeholder",children:"\u6682\u65e0\u6587\u672c\u6846\u5206\u5e03\u6570\u636e"})});const{dataPoints:l,total_cells:s,max_textboxes_per_cell:o,avg_textboxes_per_cell:i,outlierThreshold:c,hasOutliers:u,outlier_cells:d}=r,f=200,p=20,h=40,m=400-h-20,g=f-p-40,y=Math.max(...l.map(e=>e.cellCount)),v=e=>(e-1)/(o-1)*m,b=e=>g-e/y*g;return(0,Ft.jsxs)("div",{className:"textbox-distribution-chart ".concat(n),children:[(0,Ft.jsxs)("div",{className:"chart-header",children:[(0,Ft.jsx)("h4",{children:"\ud83d\udcca \u5355\u5143\u683c\u6587\u672c\u6846\u6570\u91cf\u5206\u5e03"}),(0,Ft.jsxs)("div",{className:"chart-summary",children:[(0,Ft.jsxs)("span",{children:["\u603b\u5355\u5143\u683c: ",s]}),(0,Ft.jsxs)("span",{children:["\u5e73\u5747\u6587\u672c\u6846\u6570: ",i]}),(0,Ft.jsxs)("span",{children:["\u6700\u5927\u6587\u672c\u6846\u6570: ",o]})]})]}),(0,Ft.jsxs)("div",{className:"chart-container",children:[(0,Ft.jsxs)("svg",{width:400,height:f,style:{border:"1px solid #e0e0e0",backgroundColor:"#fafafa"},children:[(0,Ft.jsxs)("g",{transform:"translate(".concat(h,", ").concat(p,")"),children:[(0,Ft.jsx)("line",{x1:0,y1:0,x2:0,y2:g,stroke:"#666",strokeWidth:1}),(0,Ft.jsx)("line",{x1:0,y1:g,x2:m,y2:g,stroke:"#666",strokeWidth:1}),[0,Math.ceil(y/4),Math.ceil(y/2),Math.ceil(3*y/4),y].map((e,t)=>{const n=b(e);return(0,Ft.jsxs)("g",{children:[(0,Ft.jsx)("line",{x1:-5,y1:n,x2:0,y2:n,stroke:"#666",strokeWidth:1}),(0,Ft.jsx)("text",{x:-8,y:n+3,textAnchor:"end",fontSize:"10",fill:"#666",children:e})]},t)}),l.map((e,t)=>{const n=v(e.textboxCount);return(0,Ft.jsxs)("g",{children:[(0,Ft.jsx)("line",{x1:n,y1:g,x2:n,y2:g+5,stroke:"#666",strokeWidth:1}),(0,Ft.jsx)("text",{x:n,y:g+18,textAnchor:"middle",fontSize:"10",fill:"#666",children:e.textboxCount})]},t)}),l.map((e,t)=>{const n=v(e.textboxCount),r=b(e.cellCount),a=g-r,l=e.textboxCount>c;return(0,Ft.jsxs)("g",{children:[(0,Ft.jsx)("rect",{x:n-8,y:r,width:16,height:a,fill:l?"#ff6b6b":"#4dabf7",stroke:l?"#e03131":"#339af0",strokeWidth:1,opacity:.8}),(0,Ft.jsx)("text",{x:n,y:r-5,textAnchor:"middle",fontSize:"9",fill:"#333",fontWeight:"bold",children:e.cellCount}),(0,Ft.jsxs)("text",{x:n,y:r-15,textAnchor:"middle",fontSize:"8",fill:"#666",children:[e.percentage,"%"]})]},t)}),i>1&&(0,Ft.jsx)("line",{x1:v(i),y1:0,x2:v(i),y2:g,stroke:"#28a745",strokeWidth:2,strokeDasharray:"5,5",opacity:.7}),c>1&&c<=o&&(0,Ft.jsx)("line",{x1:v(c),y1:0,x2:v(c),y2:g,stroke:"#dc3545",strokeWidth:2,strokeDasharray:"3,3",opacity:.7})]}),(0,Ft.jsx)("text",{x:200,y:195,textAnchor:"middle",fontSize:"12",fill:"#333",children:"\u6bcf\u4e2a\u5355\u5143\u683c\u5185\u7684\u6587\u672c\u6846\u6570\u91cf"}),(0,Ft.jsx)("text",{x:15,y:100,textAnchor:"middle",fontSize:"12",fill:"#333",transform:"rotate(-90, 15, ".concat(100,")"),children:"\u5355\u5143\u683c\u6570\u91cf"})]}),(0,Ft.jsxs)("div",{className:"chart-legend",children:[(0,Ft.jsxs)("div",{className:"legend-item",children:[(0,Ft.jsx)("div",{className:"legend-color",style:{backgroundColor:"#4dabf7"}}),(0,Ft.jsx)("span",{children:"\u6b63\u5e38\u5355\u5143\u683c"})]}),u&&(0,Ft.jsxs)("div",{className:"legend-item",children:[(0,Ft.jsx)("div",{className:"legend-color",style:{backgroundColor:"#ff6b6b"}}),(0,Ft.jsxs)("span",{children:["\u5f02\u5e38\u5355\u5143\u683c (\u6587\u672c\u6846\u6570\u91cf > ",c.toFixed(1),")"]})]}),(0,Ft.jsxs)("div",{className:"legend-item",children:[(0,Ft.jsx)("div",{className:"legend-line",style:{borderTop:"2px dashed #28a745"}}),(0,Ft.jsxs)("span",{children:["\u5e73\u5747\u503c (",i,")"]})]}),c>1&&c<=o&&(0,Ft.jsxs)("div",{className:"legend-item",children:[(0,Ft.jsx)("div",{className:"legend-line",style:{borderTop:"2px dashed #dc3545"}}),(0,Ft.jsxs)("span",{children:["\u5f02\u5e38\u9608\u503c (",c.toFixed(1),")"]})]})]})]}),u&&(0,Ft.jsxs)("div",{className:"outlier-detection",children:[(0,Ft.jsx)("h5",{children:"\ud83d\udea8 \u5f02\u5e38\u68c0\u6d4b\u7ed3\u679c"}),(0,Ft.jsxs)("p",{children:["\u68c0\u6d4b\u5230 ",d.length," \u79cd\u5f02\u5e38\u60c5\u51b5\uff1a"]}),(0,Ft.jsx)("ul",{children:d.map((e,t)=>(0,Ft.jsxs)("li",{children:["\u5305\u542b ",(0,Ft.jsx)("strong",{children:e.textbox_count})," \u4e2a\u6587\u672c\u6846\u7684\u5355\u5143\u683c \uff08\u8d85\u8fc7\u9608\u503c ",e.threshold.toFixed(1),"\uff09"]},t))}),(0,Ft.jsx)("p",{className:"outlier-note",children:"\ud83d\udca1 \u8fd9\u4e9b\u5355\u5143\u683c\u53ef\u80fd\u5305\u542b\u590d\u6742\u7684\u5185\u5bb9\u7ed3\u6784\uff0c\u9700\u8981\u7279\u522b\u5173\u6ce8\u89e3\u6790\u8d28\u91cf\u3002"})]}),(0,Ft.jsx)("style",{jsx:!0,children:"\n        .textbox-distribution-chart {\n          background: white;\n          border: 1px solid #e0e0e0;\n          border-radius: 8px;\n          padding: 16px;\n          margin: 16px 0;\n        }\n\n        .chart-header h4 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 16px;\n        }\n\n        .chart-summary {\n          display: flex;\n          gap: 16px;\n          margin-bottom: 16px;\n          font-size: 12px;\n          color: #666;\n        }\n\n        .chart-summary span {\n          background: #f8f9fa;\n          padding: 4px 8px;\n          border-radius: 4px;\n          border: 1px solid #e9ecef;\n        }\n\n        .chart-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n\n        .chart-legend {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 12px;\n          margin-top: 12px;\n          font-size: 11px;\n        }\n\n        .legend-item {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .legend-color {\n          width: 12px;\n          height: 12px;\n          border-radius: 2px;\n        }\n\n        .legend-line {\n          width: 20px;\n          height: 1px;\n        }\n\n        .outlier-detection {\n          margin-top: 16px;\n          padding: 12px;\n          background: #fff3cd;\n          border: 1px solid #ffeaa7;\n          border-radius: 6px;\n        }\n\n        .outlier-detection h5 {\n          margin: 0 0 8px 0;\n          color: #856404;\n          font-size: 14px;\n        }\n\n        .outlier-detection p {\n          margin: 4px 0;\n          color: #856404;\n          font-size: 12px;\n        }\n\n        .outlier-detection ul {\n          margin: 8px 0;\n          padding-left: 20px;\n        }\n\n        .outlier-detection li {\n          color: #856404;\n          font-size: 12px;\n          margin: 2px 0;\n        }\n\n        .outlier-note {\n          font-style: italic;\n          margin-top: 8px !important;\n        }\n\n        .chart-placeholder {\n          text-align: center;\n          color: #999;\n          padding: 40px;\n          font-style: italic;\n        }\n      "})]})};function fn(e,t){return e<=1&&t<=1?"low":e<=3&&t<=2?"medium":"high"}function pn(e,t){switch(fn(e,t)){case"low":return"\u7b80\u5355";case"medium":return"\u4e2d\u7b49";case"high":return"\u590d\u6742";default:return"\u672a\u77e5"}}function hn(e,t,n){const r=fn(e,t),a=(n.kdc_vs_monkey_ocr+n.kdc_vs_monkey_ocr_latex)/2;return"high"===r?a<.7?"\u9ad8\u590d\u6742\u5ea6\u8868\u683c\u663e\u8457\u5f71\u54cd\u89e3\u6790\u51c6\u786e\u7387\uff0c\u5efa\u8bae\u4f18\u5316\u89e3\u6790\u7b56\u7565":"\u5c3d\u7ba1\u8868\u683c\u590d\u6742\u5ea6\u8f83\u9ad8\uff0c\u4f46\u89e3\u6790\u5668\u8868\u73b0\u826f\u597d":"medium"===r?a<.8?"\u4e2d\u7b49\u590d\u6742\u5ea6\u8868\u683c\u5bf9\u51c6\u786e\u7387\u6709\u4e00\u5b9a\u5f71\u54cd":"\u4e2d\u7b49\u590d\u6742\u5ea6\u8868\u683c\uff0c\u89e3\u6790\u5668\u5904\u7406\u6548\u679c\u826f\u597d":"\u7b80\u5355\u8868\u683c\uff0c\u89e3\u6790\u5668\u901a\u5e38\u80fd\u83b7\u5f97\u8f83\u597d\u7684\u51c6\u786e\u7387"}const mn=e=>{var t;let{caseData:n,className:r=""}=e;const l=(0,a.useMemo)(()=>{var e,t;if(!n||null===(e=n.features)||void 0===e||null===(t=e.kdc)||void 0===t||!t.accuracy_metrics)return null;const r=n.features.kdc.accuracy_metrics;return{accuracy:r,textboxDistribution:n.features.kdc.textbox_count_distribution,hasData:r.has_comparison_data}},[n]);if(!l||!l.hasData)return(0,Ft.jsxs)("div",{className:"metrics-panel ".concat(r),children:[(0,Ft.jsx)("div",{className:"metrics-header",children:(0,Ft.jsx)("h4",{children:"\ud83d\udcca \u6307\u6807\u4fe1\u606f"})}),(0,Ft.jsx)("div",{className:"metrics-placeholder",children:"\u6682\u65e0\u51c6\u786e\u7387\u6bd4\u8f83\u6570\u636e"})]});const{accuracy:s,textboxDistribution:o}=l,i=(100*s.kdc_vs_monkey_ocr).toFixed(1),c=(100*s.kdc_vs_monkey_ocr_latex).toFixed(1),u=(100*s.monkey_ocr_vs_monkey_ocr_latex).toFixed(1),d=(null===o||void 0===o?void 0:o.max_textboxes_per_cell)||0,f=(null===o||void 0===o?void 0:o.avg_textboxes_per_cell)||0,p=(null===o||void 0===o?void 0:o.total_cells)||0,h=(null===o||void 0===o||null===(t=o.outlier_cells)||void 0===t?void 0:t.length)>0;return(0,Ft.jsxs)("div",{className:"metrics-panel ".concat(r),children:[(0,Ft.jsx)("div",{className:"metrics-header",children:(0,Ft.jsx)("h4",{children:"\ud83d\udcca \u6307\u6807\u4fe1\u606f"})}),(0,Ft.jsxs)("div",{className:"metrics-content",children:[(0,Ft.jsxs)("div",{className:"metrics-section",children:[(0,Ft.jsx)("h5",{children:"\u51c6\u786e\u7387\u6bd4\u8f83"}),(0,Ft.jsxs)("div",{className:"accuracy-comparison",children:[(0,Ft.jsxs)("div",{className:"comparison-item",children:[(0,Ft.jsx)("div",{className:"comparison-label",children:"KDC vs MonkeyOCR (table)"}),(0,Ft.jsxs)("div",{className:"comparison-value",children:[(0,Ft.jsx)("div",{className:"accuracy-bar",children:(0,Ft.jsx)("div",{className:"accuracy-fill",style:{width:"".concat(i,"%")}})}),(0,Ft.jsxs)("span",{className:"accuracy-text",children:[i,"%"]})]})]}),(0,Ft.jsxs)("div",{className:"comparison-item",children:[(0,Ft.jsx)("div",{className:"comparison-label",children:"KDC vs MonkeyOCR (parse)"}),(0,Ft.jsxs)("div",{className:"comparison-value",children:[(0,Ft.jsx)("div",{className:"accuracy-bar",children:(0,Ft.jsx)("div",{className:"accuracy-fill",style:{width:"".concat(c,"%")}})}),(0,Ft.jsxs)("span",{className:"accuracy-text",children:[c,"%"]})]})]}),(0,Ft.jsxs)("div",{className:"comparison-item",children:[(0,Ft.jsx)("div",{className:"comparison-label",children:"MonkeyOCR (table) vs (parse)"}),(0,Ft.jsxs)("div",{className:"comparison-value",children:[(0,Ft.jsx)("div",{className:"accuracy-bar",children:(0,Ft.jsx)("div",{className:"accuracy-fill",style:{width:"".concat(u,"%")}})}),(0,Ft.jsxs)("span",{className:"accuracy-text",children:[u,"%"]})]})]})]})]}),(0,Ft.jsxs)("div",{className:"metrics-section",children:[(0,Ft.jsx)("h5",{children:"\u8868\u683c\u590d\u6742\u5ea6"}),(0,Ft.jsxs)("div",{className:"complexity-metrics",children:[(0,Ft.jsxs)("div",{className:"metric-item",children:[(0,Ft.jsx)("div",{className:"metric-label",children:"\u603b\u5355\u5143\u683c\u6570"}),(0,Ft.jsx)("div",{className:"metric-value",children:p})]}),(0,Ft.jsxs)("div",{className:"metric-item",children:[(0,Ft.jsx)("div",{className:"metric-label",children:"\u5e73\u5747\u6587\u672c\u6846\u6570/\u5355\u5143\u683c"}),(0,Ft.jsx)("div",{className:"metric-value",children:f})]}),(0,Ft.jsxs)("div",{className:"metric-item",children:[(0,Ft.jsx)("div",{className:"metric-label",children:"\u6700\u5927\u6587\u672c\u6846\u6570/\u5355\u5143\u683c"}),(0,Ft.jsx)("div",{className:"metric-value",children:d})]}),(0,Ft.jsxs)("div",{className:"metric-item",children:[(0,Ft.jsx)("div",{className:"metric-label",children:"\u590d\u6742\u5ea6\u7b49\u7ea7"}),(0,Ft.jsx)("div",{className:"metric-value complexity-level ".concat(fn(d,f)),children:pn(d,f)})]})]})]}),h&&(0,Ft.jsxs)("div",{className:"metrics-section",children:[(0,Ft.jsx)("h5",{children:"\ud83d\udea8 \u5f02\u5e38\u68c0\u6d4b"}),(0,Ft.jsxs)("div",{className:"outlier-info",children:[(0,Ft.jsx)("p",{children:"\u68c0\u6d4b\u5230\u5305\u542b\u5f02\u5e38\u591a\u6587\u672c\u6846\u7684\u5355\u5143\u683c\uff0c\u53ef\u80fd\u5f71\u54cd\u89e3\u6790\u51c6\u786e\u7387\uff1a"}),(0,Ft.jsx)("ul",{children:o.outlier_cells.map((e,t)=>(0,Ft.jsxs)("li",{children:[e.textbox_count," \u4e2a\u6587\u672c\u6846 (\u8d85\u8fc7\u9608\u503c ",e.threshold.toFixed(1),")"]},t))})]})]}),(0,Ft.jsxs)("div",{className:"metrics-section",children:[(0,Ft.jsx)("h5",{children:"\u76f8\u5173\u6027\u5206\u6790"}),(0,Ft.jsx)("div",{className:"correlation-analysis",children:(0,Ft.jsxs)("div",{className:"analysis-item",children:[(0,Ft.jsx)("div",{className:"analysis-label",children:"\u590d\u6742\u5ea6\u5bf9\u51c6\u786e\u7387\u7684\u5f71\u54cd"}),(0,Ft.jsx)("div",{className:"analysis-value",children:hn(d,f,s)})]})})]})]}),(0,Ft.jsx)("style",{jsx:!0,children:"\n        .metrics-panel {\n          background: white;\n          border: 1px solid #e0e0e0;\n          border-radius: 8px;\n          padding: 16px;\n          height: fit-content;\n        }\n\n        .metrics-header h4 {\n          margin: 0 0 16px 0;\n          color: #333;\n          font-size: 16px;\n        }\n\n        .metrics-placeholder {\n          text-align: center;\n          color: #999;\n          padding: 40px;\n          font-style: italic;\n        }\n\n        .metrics-content {\n          display: flex;\n          flex-direction: column;\n          gap: 20px;\n        }\n\n        .metrics-section h5 {\n          margin: 0 0 12px 0;\n          color: #555;\n          font-size: 14px;\n          font-weight: 600;\n        }\n\n        .accuracy-comparison {\n          display: flex;\n          flex-direction: column;\n          gap: 12px;\n        }\n\n        .comparison-item {\n          display: flex;\n          flex-direction: column;\n          gap: 4px;\n        }\n\n        .comparison-label {\n          font-size: 12px;\n          color: #666;\n          font-weight: 500;\n        }\n\n        .comparison-value {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n        }\n\n        .accuracy-bar {\n          flex: 1;\n          height: 8px;\n          background: #e9ecef;\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .accuracy-fill {\n          height: 100%;\n          background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);\n          transition: width 0.3s ease;\n        }\n\n        .accuracy-text {\n          font-size: 12px;\n          font-weight: 600;\n          color: #333;\n          min-width: 40px;\n          text-align: right;\n        }\n\n        .complexity-metrics {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 12px;\n        }\n\n        .metric-item {\n          display: flex;\n          flex-direction: column;\n          gap: 4px;\n        }\n\n        .metric-label {\n          font-size: 11px;\n          color: #666;\n        }\n\n        .metric-value {\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .complexity-level.low {\n          color: #28a745;\n        }\n\n        .complexity-level.medium {\n          color: #ffc107;\n        }\n\n        .complexity-level.high {\n          color: #dc3545;\n        }\n\n        .outlier-info {\n          font-size: 12px;\n          color: #856404;\n          background: #fff3cd;\n          padding: 12px;\n          border-radius: 6px;\n          border: 1px solid #ffeaa7;\n        }\n\n        .outlier-info ul {\n          margin: 8px 0 0 0;\n          padding-left: 20px;\n        }\n\n        .outlier-info li {\n          margin: 4px 0;\n        }\n\n        .correlation-analysis {\n          display: flex;\n          flex-direction: column;\n          gap: 8px;\n        }\n\n        .analysis-item {\n          display: flex;\n          flex-direction: column;\n          gap: 4px;\n        }\n\n        .analysis-label {\n          font-size: 12px;\n          color: #666;\n          font-weight: 500;\n        }\n\n        .analysis-value {\n          font-size: 12px;\n          color: #333;\n          background: #f8f9fa;\n          padding: 8px;\n          border-radius: 4px;\n          border-left: 3px solid #007bff;\n        }\n      "})]})},gn=(0,a.memo)(e=>{var t,n,r,l,s,o,i,c,u,d,f,p,h,m;let{caseData:g}=e;const[y,v]=(0,a.useState)(!1),[b,x]=(0,a.useState)(null),[k,w]=(0,a.useState)(null),[j,N]=(0,a.useState)(!0);if((0,a.useEffect)(()=>{(async()=>{if(g&&g.fileName)try{let e="test20";const t=localStorage.getItem("selectedDataset");t&&(e=t);const n=new URLSearchParams(window.location.search).get("dataset");n&&(e=n);const r=await zt(e),a=r.find(e=>e.image_filename===g.fileName);console.log("\u67e5\u627e\u6807\u6ce8\u6570\u636e:",{datasetName:e,fileName:g.fileName,totalAnnotations:r.length,annotationFiles:r.map(e=>e.image_filename)}),a?(w(a),console.log("\u2705 \u627e\u5230\u6807\u6ce8\u6570\u636e:",a)):console.log("\u274c \u672a\u627e\u5230\u5339\u914d\u7684\u6807\u6ce8\u6570\u636e:",g.fileName)}catch(e){console.error("\u83b7\u53d6\u6807\u6ce8\u6570\u636e\u5931\u8d25:",e)}})()},[g]),!g)return(0,Ft.jsx)("div",{className:"case-detail",children:(0,Ft.jsx)("div",{className:"case-detail-empty",children:"\u8bf7\u9009\u62e9\u4e00\u4e2a\u6d4b\u8bd5\u6848\u4f8b\u67e5\u770b\u8be6\u60c5"})});const{index:S,fileName:_,baseName:C,dataset:E,hasImage:R,imagePath:O,kdcMarkdown:L,kdcPlain:P,kdcKdc:T,monkeyOCR:M,monkeyOCRV2:A,vlLLMResult:D,monkeyOCRLocal:z,features:F}=g,I=O,U={kdcMarkdown:L?{filename:L.filename,hasData:!!L.data}:null,kdcPlain:P?{filename:P.filename,hasData:!!P.data}:null,kdcKdc:T?{filename:T.filename,hasData:!!T.data}:null,monkeyOCR:M?{filename:M.filename,hasResult:!!M.result}:null,monkeyOCRV2:A?{filename:A.filename,hasResult:!!A.result}:null,vlLLM:D?{filename:D.filename,originalImage:D.original_image,hasResult:!!D.result}:null,monkeyOCRLocal:z?{filename:z.filename,originalImage:z.original_image,hasResult:!!z.result}:null},B=Jt(L,"markdown"),V=Jt(P,"plain"),W=Jt(T,"kdc"),K=Jt(M,"html"),H=Jt(A,"html"),$=Jt(D,"vl_llm"),q=Jt(z,"markdown"),Q=Xt(L,"markdown"),J=Xt(P,"plain"),X=Xt(T,"kdc"),Y=Xt(M,"html"),G=Xt(A,"html"),Z=Xt(D,"vl_llm"),ee=Xt(z,(null===z||void 0===z||null===(t=z.result)||void 0===t?void 0:t.type)||"markdown");console.log("KDC Render Data Debug:",{kdcKdc:T,kdcKdcRenderData:X,kdcKdcType:typeof X,kdcKdcHasData:!(!X||!X.data)}),console.log("CaseDetail Debug:",{fileName:_,dataset:E,hasImage:R,imageUrl:I,debugInfo:U,caseData:g});const te=j&&k?k.table_content:V,ne=Vt(te,B),re=j&&k?Vt(te,V):100,ae=Vt(te,W),le=Vt(te,K),se=Vt(te,H),oe=Vt(te,$),ie=Vt(te,q);return(0,Ft.jsxs)("div",{className:"case-detail",children:[(0,Ft.jsxs)("div",{className:"case-detail-header",children:[(0,Ft.jsxs)("h3",{children:["\u6848\u4f8b\u8be6\u60c5 - #",S]}),(0,Ft.jsx)("div",{className:"case-detail-filename",children:_})]}),(0,Ft.jsxs)("div",{className:"case-info-block",children:[(0,Ft.jsx)("div",{className:"case-info-header",children:(0,Ft.jsx)("h4",{children:"\u6848\u4f8b\u4fe1\u606f"})}),(0,Ft.jsxs)("div",{className:"case-info-content",children:[(0,Ft.jsxs)("div",{className:"sequence-number",children:["#",S]}),(0,Ft.jsxs)("div",{className:"file-info",children:[(0,Ft.jsx)("div",{className:"file-name",children:_}),(0,Ft.jsxs)("div",{className:"base-name",children:["\u57fa\u7840\u540d: ",C]})]}),I?(0,Ft.jsxs)("div",{className:"image-preview-container",children:[(0,Ft.jsx)("img",{src:I,alt:_,className:"image-preview",onClick:()=>{I&&(x(I),v(!0))},onLoad:()=>console.log("\u56fe\u7247\u52a0\u8f7d\u6210\u529f:",I),onError:e=>{console.error("\u56fe\u7247\u52a0\u8f7d\u5931\u8d25:",I),e.target.style.display="none",e.target.nextSibling.style.display="block"}}),(0,Ft.jsxs)("div",{style:{display:"none",color:"#dc3545",fontSize:"12px",textAlign:"center",padding:"20px"},children:["\u56fe\u7247\u52a0\u8f7d\u5931\u8d25: ",I]})]}):(0,Ft.jsx)("div",{className:"image-preview-container",children:(0,Ft.jsx)("div",{style:{color:"#666",fontSize:"12px",textAlign:"center",padding:"20px"},children:"\u65e0\u56fe\u7247"})})]})]}),(0,Ft.jsxs)("div",{className:"case-info-block",style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef"},children:[(0,Ft.jsx)("div",{className:"case-info-header",children:(0,Ft.jsx)("h4",{style:{color:"#666"},children:"\ud83d\udd0d \u8c03\u8bd5\u4fe1\u606f (\u9a8c\u8bc1\u6570\u636e\u5339\u914d)"})}),(0,Ft.jsxs)("div",{style:{padding:"16px",fontSize:"12px",fontFamily:"monospace"},children:[(0,Ft.jsxs)("div",{style:{marginBottom:"12px"},children:[(0,Ft.jsx)("strong",{children:"\u5f53\u524d\u6848\u4f8b:"})," ",_," (\u7d22\u5f15: ",S,")"]}),(0,Ft.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"},children:Object.entries(U).map(e=>{let[t,n]=e;return(0,Ft.jsxs)("div",{style:{padding:"8px",backgroundColor:n?"#d4edda":"#f8d7da",border:"1px solid ".concat(n?"#c3e6cb":"#f5c6cb"),borderRadius:"4px"},children:[(0,Ft.jsxs)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:[t,":"]}),n?(0,Ft.jsxs)("div",{children:[(0,Ft.jsx)("div",{children:"\u2705 \u6709\u6570\u636e"}),n.filename&&(0,Ft.jsxs)("div",{children:["\u6587\u4ef6: ",n.filename]}),n.originalImage&&(0,Ft.jsxs)("div",{children:["\u56fe\u7247: ",n.originalImage]}),void 0!==n.hasData&&(0,Ft.jsxs)("div",{children:["\u6570\u636e: ",n.hasData?"\u662f":"\u5426"]}),void 0!==n.hasResult&&(0,Ft.jsxs)("div",{children:["\u7ed3\u679c: ",n.hasResult?"\u662f":"\u5426"]})]}):(0,Ft.jsx)("div",{children:"\u274c \u65e0\u6570\u636e"})]},t)})})]})]}),(0,Ft.jsxs)("div",{className:"parsing-status-block",children:[(0,Ft.jsx)("h3",{children:"\u89e3\u6790\u72b6\u6001"}),(0,Ft.jsxs)("div",{className:"status-indicators-grid",children:[(0,Ft.jsxs)("div",{className:"status-indicator ".concat(L?"success":"error"),title:"KDC Markdown",children:[(0,Ft.jsx)("span",{className:"status-name",children:"KDC Markdown"}),(0,Ft.jsx)("span",{className:"status-icon",children:L?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(P?"success":"error"),title:"KDC Plain",children:[(0,Ft.jsx)("span",{className:"status-name",children:"KDC Plain"}),(0,Ft.jsx)("span",{className:"status-icon",children:P?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(T?"success":"error"),title:"KDC KDC",children:[(0,Ft.jsx)("span",{className:"status-name",children:"KDC KDC"}),(0,Ft.jsx)("span",{className:"status-icon",children:T?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(M?"success":"error"),title:"MonkeyOCR(HTML)",children:[(0,Ft.jsx)("span",{className:"status-name",children:"MonkeyOCR (HTML)"}),(0,Ft.jsx)("span",{className:"status-icon",children:M?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(A?"success":"error"),title:"MonkeyOCR(LaTeX)",children:[(0,Ft.jsx)("span",{className:"status-name",children:"MonkeyOCR (LaTeX)"}),(0,Ft.jsx)("span",{className:"status-icon",children:A?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(D?"success":"error"),title:"VL-LLM",children:[(0,Ft.jsx)("span",{className:"status-name",children:"VL-LLM"}),(0,Ft.jsx)("span",{className:"status-icon",children:D?"\u2713":"\u2717"})]}),(0,Ft.jsxs)("div",{className:"status-indicator ".concat(z?"success":"error"),title:"MonkeyOCR(local)",children:[(0,Ft.jsx)("span",{className:"status-name",children:"MonkeyOCR (local)"}),(0,Ft.jsx)("span",{className:"status-icon",children:z?"\u2713":"\u2717"})]})]})]}),(0,Ft.jsxs)("div",{className:"accuracy-summary-block",children:[(0,Ft.jsxs)("div",{className:"accuracy-summary-header",children:[(0,Ft.jsx)("h4",{children:"\u51c6\u786e\u7387\u6c47\u603b"}),k&&(0,Ft.jsxs)("div",{className:"baseline-toggle",children:[(0,Ft.jsxs)("label",{children:[(0,Ft.jsx)("input",{type:"checkbox",checked:j,onChange:()=>N(!j)}),"\u4f7f\u7528\u6807\u6ce8\u6570\u636e\u4f5c\u4e3a\u57fa\u51c6"]}),(0,Ft.jsx)("div",{className:"baseline-info",children:j?"\u5f53\u524d\u57fa\u51c6: \u6807\u6ce8\u6570\u636e":"\u5f53\u524d\u57fa\u51c6: KDC Plain"})]})]}),(0,Ft.jsxs)("div",{className:"accuracy-summary-content",children:[(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"KDC Markdown"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[ne.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"KDC Plain"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[re.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"KDC KDC"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[ae.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"MonkeyOCR(table)"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[le.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"MonkeyOCR(parse)"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[se.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"VL LLM"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[oe.toFixed(1),"%"]})]}),(0,Ft.jsxs)("div",{className:"accuracy-item",children:[(0,Ft.jsx)("div",{className:"accuracy-label",children:"MonkeyOCR (local)"}),(0,Ft.jsxs)("div",{className:"accuracy-value",children:[ie.toFixed(1),"%"]})]})]})]}),F&&F.kdc&&(0,Ft.jsxs)("div",{className:"features-metrics-container",children:[(0,Ft.jsxs)("div",{className:"features-summary-block",children:[(0,Ft.jsx)("div",{className:"features-summary-header",children:(0,Ft.jsx)("h4",{children:"\u7279\u5f81\u4fe1\u606f"})}),(0,Ft.jsxs)("div",{className:"features-summary-content",children:[(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"KDC Bbox\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:F.kdc.bbox_count||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u5e73\u5747X\u5750\u6807"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(n=F.kdc.bbox_position_metrics)||void 0===n?void 0:n.avg_x)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u5e73\u5747Y\u5750\u6807"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(r=F.kdc.bbox_position_metrics)||void 0===r?void 0:r.avg_y)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u5e73\u5747\u5bbd\u5ea6"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(l=F.kdc.bbox_position_metrics)||void 0===l?void 0:l.avg_width)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u5e73\u5747\u9ad8\u5ea6"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(s=F.kdc.bbox_position_metrics)||void 0===s?void 0:s.avg_height)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"Bbox\u5bc6\u5ea6"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(o=F.kdc.bbox_position_metrics)||void 0===o?void 0:o.bbox_density)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u6587\u672c\u6846\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(i=F.kdc.bbox_types)||void 0===i?void 0:i.textbox)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u8868\u683c\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(c=F.kdc.bbox_types)||void 0===c?void 0:c.table)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u7ec4\u4ef6\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(u=F.kdc.bbox_types)||void 0===u?void 0:u.component)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u8868\u683c\u5355\u5143\u683c\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(d=F.kdc.bbox_types)||void 0===d?void 0:d.table_cell)||0})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u68c0\u6d4b\u5230\u8868\u683c"}),(0,Ft.jsx)("div",{className:"feature-value ".concat(null!==(f=F.kdc.table_detection)&&void 0!==f&&f.has_table?"feature-value-success":"feature-value-warning"),children:null!==(p=F.kdc.table_detection)&&void 0!==p&&p.has_table?"\u662f":"\u5426"})]}),(0,Ft.jsxs)("div",{className:"feature-item",children:[(0,Ft.jsx)("div",{className:"feature-label",children:"\u8868\u683c\u68c0\u6d4b\u6570\u91cf"}),(0,Ft.jsx)("div",{className:"feature-value",children:(null===(h=F.kdc.table_detection)||void 0===h?void 0:h.table_count)||0})]})]}),F.kdc.textbox_count_distribution&&(0,Ft.jsx)(dn,{distributionData:F.kdc.textbox_count_distribution,className:"textbox-distribution-feature"})]}),(0,Ft.jsx)("div",{className:"metrics-summary-block",children:(0,Ft.jsx)(mn,{caseData:g})})]}),(0,Ft.jsxs)("div",{className:"parsing-details-block",children:[(0,Ft.jsx)("div",{className:"parsing-details-header",children:(0,Ft.jsx)("h4",{children:"\u89e3\u6790\u8be6\u60c5"})}),(0,Ft.jsx)("div",{style:{overflowX:"auto",width:"100%"},children:(0,Ft.jsxs)("div",{className:"case-detail-content",children:[(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-1",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR\uff08parse\uff09\u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(H?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:H||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR\uff08parse\uff09\u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(G?"has-table":""),children:(0,Ft.jsx)(on,{content:G,type:"html",placeholder:"\u65e0MonkeyOCR(parse)\u6e32\u67d3\u7ed3\u679c"})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-2",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR\uff08table prompt\uff09\u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(K?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:K||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR\uff08table prompt\uff09\u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(Y?"has-table":""),children:(0,Ft.jsx)(on,{content:Y,type:"html",placeholder:"\u65e0MonkeyOCR\u6e32\u67d3\u7ed3\u679c"})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-3",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR (local) \u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(q?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:q||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"MonkeyOCR (local) \u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(ee?"has-table":""),children:(0,Ft.jsx)(on,{content:ee,type:(null===z||void 0===z||null===(m=z.result)||void 0===m?void 0:m.type)||"html",placeholder:"\u65e0MonkeyOCR (local)\u6e32\u67d3\u7ed3\u679c"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-accuracy-report",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"\u51c6\u786e\u7387\u62a5\u544a"}),(0,Ft.jsx)("div",{className:"cell-content",children:(0,Ft.jsx)("div",{className:"accuracy-report",children:(0,Ft.jsx)("div",{className:"accuracy-score",children:(0,Ft.jsxs)("span",{className:"accuracy-percentage",children:[ie.toFixed(1),"%"]})})})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-4",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"VL LLM \u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat($?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:$||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"VL LLM \u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(Z?"has-table":""),children:(0,Ft.jsx)(on,{content:Z,type:"markdown",placeholder:"\u65e0VL LLM\u6e32\u67d3\u7ed3\u679c"})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-5",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC Plain \u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(V?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:V||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC Plain \u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(J?"has-table":""),children:(0,Ft.jsx)(on,{content:J,type:"plain",placeholder:"\u65e0KDC Plain\u6e32\u67d3\u7ed3\u679c"})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-6",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC Markdown \u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(B?"has-content":""),children:(0,Ft.jsx)("pre",{className:"text-content",children:B||"\u65e0\u6570\u636e"})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC Markdown \u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(Q?"has-table":""),children:(0,Ft.jsx)(on,{content:Q,type:"markdown",placeholder:"\u65e0KDC Markdown\u6e32\u67d3\u7ed3\u679c"})})]})]}),(0,Ft.jsxs)("div",{className:"case-detail-row case-detail-row-7",children:[(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-original-text",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC KDC \u539f\u59cb\u6587\u672c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(W?"has-content":""),children:(0,Ft.jsx)(un,{data:X,placeholder:"\u65e0KDC KDC\u6570\u636e",collapsed:!0})})]}),(0,Ft.jsxs)("div",{className:"case-detail-cell case-detail-rendered-result",children:[(0,Ft.jsx)("div",{className:"cell-header",children:"KDC KDC \u6e32\u67d3\u7ed3\u679c"}),(0,Ft.jsx)("div",{className:"cell-content ".concat(X?"has-table":""),children:(0,Ft.jsx)(on,{content:X,type:"kdc",placeholder:"\u65e0KDC KDC\u6e32\u67d3\u7ed3\u679c"})})]})]})]})})]}),y&&(0,Ft.jsx)(en,{imageUrl:b,onClose:()=>{v(!1),x(null)},title:_})]})}),yn=e=>{let{annotation:t,selectedCase:n,onSave:r,onCancel:l}=e;const[s,o]=(0,a.useState)({annotator:"",structure:{rows:0,cols:0,merged_cells:[],headers:[]},content:"",status:"draft"}),[i,c]=(0,a.useState)("markdown"),[u,d]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(t)try{const e="string"===typeof t.table_structure?JSON.parse(t.table_structure):t.table_structure;o({annotator:t.annotator||"",structure:e||{rows:0,cols:0,merged_cells:[],headers:[]},content:t.table_content||"",status:t.status||"draft"})}catch(e){console.error("\u89e3\u6790\u6807\u6ce8\u6570\u636e\u5931\u8d25:",e)}else o({annotator:localStorage.getItem("annotator_name")||"",structure:{rows:0,cols:0,merged_cells:[],headers:[]},content:"",status:"draft"})},[t]);const f=(e,t)=>{o(n=>pe(pe({},n),{},{[e]:t})),"annotator"===e&&localStorage.setItem("annotator_name",t)},p=(e,t)=>{o(n=>pe(pe({},n),{},{structure:pe(pe({},n.structure),{},{[e]:t})}))};return(0,Ft.jsxs)("div",{className:"annotation-editor",children:[(0,Ft.jsxs)("div",{className:"annotation-editor-header",children:[(0,Ft.jsx)("h4",{children:t?"\u7f16\u8f91\u6807\u6ce8":"\u65b0\u5efa\u6807\u6ce8"}),(0,Ft.jsxs)("div",{className:"annotation-editor-actions",children:[(0,Ft.jsx)("button",{className:"btn btn-primary",onClick:()=>{s.annotator.trim()?s.content.trim()?r(s):alert("\u8bf7\u8f93\u5165\u8868\u683c\u5185\u5bb9"):alert("\u8bf7\u8f93\u5165\u6807\u6ce8\u5458\u59d3\u540d")},children:"\u4fdd\u5b58"}),(0,Ft.jsx)("button",{className:"btn btn-secondary",onClick:l,children:"\u53d6\u6d88"})]})]}),(0,Ft.jsxs)("div",{className:"annotation-editor-content",children:[(0,Ft.jsxs)("div",{className:"form-section",children:[(0,Ft.jsx)("h5",{children:"\u57fa\u672c\u4fe1\u606f"}),(0,Ft.jsxs)("div",{className:"form-row",children:[(0,Ft.jsxs)("div",{className:"form-group",children:[(0,Ft.jsx)("label",{children:"\u6807\u6ce8\u5458:"}),(0,Ft.jsx)("input",{type:"text",value:s.annotator,onChange:e=>f("annotator",e.target.value),placeholder:"\u8bf7\u8f93\u5165\u6807\u6ce8\u5458\u59d3\u540d"})]}),(0,Ft.jsxs)("div",{className:"form-group",children:[(0,Ft.jsx)("label",{children:"\u72b6\u6001:"}),(0,Ft.jsxs)("select",{value:s.status,onChange:e=>f("status",e.target.value),children:[(0,Ft.jsx)("option",{value:"draft",children:"\u8349\u7a3f"}),(0,Ft.jsx)("option",{value:"completed",children:"\u5b8c\u6210"}),(0,Ft.jsx)("option",{value:"reviewed",children:"\u5df2\u5ba1\u6838"})]})]})]})]}),(0,Ft.jsxs)("div",{className:"form-section",children:[(0,Ft.jsx)("h5",{children:"\u8868\u683c\u7ed3\u6784"}),(0,Ft.jsxs)("div",{className:"form-row",children:[(0,Ft.jsxs)("div",{className:"form-group",children:[(0,Ft.jsx)("label",{children:"\u884c\u6570:"}),(0,Ft.jsx)("input",{type:"number",min:"0",value:s.structure.rows,onChange:e=>p("rows",parseInt(e.target.value)||0)})]}),(0,Ft.jsxs)("div",{className:"form-group",children:[(0,Ft.jsx)("label",{children:"\u5217\u6570:"}),(0,Ft.jsx)("input",{type:"number",min:"0",value:s.structure.cols,onChange:e=>p("cols",parseInt(e.target.value)||0)})]}),(0,Ft.jsx)("div",{className:"form-group",children:(0,Ft.jsx)("button",{type:"button",className:"btn btn-small",onClick:()=>{const e=(()=>{const{rows:e,cols:t}=s.structure;if(e<=0||t<=0)return"";let n="";const r=Array(t).fill("\u5217").map((e,t)=>"".concat(e).concat(t+1)).join(" | "),a=Array(t).fill("---").join(" | ");n+="| ".concat(r," |\n"),n+="| ".concat(a," |\n");for(let l=0;l<e-1;l++){const e=Array(t).fill("\u6570\u636e").map((e,t)=>"".concat(e).concat(l+1,"-").concat(t+1)).join(" | ");n+="| ".concat(e," |\n")}return n})();o(t=>pe(pe({},t),{},{content:e}))},children:"\u751f\u6210\u6a21\u677f"})})]})]}),(0,Ft.jsxs)("div",{className:"form-section",children:[(0,Ft.jsxs)("div",{className:"form-section-header",children:[(0,Ft.jsx)("h5",{children:"\u8868\u683c\u5185\u5bb9"}),(0,Ft.jsxs)("div",{className:"content-mode-toggle",children:[(0,Ft.jsx)("button",{className:"btn btn-small ".concat("markdown"===i?"active":""),onClick:()=>c("markdown"),children:"Markdown"}),(0,Ft.jsx)("button",{className:"btn btn-small ".concat("html"===i?"active":""),onClick:()=>c("html"),children:"HTML"}),(0,Ft.jsx)("button",{className:"btn btn-small ".concat(u?"active":""),onClick:()=>d(!u),children:"\u9884\u89c8"})]})]}),u?(0,Ft.jsx)("div",{className:"content-preview",children:(()=>{if(!s.content)return(0,Ft.jsx)("p",{children:"\u6682\u65e0\u5185\u5bb9"});if("markdown"===i){const e=s.content.split("\n").filter(e=>e.trim().startsWith("|"));return 0===e.length?(0,Ft.jsx)("pre",{children:s.content}):(0,Ft.jsx)("table",{className:"preview-table",children:(0,Ft.jsx)("tbody",{children:e.map((e,t)=>{const n=e.split("|").slice(1,-1).map(e=>e.trim()),r=0===t;return e.includes("---")?null:(0,Ft.jsx)("tr",{className:r?"header-row":"",children:n.map((e,t)=>r?(0,Ft.jsx)("th",{children:e},t):(0,Ft.jsx)("td",{children:e},t))},t)})})})}return(0,Ft.jsx)("div",{dangerouslySetInnerHTML:{__html:s.content}})})()}):(0,Ft.jsx)("textarea",{className:"content-editor",value:s.content,onChange:e=>f("content",e.target.value),placeholder:"markdown"===i?"\u8bf7\u8f93\u5165Markdown\u683c\u5f0f\u7684\u8868\u683c\u5185\u5bb9...\n\u4f8b\u5982:\n| \u52171 | \u52172 |\n| --- | --- |\n| \u6570\u636e1 | \u6570\u636e2 |":"\u8bf7\u8f93\u5165HTML\u683c\u5f0f\u7684\u8868\u683c\u5185\u5bb9...",rows:15})]})]})]})},vn=e=>{let{annotations:t,onEdit:n,onDelete:r,loading:a}=e;const l=e=>{try{return new Date(e).toLocaleString("zh-CN")}catch(t){return e}},s=e=>({draft:"\u8349\u7a3f",completed:"\u5b8c\u6210",reviewed:"\u5df2\u5ba1\u6838"}[e]||e),o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return e?e.length<=t?e:e.substring(0,t)+"...":""};return a?(0,Ft.jsx)("div",{className:"annotation-list",children:(0,Ft.jsx)("div",{className:"annotation-list-loading",children:(0,Ft.jsx)("p",{children:"\u52a0\u8f7d\u4e2d..."})})}):0===t.length?(0,Ft.jsx)("div",{className:"annotation-list",children:(0,Ft.jsxs)("div",{className:"annotation-list-empty",children:[(0,Ft.jsx)("p",{children:"\u6682\u65e0\u6807\u6ce8\u6570\u636e"}),(0,Ft.jsx)("p",{children:'\u70b9\u51fb"\u65b0\u5efa\u6807\u6ce8"\u5f00\u59cb\u6807\u6ce8'})]})}):(0,Ft.jsxs)("div",{className:"annotation-list",children:[(0,Ft.jsx)("div",{className:"annotation-list-header",children:(0,Ft.jsxs)("h5",{children:["\u6807\u6ce8\u5217\u8868 (",t.length,")"]})}),(0,Ft.jsx)("div",{className:"annotation-list-content",children:t.map(e=>{return(0,Ft.jsxs)("div",{className:"annotation-item",children:[(0,Ft.jsxs)("div",{className:"annotation-item-header",children:[(0,Ft.jsxs)("div",{className:"annotation-item-info",children:[(0,Ft.jsxs)("span",{className:"annotation-id",children:["#",e.id]}),(0,Ft.jsx)("span",{className:"annotation-annotator",children:e.annotator}),(0,Ft.jsx)("span",{className:"annotation-status ".concat((t=e.status,"status-".concat(t))),children:s(e.status)})]}),(0,Ft.jsxs)("div",{className:"annotation-item-actions",children:[(0,Ft.jsx)("button",{className:"btn btn-small btn-primary",onClick:()=>n(e),children:"\u7f16\u8f91"}),(0,Ft.jsx)("button",{className:"btn btn-small btn-danger",onClick:()=>r(e.id),children:"\u5220\u9664"})]})]}),(0,Ft.jsxs)("div",{className:"annotation-item-content",children:[(0,Ft.jsx)("div",{className:"annotation-structure",children:(()=>{try{const t="string"===typeof e.table_structure?JSON.parse(e.table_structure):e.table_structure;return(0,Ft.jsxs)("span",{className:"structure-info",children:["\u8868\u683c: ",t.rows||0,"\u884c \xd7 ",t.cols||0,"\u5217"]})}catch(t){return(0,Ft.jsx)("span",{className:"structure-info",children:"\u7ed3\u6784\u4fe1\u606f\u89e3\u6790\u5931\u8d25"})}})()}),(0,Ft.jsx)("div",{className:"annotation-content-preview",children:(0,Ft.jsx)("pre",{children:o(e.table_content)})})]}),(0,Ft.jsxs)("div",{className:"annotation-item-footer",children:[(0,Ft.jsxs)("span",{className:"annotation-date",children:["\u521b\u5efa: ",l(e.created_at)]}),e.updated_at!==e.created_at&&(0,Ft.jsxs)("span",{className:"annotation-date",children:["\u66f4\u65b0: ",l(e.updated_at)]})]})]},e.id);var t})})]})},bn=e=>{let{selectedDataset:t,onAnnotationChange:n}=e;const[r,l]=(0,a.useState)([]),[s,o]=(0,a.useState)(!1),[i,c]=(0,a.useState)(null),[u,d]=(0,a.useState)(!1),[f,p]=(0,a.useState)(null),h=(0,a.useCallback)(async()=>{if(t){o(!0),c(null);try{const e=await zt(t);l(e);const r={total:e.length,auto_generated:e.filter(e=>"auto_generated"===e.annotation_type).length,manual:e.filter(e=>"manual"===e.annotation_type).length,completed:e.filter(e=>"completed"===e.status).length,draft:e.filter(e=>"draft"===e.status).length};p(r),n&&n(e)}catch(e){c("\u52a0\u8f7d\u6807\u6ce8\u6570\u636e\u5931\u8d25: "+e.message),console.error("\u52a0\u8f7d\u6807\u6ce8\u6570\u636e\u5931\u8d25:",e)}finally{o(!1)}}},[t,n]);return(0,a.useEffect)(()=>{h()},[t,h]),t?(0,Ft.jsxs)("div",{className:"annotation-manager",children:[(0,Ft.jsxs)("div",{className:"annotation-header",children:[(0,Ft.jsxs)("h3",{children:["\u6807\u6ce8\u7ba1\u7406 - ",t]}),(0,Ft.jsxs)("div",{className:"annotation-actions",children:[(0,Ft.jsx)("button",{onClick:h,disabled:s,className:"btn btn-secondary",children:s?"\u52a0\u8f7d\u4e2d...":"\u5237\u65b0"}),(0,Ft.jsx)("button",{onClick:async()=>{if(t){d(!0),c(null);try{const e=await async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{const n=new URLSearchParams;t.annotator&&n.append("annotator",t.annotator),void 0!==t.overwrite&&n.append("overwrite",t.overwrite);const r="/api/annotations/auto-generate/".concat(e).concat(n.toString()?"?"+n.toString():"");return(await At.post(r)).data}catch(i){throw console.error("\u81ea\u52a8\u751f\u6210\u6807\u6ce8\u5931\u8d25:",i),i}}(t,{annotator:"auto_generator",overwrite:!1});console.log("\u81ea\u52a8\u751f\u6210\u6807\u6ce8\u7ed3\u679c:",e),await h(),alert("\u81ea\u52a8\u751f\u6210\u6807\u6ce8\u5b8c\u6210\uff01\n\u6210\u529f: ".concat(e.stats.success_count,"\n\u5931\u8d25: ").concat(e.stats.failed_count,"\n\u8df3\u8fc7: ").concat(e.stats.skipped_count))}catch(e){c("\u81ea\u52a8\u751f\u6210\u6807\u6ce8\u5931\u8d25: "+e.message),console.error("\u81ea\u52a8\u751f\u6210\u6807\u6ce8\u5931\u8d25:",e)}finally{d(!1)}}},disabled:u||s,className:"btn btn-primary",children:u?"\u751f\u6210\u4e2d...":"\u81ea\u52a8\u751f\u6210\u6807\u6ce8"})]})]}),i&&(0,Ft.jsx)("div",{className:"error-message",children:i}),f&&(0,Ft.jsxs)("div",{className:"annotation-stats",children:[(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("span",{className:"stat-label",children:"\u603b\u8ba1:"}),(0,Ft.jsx)("span",{className:"stat-value",children:f.total})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("span",{className:"stat-label",children:"\u81ea\u52a8\u751f\u6210:"}),(0,Ft.jsx)("span",{className:"stat-value",children:f.auto_generated})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("span",{className:"stat-label",children:"\u624b\u52a8\u6807\u6ce8:"}),(0,Ft.jsx)("span",{className:"stat-value",children:f.manual})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("span",{className:"stat-label",children:"\u5df2\u5b8c\u6210:"}),(0,Ft.jsx)("span",{className:"stat-value",children:f.completed})]}),(0,Ft.jsxs)("div",{className:"stat-item",children:[(0,Ft.jsx)("span",{className:"stat-label",children:"\u8349\u7a3f:"}),(0,Ft.jsx)("span",{className:"stat-value",children:f.draft})]})]}),(0,Ft.jsx)("div",{className:"annotation-list",children:s?(0,Ft.jsx)("div",{className:"loading",children:"\u52a0\u8f7d\u6807\u6ce8\u6570\u636e\u4e2d..."}):0===r.length?(0,Ft.jsxs)("div",{className:"no-annotations",children:[(0,Ft.jsx)("p",{children:"\u6682\u65e0\u6807\u6ce8\u6570\u636e"}),(0,Ft.jsx)("p",{children:'\u70b9\u51fb"\u81ea\u52a8\u751f\u6210\u6807\u6ce8"\u6309\u94ae\u4ece\u751f\u6210\u7684\u8868\u683c\u6570\u636e\u521b\u5efa\u6807\u6ce8'})]}):(0,Ft.jsxs)("div",{className:"annotation-table",children:[(0,Ft.jsxs)("div",{className:"table-header",children:[(0,Ft.jsx)("div",{className:"col-image",children:"\u56fe\u7247"}),(0,Ft.jsx)("div",{className:"col-annotator",children:"\u6807\u6ce8\u5458"}),(0,Ft.jsx)("div",{className:"col-type",children:"\u7c7b\u578b"}),(0,Ft.jsx)("div",{className:"col-status",children:"\u72b6\u6001"}),(0,Ft.jsx)("div",{className:"col-created",children:"\u521b\u5efa\u65f6\u95f4"}),(0,Ft.jsx)("div",{className:"col-actions",children:"\u64cd\u4f5c"})]}),r.map(e=>(0,Ft.jsxs)("div",{className:"table-row",children:[(0,Ft.jsx)("div",{className:"col-image",children:e.image_filename}),(0,Ft.jsx)("div",{className:"col-annotator",children:e.annotator}),(0,Ft.jsx)("div",{className:"col-type",children:(0,Ft.jsx)("span",{className:"type-badge ".concat(e.annotation_type),children:"auto_generated"===e.annotation_type?"\u81ea\u52a8":"\u624b\u52a8"})}),(0,Ft.jsx)("div",{className:"col-status",children:(0,Ft.jsx)("span",{className:"status-badge ".concat(e.status),children:"completed"===e.status?"\u5b8c\u6210":"draft"===e.status?"\u8349\u7a3f":e.status})}),(0,Ft.jsx)("div",{className:"col-created",children:new Date(e.created_at).toLocaleDateString()}),(0,Ft.jsx)("div",{className:"col-actions",children:(0,Ft.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("\u786e\u5b9a\u8981\u5220\u9664\u8fd9\u4e2a\u6807\u6ce8\u5417\uff1f"))try{await Dt(e),await h()}catch(t){c("\u5220\u9664\u6807\u6ce8\u5931\u8d25: "+t.message),console.error("\u5220\u9664\u6807\u6ce8\u5931\u8d25:",t)}})(e.id),className:"btn btn-danger btn-sm",children:"\u5220\u9664"})})]},e.id))]})})]}):(0,Ft.jsx)("div",{className:"annotation-manager",children:(0,Ft.jsx)("div",{className:"no-dataset",children:"\u8bf7\u5148\u9009\u62e9\u4e00\u4e2a\u6570\u636e\u96c6"})})},xn=e=>{let{selectedCase:t,selectedDataset:n,onAnnotationChange:r}=e;const[l,s]=(0,a.useState)([]),[o,i]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),[d,f]=(0,a.useState)(!1),[p,h]=(0,a.useState)(null),[m,g]=(0,a.useState)("case");(0,a.useEffect)(()=>{t&&n&&y()},[t,n]);const y=async()=>{if(t&&n){f(!0),h(null);try{console.log("loadAnnotations: \u5f00\u59cb\u52a0\u8f7d\u6807\u6ce8",{selectedDataset:n,fileName:t.fileName,imageName:t.imageName,caseId:t.id});try{const e=await(async(e,t)=>{try{const n=(await At.get("/api/datasets/".concat(e,"/images"))).data.find(e=>e.filename===t);return n?n.id:null}catch(p){throw console.error("Failed to find image ID for ".concat(t,":"),p),p}})(n,t.fileName);if(console.log("loadAnnotations: \u627e\u5230\u56fe\u7247ID",e),e){const t=await(async e=>{try{return(await At.get("/api/images/".concat(e,"/annotations"))).data}catch(p){throw console.error("Failed to fetch annotations for image ".concat(e,":"),p),p}})(e);return console.log("loadAnnotations: \u901a\u8fc7\u56fe\u7247ID\u83b7\u53d6\u7684\u6807\u6ce8",t),void s(t)}}catch(e){console.warn("loadAnnotations: \u901a\u8fc7\u56fe\u7247ID\u83b7\u53d6\u6807\u6ce8\u5931\u8d25\uff0c\u5c1d\u8bd5\u5907\u7528\u65b9\u6cd5",e)}console.log("loadAnnotations: \u4f7f\u7528\u5907\u7528\u65b9\u6cd5\u83b7\u53d6\u6807\u6ce8");const r=await async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{return(await At.get("/api/annotations",{params:e})).data}catch(p){throw console.error("Failed to fetch annotations:",p),p}}({dataset_name:n});console.log("loadAnnotations: \u83b7\u53d6\u5230\u7684\u6240\u6709\u6807\u6ce8",r);const a=r.filter(e=>{const n=e.image_filename===t.fileName,r=e.image_filename===t.imageName,a=e.image_filename.replace(/\.(png|jpg|jpeg|gif|webp)$/i,"")===t.fileName.replace(/\.(png|jpg|jpeg|gif|webp|pdf)$/i,""),l=n||r||a;return console.log("loadAnnotations: \u6587\u4ef6\u540d\u5339\u914d\u68c0\u67e5",{annotationFile:e.image_filename,selectedFile:t.fileName,selectedImageName:t.imageName,exactMatch:n,baseNameMatch:r,withoutExtMatch:a,finalMatch:l}),l});console.log("loadAnnotations: \u7b5b\u9009\u540e\u7684\u6807\u6ce8",a),s(a)}catch(e){console.error("\u52a0\u8f7d\u6807\u6ce8\u6570\u636e\u5931\u8d25:",e),h("\u52a0\u8f7d\u6807\u6ce8\u6570\u636e\u5931\u8d25")}finally{f(!1)}}else console.log("loadAnnotations: \u7f3a\u5c11\u5fc5\u8981\u53c2\u6570",{selectedCase:!!t,selectedDataset:n})},v=async(e,t)=>{try{const n=await(async(e,t)=>{try{return(await At.put("/api/annotations/".concat(e),t)).data}catch(p){throw console.error("Failed to update annotation ".concat(e,":"),p),p}})(e,{table_structure:JSON.stringify(t.structure),table_content:t.content,status:t.status});s(l.map(t=>t.id===e?n:t)),u(!1),i(null),r&&r()}catch(n){console.error("\u66f4\u65b0\u6807\u6ce8\u5931\u8d25:",n),h("\u66f4\u65b0\u6807\u6ce8\u5931\u8d25")}};return(0,Ft.jsxs)("div",{className:"annotation-panel",children:[(0,Ft.jsxs)("div",{className:"annotation-panel-header",children:[(0,Ft.jsx)("h3",{children:"\u4eba\u5de5\u6807\u6ce8"}),(0,Ft.jsxs)("div",{className:"annotation-tab-navigation",children:[(0,Ft.jsx)("button",{className:"tab-button ".concat("case"===m?"active":""),onClick:()=>g("case"),children:"\u6848\u4f8b\u6807\u6ce8"}),(0,Ft.jsx)("button",{className:"tab-button ".concat("dataset"===m?"active":""),onClick:()=>g("dataset"),children:"\u6570\u636e\u96c6\u7ba1\u7406"})]})]}),p&&(0,Ft.jsxs)("div",{className:"annotation-error",children:[(0,Ft.jsx)("p",{children:p}),(0,Ft.jsx)("button",{onClick:()=>h(null),children:"\u5173\u95ed"})]}),(0,Ft.jsx)("div",{className:"annotation-panel-content",children:"dataset"===m?(0,Ft.jsx)(bn,{selectedDataset:n,onAnnotationChange:r}):(0,Ft.jsx)(Ft.Fragment,{children:t?c?(0,Ft.jsx)(yn,{annotation:o,selectedCase:t,onSave:o?e=>v(o.id,e):async e=>{try{const a=await(async e=>{try{return(await At.post("/api/annotations",e)).data}catch(p){throw console.error("Failed to create annotation:",p),p}})({dataset_name:n,image_filename:t.fileName,annotator:e.annotator||"anonymous",table_structure:JSON.stringify(e.structure),table_content:e.content,annotation_type:"manual",status:"draft"});s([...l,a]),u(!1),i(null),r&&r()}catch(a){console.error("\u521b\u5efa\u6807\u6ce8\u5931\u8d25:",a),h("\u521b\u5efa\u6807\u6ce8\u5931\u8d25")}},onCancel:()=>{i(null),u(!1)}}):(0,Ft.jsxs)(Ft.Fragment,{children:[(0,Ft.jsxs)("div",{className:"annotation-panel-info",children:[(0,Ft.jsxs)("span",{children:["\u56fe\u7247: ",t.fileName]}),(0,Ft.jsxs)("span",{children:["\u6807\u6ce8\u6570\u91cf: ",l.length]})]}),(0,Ft.jsxs)("div",{className:"annotation-panel-actions",children:[(0,Ft.jsx)("button",{className:"btn btn-primary",onClick:()=>{i(null),u(!0)},children:"\u65b0\u5efa\u6807\u6ce8"}),(0,Ft.jsx)("button",{className:"btn btn-secondary",onClick:y,disabled:d,children:d?"\u52a0\u8f7d\u4e2d...":"\u5237\u65b0"})]}),(0,Ft.jsx)(vn,{annotations:l,onEdit:e=>{i(e),u(!0)},onDelete:async e=>{if(window.confirm("\u786e\u5b9a\u8981\u5220\u9664\u8fd9\u4e2a\u6807\u6ce8\u5417\uff1f"))try{await Dt(e),s(l.filter(t=>t.id!==e)),o&&o.id===e&&(i(null),u(!1)),r&&r()}catch(t){console.error("\u5220\u9664\u6807\u6ce8\u5931\u8d25:",t),h("\u5220\u9664\u6807\u6ce8\u5931\u8d25")}},loading:d})]}):(0,Ft.jsx)("div",{className:"annotation-panel-empty",children:(0,Ft.jsx)("p",{children:"\u8bf7\u9009\u62e9\u4e00\u4e2a\u6d4b\u8bd5\u6848\u4f8b\u5f00\u59cb\u6807\u6ce8"})})})})]})};const kn=function(){const[e,t]=(0,a.useState)(null),[n,r]=(0,a.useState)(null),[l,s]=(0,a.useState)(!1),[o,i]=(0,a.useState)("analysis"),c=(0,a.useCallback)(e=>{t(e),r(null)},[]),u=(0,a.useCallback)(e=>{r(e)},[]),d=(0,a.useMemo)(()=>(0,Ft.jsx)(It,{selectedDataset:e,onDatasetChange:c,disabled:l}),[e,c,l]),f=(0,a.useMemo)(()=>(0,Ft.jsx)(Zt,{selectedDataset:e,selectedCase:n,onCaseSelect:u}),[e,n,u]),p=(0,a.useMemo)(()=>(0,Ft.jsx)(gn,{caseData:n}),[n]),h=(0,a.useMemo)(()=>(0,Ft.jsx)(xn,{selectedCase:n,selectedDataset:e,onAnnotationChange:()=>{console.log("\u6807\u6ce8\u6570\u636e\u5df2\u66f4\u65b0")}}),[n,e]);return(0,Ft.jsxs)("div",{className:"app",children:[(0,Ft.jsx)("header",{className:"app-header",children:(0,Ft.jsxs)("div",{className:"container",children:[(0,Ft.jsx)("h1",{className:"app-title",children:"TableRAG \u5206\u6790\u5668"}),(0,Ft.jsx)("p",{className:"app-subtitle",children:"\u57fa\u4e8eReact\u7684\u8868\u683c\u89e3\u6790\u7ed3\u679c\u5206\u6790\u5de5\u5177"})]})}),(0,Ft.jsx)("main",{className:"app-main",children:(0,Ft.jsxs)("div",{className:"container",children:[(0,Ft.jsx)("section",{className:"app-section",children:d}),(0,Ft.jsx)("section",{className:"app-section",children:(0,Ft.jsxs)("div",{className:"tab-navigation",children:[(0,Ft.jsx)("button",{className:"tab-button ".concat("analysis"===o?"active":""),onClick:()=>i("analysis"),children:"\u89e3\u6790\u5206\u6790"}),(0,Ft.jsx)("button",{className:"tab-button ".concat("annotation"===o?"active":""),onClick:()=>i("annotation"),children:"\u4eba\u5de5\u6807\u6ce8"})]})}),(0,Ft.jsxs)("div",{className:"app-content",children:[(0,Ft.jsx)("aside",{className:"app-sidebar",children:f}),(0,Ft.jsx)("section",{className:"app-detail",children:"analysis"===o?p:h})]})]})}),(0,Ft.jsx)("footer",{className:"app-footer",children:(0,Ft.jsx)("div",{className:"container",children:(0,Ft.jsx)("p",{children:"\xa9 2025 TableRAG Analyzer. \u57fa\u4e8eReact\u6784\u5efa\u7684\u73b0\u4ee3\u5316\u5206\u6790\u5de5\u5177\u3002"})})})]})};l.createRoot(document.getElementById("root")).render((0,Ft.jsx)(a.StrictMode,{children:(0,Ft.jsx)(kn,{})}))})();
//# sourceMappingURL=main.5d17b2c4.js.map