.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.app-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
}

.app-subtitle {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 300;
}

.app-main {
  flex: 1;
  padding: 20px 0;
}

.app-section {
  margin-bottom: 20px;
}

/* 标签导航样式 */
.tab-navigation {
  display: flex;
  gap: 2px;
  background: #e9ecef;
  padding: 4px;
  border-radius: 8px;
  width: fit-content;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  background: #dee2e6;
  color: #495057;
}

.tab-button.active {
  background: #007bff;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.app-content {
  display: flex;
  gap: 20px;
  min-height: 600px;
}

.app-sidebar {
  width: 30%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.app-detail {
  width: 70%;
  flex: 1;
}

.app-footer {
  background-color: #343a40;
  color: white;
  padding: 16px 0;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-content {
    flex-direction: column;
  }

  .app-sidebar,
  .app-detail {
    width: 100%;
  }
}

@media (max-width: 992px) {
  .app-content {
    gap: 16px;
  }

  .app-sidebar {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 16px 0;
  }
  
  .app-title {
    font-size: 24px;
  }
  
  .app-subtitle {
    font-size: 14px;
  }
  
  .app-main {
    padding: 16px 0;
  }
  
  .container {
    padding: 0 16px;
  }
}

@media (max-width: 576px) {
  .app-title {
    font-size: 20px;
  }
  
  .app-subtitle {
    font-size: 13px;
  }
  
  .app-main {
    padding: 12px 0;
  }
  
  .container {
    padding: 0 12px;
  }
  
  .app-content {
    gap: 16px;
  }
}

/* 加载状态 */
.app-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
}

.app-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.app-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 16px;
  border-radius: 8px;
  margin: 20px 0;
}

.app-error-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.app-error-message {
  margin: 0;
}

/* 空状态 */
.app-empty {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.app-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.app-empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.app-empty-message {
  font-size: 14px;
  opacity: 0.8;
}

/* 性能优化相关样式 */
.app-virtualized {
  height: 100%;
  width: 100%;
}

.app-lazy-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.app-lazy-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* 滚动优化 */
.app-scroll-container {
  scroll-behavior: smooth;
}

.app-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.app-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.app-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.app-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 统计面板样式 */
.stats-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.stats-panel-header {
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 16px;
}

.stats-panel-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #007bff;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.warning {
  color: #ffc107;
}

.stat-value.danger {
  color: #dc3545;
}
