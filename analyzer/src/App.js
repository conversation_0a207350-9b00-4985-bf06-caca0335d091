import React, { useState, useCallback, useMemo } from 'react';
import DatasetSelector from './components/DatasetSelector';
import CaseList from './components/CaseList';
import CaseDetail from './components/CaseDetail';
import AnnotationPanel from './components/annotation/AnnotationPanel';
import './App.css';

function App() {
  const [selectedDataset, setSelectedDataset] = useState(null);
  const [selectedCase, setSelectedCase] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('analysis'); // analysis | annotation

  const handleDatasetChange = useCallback((dataset) => {
    setSelectedDataset(dataset);
    setSelectedCase(null);
  }, []);

  const handleCaseSelect = useCallback((caseData) => {
    setSelectedCase(caseData);
  }, []);

  // 使用 useMemo 优化渲染性能
  const datasetSelector = useMemo(() => (
    <DatasetSelector
      selectedDataset={selectedDataset}
      onDatasetChange={handleDatasetChange}
      disabled={loading}
    />
  ), [selectedDataset, handleDatasetChange, loading]);

  const caseList = useMemo(() => (
    <CaseList
      selectedDataset={selectedDataset}
      selectedCase={selectedCase}
      onCaseSelect={handleCaseSelect}
    />
  ), [selectedDataset, selectedCase, handleCaseSelect]);

  const caseDetail = useMemo(() => (
    <CaseDetail caseData={selectedCase} dataset={selectedDataset} />
  ), [selectedCase, selectedDataset]);

  const annotationPanel = useMemo(() => (
    <AnnotationPanel
      selectedCase={selectedCase}
      selectedDataset={selectedDataset}
      onAnnotationChange={() => {
        // 标注变化时的回调，可以用来刷新数据
        console.log('标注数据已更新');
      }}
    />
  ), [selectedCase, selectedDataset]);

  return (
    <div className="app">
      <header className="app-header">
        <div className="container">
          <h1 className="app-title">TableRAG 分析器</h1>
          <p className="app-subtitle">基于React的表格解析结果分析工具</p>
        </div>
      </header>

      <main className="app-main">
        <div className="container">
          {/* 数据集选择器 */}
          <section className="app-section">
            {datasetSelector}
          </section>

          {/* 标签切换 */}
          <section className="app-section">
            <div className="tab-navigation">
              <button
                className={`tab-button ${activeTab === 'analysis' ? 'active' : ''}`}
                onClick={() => setActiveTab('analysis')}
              >
                解析分析
              </button>
              <button
                className={`tab-button ${activeTab === 'annotation' ? 'active' : ''}`}
                onClick={() => setActiveTab('annotation')}
              >
                人工标注
              </button>
            </div>
          </section>

          {/* 主要内容区域 */}
          <div className="app-content">
            {/* 左侧：案例列表 */}
            <aside className="app-sidebar">
              {caseList}
            </aside>

            {/* 右侧：内容区域 */}
            <section className="app-detail">
              {activeTab === 'analysis' ? caseDetail : annotationPanel}
            </section>
          </div>
        </div>
      </main>

      <footer className="app-footer">
        <div className="container">
          <p>&copy; 2025 TableRAG Analyzer. 基于React构建的现代化分析工具。</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
