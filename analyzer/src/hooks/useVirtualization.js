import { useState, useEffect, useCallback, useMemo } from 'react';

/**
 * 虚拟滚动Hook
 * @param {Array} items - 要渲染的项目列表
 * @param {number} itemHeight - 每个项目的高度
 * @param {number} containerHeight - 容器高度
 * @param {number} overscan - 预渲染的项目数量
 */
export const useVirtualization = ({
  items = [],
  itemHeight = 50,
  containerHeight = 400,
  overscan = 5
}) => {
  const [scrollTop, setScrollTop] = useState(0);

  const handleScroll = useCallback((event) => {
    setScrollTop(event.target.scrollTop);
  }, []);

  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => ({
      ...item,
      index: visibleRange.start + index,
      style: {
        position: 'absolute',
        top: (visibleRange.start + index) * itemHeight,
        height: itemHeight,
        width: '100%'
      }
    }));
  }, [items, visibleRange, itemHeight]);

  const totalHeight = items.length * itemHeight;

  return {
    visibleItems,
    totalHeight,
    handleScroll,
    visibleRange
  };
};

/**
 * 懒加载Hook
 * @param {Function} loadMore - 加载更多数据的函数
 * @param {boolean} hasMore - 是否还有更多数据
 * @param {number} threshold - 触发加载的阈值（距离底部的像素）
 */
export const useLazyLoading = ({
  loadMore,
  hasMore = true,
  threshold = 100
}) => {
  const [loading, setLoading] = useState(false);

  const handleScroll = useCallback(async (event) => {
    if (loading || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = event.target;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceFromBottom < threshold) {
      setLoading(true);
      try {
        await loadMore();
      } catch (error) {
        console.error('Failed to load more data:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [loading, hasMore, threshold, loadMore]);

  return {
    loading,
    handleScroll
  };
};

/**
 * 防抖Hook
 * @param {*} value - 要防抖的值
 * @param {number} delay - 延迟时间（毫秒）
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * 节流Hook
 * @param {Function} callback - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 */
export const useThrottle = (callback, delay) => {
  const [lastCall, setLastCall] = useState(0);

  return useCallback((...args) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      setLastCall(now);
      return callback(...args);
    }
  }, [callback, delay, lastCall]);
};

/**
 * 交叉观察器Hook（用于懒加载图片等）
 * @param {Object} options - IntersectionObserver选项
 */
export const useIntersectionObserver = (options = {}) => {
  const [entries, setEntries] = useState([]);
  const [observer, setObserver] = useState(null);

  useEffect(() => {
    const obs = new IntersectionObserver((observedEntries) => {
      setEntries(observedEntries);
    }, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    });

    setObserver(obs);

    return () => {
      obs.disconnect();
    };
  }, []);

  const observe = useCallback((element) => {
    if (observer && element) {
      observer.observe(element);
    }
  }, [observer]);

  const unobserve = useCallback((element) => {
    if (observer && element) {
      observer.unobserve(element);
    }
  }, [observer]);

  return {
    entries,
    observe,
    unobserve
  };
};

/**
 * 内存优化Hook - 清理不再使用的数据
 * @param {Array} dependencies - 依赖数组
 * @param {Function} cleanup - 清理函数
 */
export const useMemoryOptimization = (dependencies, cleanup) => {
  useEffect(() => {
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, dependencies);
};
