.annotation-manager {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.annotation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.annotation-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.5rem;
}

.annotation-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.annotation-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.no-dataset,
.no-annotations,
.loading {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.no-annotations p {
  margin: 10px 0;
}

.annotation-table {
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr 1fr;
  gap: 15px;
  padding: 12px 15px;
  align-items: center;
}

.table-header {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table-row {
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.col-image {
  font-family: monospace;
  font-size: 13px;
  word-break: break-all;
}

.type-badge,
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.auto_generated {
  background-color: #d4edda;
  color: #155724;
}

.type-badge.manual {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.draft {
  background-color: #fff3cd;
  color: #856404;
}

.col-created {
  font-size: 13px;
  color: #6c757d;
}

.col-actions {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annotation-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .annotation-actions {
    justify-content: center;
  }

  .annotation-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    padding: 15px;
    border: 1px solid #dee2e6;
    margin-bottom: 10px;
    border-radius: 6px;
  }

  .table-row > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
  }

  .table-row > div::before {
    content: attr(data-label);
    font-weight: bold;
    color: #495057;
  }

  .col-image::before { content: "图片: "; }
  .col-annotator::before { content: "标注员: "; }
  .col-type::before { content: "类型: "; }
  .col-status::before { content: "状态: "; }
  .col-created::before { content: "创建时间: "; }
  .col-actions::before { content: "操作: "; }
}
