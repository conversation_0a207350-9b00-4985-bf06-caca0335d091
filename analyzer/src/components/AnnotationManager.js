import React, { useState, useEffect, useCallback } from 'react';
import { 
  getDatasetAnnotations, 
  autoGenerateAnnotations,
  deleteAnnotation 
} from '../services/api';
import './AnnotationManager.css';

const AnnotationManager = ({ selectedDataset, onAnnotationChange }) => {
  const [annotations, setAnnotations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generating, setGenerating] = useState(false);
  const [stats, setStats] = useState(null);

  // 加载标注数据
  const loadAnnotations = useCallback(async () => {
    if (!selectedDataset) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await getDatasetAnnotations(selectedDataset);
      setAnnotations(data);
      
      // 计算统计信息
      const statsData = {
        total: data.length,
        auto_generated: data.filter(a => a.annotation_type === 'auto_generated').length,
        manual: data.filter(a => a.annotation_type === 'manual').length,
        completed: data.filter(a => a.status === 'completed').length,
        draft: data.filter(a => a.status === 'draft').length
      };
      setStats(statsData);
      
      // 通知父组件标注数据变化
      if (onAnnotationChange) {
        onAnnotationChange(data);
      }
      
    } catch (err) {
      setError('加载标注数据失败: ' + err.message);
      console.error('加载标注数据失败:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedDataset, onAnnotationChange]);

  // 自动生成标注
  const handleAutoGenerate = async () => {
    if (!selectedDataset) return;
    
    setGenerating(true);
    setError(null);
    
    try {
      const result = await autoGenerateAnnotations(selectedDataset, {
        annotator: 'auto_generator',
        overwrite: false
      });
      
      console.log('自动生成标注结果:', result);
      
      // 重新加载标注数据
      await loadAnnotations();
      
      // 显示成功消息
      alert(`自动生成标注完成！\n成功: ${result.stats.success_count}\n失败: ${result.stats.failed_count}\n跳过: ${result.stats.skipped_count}`);
      
    } catch (err) {
      setError('自动生成标注失败: ' + err.message);
      console.error('自动生成标注失败:', err);
    } finally {
      setGenerating(false);
    }
  };

  // 删除标注
  const handleDeleteAnnotation = async (annotationId) => {
    if (!window.confirm('确定要删除这个标注吗？')) return;
    
    try {
      await deleteAnnotation(annotationId);
      await loadAnnotations(); // 重新加载数据
    } catch (err) {
      setError('删除标注失败: ' + err.message);
      console.error('删除标注失败:', err);
    }
  };

  // 当选中的数据集变化时重新加载
  useEffect(() => {
    loadAnnotations();
  }, [selectedDataset, loadAnnotations]);

  if (!selectedDataset) {
    return (
      <div className="annotation-manager">
        <div className="no-dataset">
          请先选择一个数据集
        </div>
      </div>
    );
  }

  return (
    <div className="annotation-manager">
      <div className="annotation-header">
        <h3>标注管理 - {selectedDataset}</h3>
        <div className="annotation-actions">
          <button 
            onClick={loadAnnotations} 
            disabled={loading}
            className="btn btn-secondary"
          >
            {loading ? '加载中...' : '刷新'}
          </button>
          <button 
            onClick={handleAutoGenerate} 
            disabled={generating || loading}
            className="btn btn-primary"
          >
            {generating ? '生成中...' : '自动生成标注'}
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {stats && (
        <div className="annotation-stats">
          <div className="stat-item">
            <span className="stat-label">总计:</span>
            <span className="stat-value">{stats.total}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">自动生成:</span>
            <span className="stat-value">{stats.auto_generated}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">手动标注:</span>
            <span className="stat-value">{stats.manual}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">已完成:</span>
            <span className="stat-value">{stats.completed}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">草稿:</span>
            <span className="stat-value">{stats.draft}</span>
          </div>
        </div>
      )}

      <div className="annotation-list">
        {loading ? (
          <div className="loading">加载标注数据中...</div>
        ) : annotations.length === 0 ? (
          <div className="no-annotations">
            <p>暂无标注数据</p>
            <p>点击"自动生成标注"按钮从生成的表格数据创建标注</p>
          </div>
        ) : (
          <div className="annotation-table">
            <div className="table-header">
              <div className="col-image">图片</div>
              <div className="col-annotator">标注员</div>
              <div className="col-type">类型</div>
              <div className="col-status">状态</div>
              <div className="col-created">创建时间</div>
              <div className="col-actions">操作</div>
            </div>
            {annotations.map(annotation => (
              <div key={annotation.id} className="table-row">
                <div className="col-image">
                  {annotation.image_filename}
                </div>
                <div className="col-annotator">
                  {annotation.annotator}
                </div>
                <div className="col-type">
                  <span className={`type-badge ${annotation.annotation_type}`}>
                    {annotation.annotation_type === 'auto_generated' ? '自动' : '手动'}
                  </span>
                </div>
                <div className="col-status">
                  <span className={`status-badge ${annotation.status}`}>
                    {annotation.status === 'completed' ? '完成' : 
                     annotation.status === 'draft' ? '草稿' : annotation.status}
                  </span>
                </div>
                <div className="col-created">
                  {new Date(annotation.created_at).toLocaleDateString()}
                </div>
                <div className="col-actions">
                  <button 
                    onClick={() => handleDeleteAnnotation(annotation.id)}
                    className="btn btn-danger btn-sm"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnnotationManager;
