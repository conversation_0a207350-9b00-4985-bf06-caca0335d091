import React, { useMemo } from 'react';

const StatsPanel = ({ cases = [] }) => {
  const stats = useMemo(() => {
    if (!cases.length) return null;

    const total = cases.length;
    const stats = {
      kdcMarkdown: { success: 0, accuracy: 0 },
      kdcPlain: { success: 0, accuracy: 0 },
      kdcKdc: { success: 0, accuracy: 0 },
      monkeyOCR: { success: 0, accuracy: 0 },
      monkeyOCRV2: { success: 0, accuracy: 0 },
      vlLLM: { success: 0, accuracy: 0 },
      monkeyOCRLocal: { success: 0, accuracy: 0 },
      tableDetection: { detected: 0, total: 0 }  // 表格检测统计
    };

    cases.forEach(caseData => {
      // KDC Markdown
      if (caseData.kdcMarkdown && caseData.kdcMarkdown.result) {
        stats.kdcMarkdown.success++;
        if (caseData.kdcMarkdown.accuracy) {
          stats.kdcMarkdown.accuracy += caseData.kdcMarkdown.accuracy;
        }
      }

      // KDC Plain
      if (caseData.kdcPlain && caseData.kdcPlain.result) {
        stats.kdcPlain.success++;
        if (caseData.kdcPlain.accuracy) {
          stats.kdcPlain.accuracy += caseData.kdcPlain.accuracy;
        }
      }

      // KDC KDC
      if (caseData.kdcKdc && caseData.kdcKdc.result) {
        stats.kdcKdc.success++;
        if (caseData.kdcKdc.accuracy) {
          stats.kdcKdc.accuracy += caseData.kdcKdc.accuracy;
        }
      }

      // MonkeyOCR - 使用与CaseItem相同的判断逻辑
      if (caseData.monkeyOCR && caseData.monkeyOCR.result) {
        // 检查新的success字段
        if (caseData.monkeyOCR.result.success !== false &&
            !caseData.monkeyOCR.result.is_timeout &&
            caseData.monkeyOCR.result.html &&
            caseData.monkeyOCR.result.html.trim() &&
            !caseData.monkeyOCR.result.html.includes('MonkeyOCR文件上传失败')) {
          stats.monkeyOCR.success++;
          if (caseData.monkeyOCR.accuracy) {
            stats.monkeyOCR.accuracy += caseData.monkeyOCR.accuracy;
          }
        }
      } else if (caseData.monkeyOCR && caseData.monkeyOCR.html &&
                 caseData.monkeyOCR.html.trim() &&
                 !caseData.monkeyOCR.html.includes('MonkeyOCR文件上传失败')) {
        // 兼容旧格式
        stats.monkeyOCR.success++;
        if (caseData.monkeyOCR.accuracy) {
          stats.monkeyOCR.accuracy += caseData.monkeyOCR.accuracy;
        }
      }

      // MonkeyOCR V2 - 使用与CaseItem相同的判断逻辑
      if (caseData.monkeyOCRV2 && caseData.monkeyOCRV2.result) {
        // 检查新的success字段
        if (caseData.monkeyOCRV2.result.success !== false &&
            !caseData.monkeyOCRV2.result.is_timeout &&
            caseData.monkeyOCRV2.result.html &&
            caseData.monkeyOCRV2.result.html.trim() &&
            !caseData.monkeyOCRV2.result.html.includes('MonkeyOCR文件上传失败')) {
          stats.monkeyOCRV2.success++;
          if (caseData.monkeyOCRV2.accuracy) {
            stats.monkeyOCRV2.accuracy += caseData.monkeyOCRV2.accuracy;
          }
        }
      } else if (caseData.monkeyOCRV2 && caseData.monkeyOCRV2.html &&
                 caseData.monkeyOCRV2.html.trim() &&
                 !caseData.monkeyOCRV2.html.includes('MonkeyOCR文件上传失败')) {
        // 兼容旧格式
        stats.monkeyOCRV2.success++;
        if (caseData.monkeyOCRV2.accuracy) {
          stats.monkeyOCRV2.accuracy += caseData.monkeyOCRV2.accuracy;
        }
      }

      // VL LLM - 处理两种不同的数据结构
      if (caseData.vlLLMResult && caseData.vlLLMResult.result) {
        let hasValidContent = false;
        const result = caseData.vlLLMResult.result;

        // 检查两种数据结构
        let choices = [];
        if (result.content && result.content.choices) {
          choices = result.content.choices;
        } else if (result.choices) {
          choices = result.choices;
        }

        if (choices.length > 0) {
          const content = choices[0].message?.content;
          hasValidContent = !!(content && content.trim());
        }

        if (hasValidContent) {
          stats.vlLLM.success++;
          if (caseData.vlLLMResult.accuracy) {
            stats.vlLLM.accuracy += caseData.vlLLMResult.accuracy;
          }
        }
      }

      // MonkeyOCR Local - 使用与CaseItem相同的判断逻辑
      if (caseData.monkeyOCRLocal && caseData.monkeyOCRLocal.results &&
          Array.isArray(caseData.monkeyOCRLocal.results) &&
          caseData.monkeyOCRLocal.results.length > 0) {
        const firstResult = caseData.monkeyOCRLocal.results[0];
        if (firstResult.result &&
            firstResult.result.success !== false &&
            ((firstResult.result.html && firstResult.result.html.trim()) ||
             (firstResult.result.markdown && firstResult.result.markdown.trim()))) {
          stats.monkeyOCRLocal.success++;
          if (caseData.monkeyOCRLocal.accuracy) {
            stats.monkeyOCRLocal.accuracy += caseData.monkeyOCRLocal.accuracy;
          }
        }
      } else if (caseData.monkeyOCRLocal && caseData.monkeyOCRLocal.result) {
        // 兼容旧格式
        if (caseData.monkeyOCRLocal.result.success !== false &&
            ((caseData.monkeyOCRLocal.result.html && caseData.monkeyOCRLocal.result.html.trim()) ||
             (caseData.monkeyOCRLocal.result.markdown && caseData.monkeyOCRLocal.result.markdown.trim()))) {
          stats.monkeyOCRLocal.success++;
          if (caseData.monkeyOCRLocal.accuracy) {
            stats.monkeyOCRLocal.accuracy += caseData.monkeyOCRLocal.accuracy;
          }
        }
      }

      // 表格检测统计
      stats.tableDetection.total++;
      if (caseData.features && caseData.features.kdc && caseData.features.kdc.table_detection && caseData.features.kdc.table_detection.has_table) {
        stats.tableDetection.detected++;
      }
    });

    // 计算成功率和平均准确率
    Object.keys(stats).forEach(key => {
      if (key === 'tableDetection') {
        // 表格检测使用特殊的计算方式
        const detectionRate = (stats[key].detected / stats[key].total) * 100;
        stats[key] = {
          detectionRate: detectionRate.toFixed(1),
          detected: stats[key].detected,
          total: stats[key].total
        };
      } else {
        const successRate = (stats[key].success / total) * 100;
        const avgAccuracy = stats[key].success ? (stats[key].accuracy / stats[key].success) : 0;
        stats[key] = {
          successRate: successRate.toFixed(1),
          avgAccuracy: avgAccuracy.toFixed(1)
        };
      }
    });

    return stats;
  }, [cases]);

  if (!stats) {
    return (
      <div className="stats-panel">
        <div className="stats-panel-header">
          <h3>统计指标</h3>
        </div>
        <div className="stats-empty">暂无数据</div>
      </div>
    );
  }

  return (
    <div className="stats-panel">
      <div className="stats-panel-header">
        <h3>统计指标</h3>
      </div>
      <div className="stats-grid">
        <div className="stat-item">
          <div className="stat-label">KDC Markdown 成功率</div>
          <div className={`stat-value ${stats.kdcMarkdown.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.kdcMarkdown.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">KDC Plain 成功率</div>
          <div className={`stat-value ${stats.kdcPlain.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.kdcPlain.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">MonkeyOCR(table) 成功率</div>
          <div className={`stat-value ${stats.monkeyOCR.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.monkeyOCR.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">MonkeyOCR(parse) 成功率</div>
          <div className={`stat-value ${stats.monkeyOCRV2.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.monkeyOCRV2.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">VL-LLM 成功率</div>
          <div className={`stat-value ${stats.vlLLM.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.vlLLM.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">MonkeyOCR(local) 成功率</div>
          <div className={`stat-value ${stats.monkeyOCRLocal.successRate >= 80 ? 'success' : 'warning'}`}>
            {stats.monkeyOCRLocal.successRate}%
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-label">KDC 表格检测率</div>
          <div className={`stat-value ${stats.tableDetection.detectionRate >= 50 ? 'success' : 'warning'}`}>
            {stats.tableDetection.detectionRate}% ({stats.tableDetection.detected}/{stats.tableDetection.total})
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsPanel; 