.table-renderer {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.table-renderer-content {
  width: 100%;
  height: 100%;
}

.table-renderer-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  color: #666;
  font-style: italic;
}

.empty-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dee2e6;
}

/* 表格样式 */
.table-renderer-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 14px;
}

.table-renderer-content th,
.table-renderer-content td {
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
  word-wrap: break-word;
  word-break: break-all;
}

.table-renderer-content th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-renderer-content tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.table-renderer-content tbody tr:hover {
  background-color: #e3f2fd;
}

/* JSON表格特殊样式 */
.json-table {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.json-table th {
  background-color: #e9ecef;
  font-weight: bold;
}

.json-table td {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.json-value {
  margin: 0;
  padding: 4px;
  background-color: #f8f9fa;
  border-radius: 2px;
  font-size: 11px;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 100px;
  overflow-y: auto;
}

/* 纯文本样式 */
.plain-text {
  margin: 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #333;
  border: 1px solid #dee2e6;
  height: auto;
  min-height: 100px;
  max-height: 1500px;
  overflow-y: auto;
}

/* JSON内容样式 */
.json-content {
  margin: 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #333;
  border: 1px solid #dee2e6;
  height: auto;
  min-height: 100px;
  max-height: 1500px;
  overflow-y: auto;
}

/* 错误样式 */
.error {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 12px;
  margin: 8px 0;
  font-size: 14px;
}

/* 滚动条样式 */
.table-renderer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-renderer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-renderer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-renderer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-renderer-content table {
    font-size: 12px;
  }
  
  .table-renderer-content th,
  .table-renderer-content td {
    padding: 6px 8px;
  }
  
  .json-table {
    font-size: 10px;
  }
  
  .json-value {
    font-size: 10px;
    max-height: 60px;
  }
  
  .plain-text,
  .json-content {
    font-size: 11px;
    padding: 8px;
  }
}
