import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useVirtualization, useLazyLoading } from '../hooks/useVirtualization';
import './VirtualList.css';

const VirtualList = ({
  items = [],
  itemHeight = 60,
  height = 400,
  renderItem,
  onLoadMore,
  hasMore = false,
  loading = false,
  className = '',
  overscan = 5
}) => {
  const containerRef = useRef(null);
  const [containerHeight, setContainerHeight] = useState(height);

  // 虚拟滚动
  const {
    visibleItems,
    totalHeight,
    handleScroll: handleVirtualScroll
  } = useVirtualization({
    items,
    itemHeight,
    containerHeight,
    overscan
  });

  // 懒加载
  const {
    loading: lazyLoading,
    handleScroll: handleLazyScroll
  } = useLazyLoading({
    loadMore: onLoadMore,
    hasMore,
    threshold: 100
  });

  // 组合滚动处理
  const handleScroll = useCallback((event) => {
    handleVirtualScroll(event);
    if (onLoadMore) {
      handleLazyScroll(event);
    }
  }, [handleVirtualScroll, handleLazyScroll, onLoadMore]);

  // 监听容器大小变化
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 如果没有项目，显示空状态
  if (items.length === 0 && !loading) {
    return (
      <div className={`virtual-list virtual-list-empty ${className}`} ref={containerRef}>
        <div className="virtual-list-empty-content">
          <div className="virtual-list-empty-icon">📋</div>
          <div className="virtual-list-empty-text">暂无数据</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`virtual-list ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div
        className="virtual-list-content"
        style={{ height: totalHeight, position: 'relative' }}
      >
        {visibleItems.map((item) => (
          <div
            key={item.id || item.index}
            className="virtual-list-item"
            style={item.style}
          >
            {renderItem(item, item.index)}
          </div>
        ))}
        
        {/* 加载更多指示器 */}
        {(loading || lazyLoading) && (
          <div className="virtual-list-loading">
            <div className="virtual-list-loading-spinner"></div>
            <span>加载中...</span>
          </div>
        )}
        
        {/* 没有更多数据指示器 */}
        {!hasMore && items.length > 0 && !loading && (
          <div className="virtual-list-no-more">
            没有更多数据了
          </div>
        )}
      </div>
    </div>
  );
};

export default VirtualList;
