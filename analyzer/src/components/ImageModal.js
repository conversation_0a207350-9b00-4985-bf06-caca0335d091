import React, { useEffect, useCallback, useState } from 'react';
import './ImageModal.css';

const ImageModal = ({ imageUrl, onClose, title = '' }) => {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden'; // 防止背景滚动

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [handleKeyDown]);

  const handleOverlayClick = (event) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  const handleCloseClick = () => {
    onClose();
  };

  const handleWheel = useCallback((event) => {
    event.preventDefault();
    const delta = event.deltaY > 0 ? -0.1 : 0.1;
    setScale(prevScale => Math.max(0.1, Math.min(5, prevScale + delta)));
  }, []);

  const handleMouseDown = useCallback((event) => {
    if (scale > 1) {
      setIsDragging(true);
      setDragStart({
        x: event.clientX - position.x,
        y: event.clientY - position.y
      });
    }
  }, [scale, position]);

  const handleMouseMove = useCallback((event) => {
    if (isDragging) {
      setPosition({
        x: event.clientX - dragStart.x,
        y: event.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const resetZoom = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  const zoomIn = () => {
    setScale(prevScale => Math.min(5, prevScale + 0.2));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(0.1, prevScale - 0.2));
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  if (!imageUrl) {
    return null;
  }

  return (
    <div className="image-modal-overlay" onClick={handleOverlayClick}>
      <div className="image-modal-content">
        <button 
          className="image-modal-close"
          onClick={handleCloseClick}
          title="关闭 (ESC)"
        >
          ×
        </button>
        
        {title && (
          <div className="image-modal-title">
            {title}
          </div>
        )}
        
        <div
          className="image-modal-image-container"
          onWheel={handleWheel}
          onMouseDown={handleMouseDown}
          style={{ cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
        >
          <img
            src={imageUrl}
            alt={title || '图片预览'}
            className="image-modal-image"
            style={{
              transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
              transition: isDragging ? 'none' : 'transform 0.1s ease-out'
            }}
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'block';
            }}
            draggable={false}
          />
          <div className="image-modal-error" style={{ display: 'none' }}>
            图片加载失败
          </div>
        </div>
        
        <div className="image-modal-controls">
          <div className="zoom-controls">
            <button
              className="image-modal-btn zoom-btn"
              onClick={zoomOut}
              title="缩小"
            >
              -
            </button>
            <span className="zoom-level">{Math.round(scale * 100)}%</span>
            <button
              className="image-modal-btn zoom-btn"
              onClick={zoomIn}
              title="放大"
            >
              +
            </button>
            <button
              className="image-modal-btn"
              onClick={resetZoom}
              title="重置缩放"
            >
              重置
            </button>
          </div>
          <div className="action-controls">
            <button
              className="image-modal-btn"
              onClick={() => window.open(imageUrl, '_blank')}
              title="在新窗口中打开"
            >
              新窗口打开
            </button>
            <button
              className="image-modal-btn"
              onClick={handleCloseClick}
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageModal;
