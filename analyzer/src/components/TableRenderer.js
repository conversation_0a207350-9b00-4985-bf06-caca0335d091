import React from 'react';
import { convertMarkdownTableToHTML } from '../utils/dataProcessor';
import KdcCanvasRenderer from './KdcCanvasRenderer';
import './TableRenderer.css';

const TableRenderer = ({ 
  content, 
  type = 'markdown', 
  className = '', 
  placeholder = '无表格内容' 
}) => {
  // 检查content是否为空或无效
  const isEmpty = !content ||
    (typeof content === 'string' && content.trim() === '') ||
    (typeof content === 'object' && type === 'kdc' && (!content.data || !Array.isArray(content.data) || content.data.length === 0));

  console.log('TableRenderer isEmpty check:', {
    content,
    type,
    isEmpty,
    hasContent: !!content,
    isKdc: type === 'kdc',
    hasData: !!(content && content.data),
    isDataArray: !!(content && content.data && Array.isArray(content.data)),
    dataLength: content && content.data ? content.data.length : 0
  });

  if (isEmpty) {
    return (
      <div className={`table-renderer table-renderer-empty ${className}`}>
        <div className="empty-message">{placeholder}</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (type) {
      case 'markdown':
        // 处理markdown中的图片链接
        const processedMarkdown = processMarkdownImages(content);
        return convertMarkdownToHTML(processedMarkdown);

      case 'latex':
        // 处理LaTeX表格格式
        return convertLatexTableToHTML(content);

      case 'html':
        // 检查是否是错误消息或超时消息
        if (typeof content === 'string') {
          if (content.includes('超时') || content.includes('Please upload') || content.includes('upload')) {
            return `<div style="color: #dc3545; padding: 10px; text-align: center; border: 1px solid #dc3545; border-radius: 4px; background-color: #f8d7da;">${content}</div>`;
          }

          // 检查是否是HTML表格内容
          if (content.includes('<table') || content.includes('<div') || content.includes('<html')) {
            // 使用iframe隔离HTML内容，防止影响外层样式
            // 注入CSS样式确保表格有边框
            const tableStyles = `
              <style>
                body {
                  margin: 0;
                  padding: 10px;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                  font-size: 14px;
                  line-height: 1.5;
                }
                table {
                  width: 100%;
                  border-collapse: collapse;
                  margin: 0;
                  border: 1px solid #dee2e6;
                }
                th, td {
                  border: 1px solid #dee2e6;
                  padding: 8px 12px;
                  text-align: left;
                  vertical-align: top;
                  word-wrap: break-word;
                }
                th {
                  background-color: #f8f9fa;
                  font-weight: 600;
                  color: #333;
                }
                tbody tr:nth-child(even) {
                  background-color: #f9f9f9;
                }
                tbody tr:hover {
                  background-color: #e3f2fd;
                }
                /* 确保所有表格都有边框，包括动态生成的 */
                table, table * {
                  border-collapse: collapse !important;
                }
                table th, table td {
                  border: 1px solid #dee2e6 !important;
                }
              </style>
            `;

            // 构建完整的HTML文档
            let htmlContent = content;
            if (!content.includes('<html')) {
              // 如果不是完整的HTML文档，包装成完整文档
              htmlContent = `<!DOCTYPE html><html><head>${tableStyles}</head><body>${content}</body></html>`;
            } else {
              // 如果是完整的HTML文档，在head中注入样式
              if (content.includes('<head>')) {
                htmlContent = content.replace('<head>', `<head>${tableStyles}`);
              } else if (content.includes('<html>')) {
                htmlContent = content.replace('<html>', `<html><head>${tableStyles}</head>`);
              } else {
                htmlContent = `<!DOCTYPE html><html><head>${tableStyles}</head><body>${content}</body></html>`;
              }
            }

            const safeHtml = htmlContent.replace(/"/g, '&quot;').replace(/'/g, '&#x27;');

            const iframeId = 'iframe_' + Math.random().toString(36).substr(2, 9);
            return `
              <iframe
                id="${iframeId}"
                srcdoc="${safeHtml}"
                style="width:100%;height:auto;min-height:800px;border:1px solid #ddd;border-radius:3px;background:white;"
                sandbox="allow-scripts"
                onload="
                  const iframe = this;
                  setTimeout(() => {
                    try {
                      const doc = iframe.contentDocument || iframe.contentWindow.document;
                      const height = Math.max(doc.body.scrollHeight, doc.documentElement.scrollHeight);
                      iframe.style.height = Math.min(height + 20, 2000) + 'px';
                    } catch(e) {
                      iframe.style.height = '800px';
                    }
                  }, 200);
                "
              ></iframe>
            `;
          } else {
            // 普通文本内容，直接显示
            return `<pre style="white-space: pre-wrap; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">${content}</pre>`;
          }
        }
        return content;

      case 'json':
        try {
          const jsonData = typeof content === 'string' ? JSON.parse(content) : content;
          return renderJSONAsTable(jsonData);
        } catch (error) {
          return `<div class="error">JSON解析失败: ${error.message}</div>`;
        }

      case 'plain':
        return `<pre class="plain-text">${content}</pre>`;

      case 'kdc':
        // KDC类型使用Canvas渲染
        return null; // 特殊处理，在下面返回KdcCanvasRenderer组件

      default:
        // 自动检测格式
        if (typeof content === 'string') {
          if (content.includes('\\begin{tabular}')) {
            return convertLatexTableToHTML(content);
          } else if (content.includes('|') && content.includes('-')) {
            const markdownContent = processMarkdownImages(content);
            return convertMarkdownTableToHTML(markdownContent);
          }
        }
        const defaultMarkdown = processMarkdownImages(content);
        return convertMarkdownTableToHTML(defaultMarkdown);
    }
  };

  // 特殊处理KDC类型
  if (type === 'kdc') {
    console.log('TableRenderer KDC Debug:', { content, type, isEmpty });
    return (
      <div className={`table-renderer ${className}`}>
        <KdcCanvasRenderer kdcData={content} placeholder={placeholder} />
      </div>
    );
  }

  return (
    <div className={`table-renderer ${className}`}>
      <div 
        className="table-renderer-content"
        dangerouslySetInnerHTML={{ __html: renderContent() }}
      />
    </div>
  );
};

/**
 * 处理markdown中的图片链接
 */
const processMarkdownImages = (content) => {
  if (!content) return content;

  // 匹配markdown图片语法: ![alt](url)
  const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;

  return content.replace(imageRegex, (match, alt, url) => {
    // 处理相对路径和特殊字符
    const processedUrl = url.startsWith('http') ? url : url.replace(/^\.\//, '');

    // 将图片链接转换为HTML img标签，并添加样式和错误处理
    // 修复：确保图片标签格式正确，避免属性分行显示
    return `<div class="markdown-image-container"><img src="${processedUrl}" alt="${alt || '图片'}" class="markdown-image" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; margin: 5px; border-radius: 4px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" /><div class="markdown-image-error" style="display:none; color: #dc3545; font-size: 12px; padding: 5px; text-align: center;"><span>图片加载失败</span><br/><small style="color: #666;">${processedUrl}</small></div></div>`;
  });
};

/**
 * 将完整的Markdown内容转换为HTML - 参考HTML报告生成逻辑
 */
const convertMarkdownToHTML = (markdownContent) => {
  if (!markdownContent || typeof markdownContent !== 'string') {
    return "<div style='color:gray;'>无Markdown内容</div>";
  }

  try {
    // 分割内容为行
    const lines = markdownContent.split('\n');
    const htmlParts = [];
    let currentSection = [];
    let inTable = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检查是否是表格行
      if (line.trim().startsWith('|') && line.trim().endsWith('|')) {
        if (!inTable) {
          // 处理之前的非表格内容
          if (currentSection.length > 0) {
            htmlParts.push(convertMarkdownSection(currentSection.join('\n')));
            currentSection = [];
          }
          inTable = true;
        }
        currentSection.push(line);
      } else if (inTable && line.trim() === '') {
        // 表格结束
        if (currentSection.length > 0) {
          htmlParts.push(convertMarkdownTableToHTML(currentSection.join('\n')));
          currentSection = [];
        }
        inTable = false;
      } else if (inTable) {
        // 表格结束，处理表格内容
        if (currentSection.length > 0) {
          htmlParts.push(convertMarkdownTableToHTML(currentSection.join('\n')));
          currentSection = [];
        }
        inTable = false;
        currentSection.push(line);
      } else {
        // 非表格内容
        currentSection.push(line);
      }
    }

    // 处理最后的内容
    if (currentSection.length > 0) {
      if (inTable) {
        htmlParts.push(convertMarkdownTableToHTML(currentSection.join('\n')));
      } else {
        htmlParts.push(convertMarkdownSection(currentSection.join('\n')));
      }
    }

    return htmlParts.join('');
  } catch (error) {
    return `<div style='color:red;'>Markdown解析失败: ${error.message}</div>`;
  }
};

/**
 * 转换Markdown段落内容（非表格）
 */
const convertMarkdownSection = (content) => {
  if (!content.trim()) return '';

  let html = content;

  // 处理标题
  html = html.replace(/^# (.+)$/gm, '<h1 style="margin: 10px 0; color: #333;">$1</h1>');
  html = html.replace(/^## (.+)$/gm, '<h2 style="margin: 8px 0; color: #333;">$1</h2>');
  html = html.replace(/^### (.+)$/gm, '<h3 style="margin: 6px 0; color: #333;">$1</h3>');

  // 处理粗体
  html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');

  // 处理斜体
  html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');

  // 处理换行
  html = html.replace(/\n/g, '<br/>');

  // 包装在div中
  return `<div style="margin: 10px 0; line-height: 1.5;">${html}</div>`;
};

/**
 * 将LaTeX表格转换为HTML
 */
const convertLatexTableToHTML = (latexContent) => {
  if (!latexContent || typeof latexContent !== 'string') {
    return "<div style='color:gray;'>无LaTeX表格内容</div>";
  }

  try {
    // 查找 \begin{tabular} ... \end{tabular} 块
    const tabularMatch = latexContent.match(/\\begin\{tabular\}\{[^}]*\}(.*?)\\end\{tabular\}/s);
    if (!tabularMatch) {
      return "<div style='color:gray;'>未找到有效的LaTeX表格</div>";
    }

    const tableContent = tabularMatch[1];
    
    // 分割行
    const lines = tableContent.split('\\\\').map(line => line.trim()).filter(line => line);
    
    if (lines.length === 0) {
      return "<div style='color:gray;'>LaTeX表格内容为空</div>";
    }

    // 构建HTML表格
    const html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"];
    
    let isFirstRow = true;
    let hasHeader = false;

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];
      
      // 跳过空行和命令行
      if (!line || line.startsWith('\\') || line.includes('\\multicolumn')) {
        if (line.includes('\\multicolumn')) {
          // 处理标题行
          const titleMatch = line.match(/\\multicolumn\{[^}]*\}\{[^}]*\}\{([^}]*)\}/);
          if (titleMatch) {
            html.push("<caption style='font-weight: bold; padding: 8px;'>" + titleMatch[1].replace(/\\bf\s*/, '') + "</caption>");
          }
        }
        continue;
      }
      
      // 分割单元格
      const cells = line.split('&').map(cell => cell.trim());
      
      if (cells.length === 0) continue;

      // 第一行数据作为表头
      if (isFirstRow && !hasHeader) {
        html.push("<thead><tr>");
        cells.forEach(cell => {
          const cleanCell = cell.replace(/\\textbf\{([^}]*)\}/g, '$1').trim();
          html.push(`<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>${cleanCell}</th>`);
        });
        html.push("</tr></thead><tbody>");
        hasHeader = true;
        isFirstRow = false;
      } else {
        // 数据行
        html.push("<tr>");
        cells.forEach(cell => {
          const cleanCell = cell.replace(/\\textbf\{([^}]*)\}/g, '<strong>$1</strong>')
                               .replace(/\\text\{([^}]*)\}/g, '$1')
                               .trim();
          html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${cleanCell}</td>`);
        });
        html.push("</tr>");
      }
    }
    
    if (hasHeader) {
      html.push("</tbody>");
    }
    html.push("</table>");
    
    return html.join('');
  } catch (error) {
    return `<div style='color:red;'>LaTeX表格解析失败: ${error.message}</div>`;
  }
};

/**
 * 将JSON数据渲染为表格
 */
const renderJSONAsTable = (jsonData) => {
  if (!jsonData || typeof jsonData !== 'object') {
    return "<div style='color:gray;'>无效的JSON数据</div>";
  }

  if (Array.isArray(jsonData)) {
    if (jsonData.length === 0) {
      return "<div style='color:gray;'>JSON数组为空</div>";
    }

    // 获取所有键作为表头
    const allKeys = new Set();
    jsonData.forEach(item => {
      if (typeof item === 'object' && item !== null) {
        Object.keys(item).forEach(key => allKeys.add(key));
      }
    });

    const headers = Array.from(allKeys);
    
    const html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"];
    
    // 表头
    html.push("<thead><tr>");
    headers.forEach(header => {
      html.push(`<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>${header}</th>`);
    });
    html.push("</tr></thead><tbody>");
    
    // 数据行
    jsonData.forEach(item => {
      html.push("<tr>");
      headers.forEach(header => {
        const value = item && typeof item === 'object' ? (item[header] || '') : '';
        html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${value}</td>`);
      });
      html.push("</tr>");
    });
    
    html.push("</tbody></table>");
    return html.join('');
  } else {
    // 单个对象，显示键值对
    const html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"];
    html.push("<thead><tr><th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>属性</th><th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>值</th></tr></thead><tbody>");
    
    Object.entries(jsonData).forEach(([key, value]) => {
      html.push("<tr>");
      html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${key}</td>`);
      html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${typeof value === 'object' ? JSON.stringify(value) : value}</td>`);
      html.push("</tr>");
    });
    
    html.push("</tbody></table>");
    return html.join('');
  }
};

export default TableRenderer;
