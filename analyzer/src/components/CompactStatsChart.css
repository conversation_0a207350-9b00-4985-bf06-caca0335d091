.compact-stats-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.chart-header {
  padding: 16px 20px 8px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-subtitle {
  margin: 4px 0 0;
  font-size: 12px;
  opacity: 0.9;
  font-weight: 400;
}

.chart-container {
  padding: 20px;
  background: #fafafa;
}

.chart-empty {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 自定义Recharts样式 */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: #e8e8e8;
  stroke-width: 1;
}

.recharts-tooltip-wrapper {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recharts-default-tooltip {
  background: white !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.recharts-tooltip-label {
  color: #333 !important;
  font-weight: 600 !important;
  margin-bottom: 4px !important;
}

.recharts-tooltip-item {
  color: #666 !important;
}

.recharts-legend-wrapper {
  padding-top: 10px !important;
}

.recharts-legend-item-text {
  color: #666 !important;
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    padding: 12px 16px 6px;
  }
  
  .chart-header h3 {
    font-size: 16px;
  }
  
  .chart-subtitle {
    font-size: 11px;
  }
  
  .chart-container {
    padding: 15px;
  }
}

/* 动画效果 */
.compact-stats-chart {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 柱状图颜色主题 */
.recharts-bar-rectangle {
  transition: opacity 0.2s ease;
}

.recharts-bar-rectangle:hover {
  opacity: 0.8;
}

/* 成功率柱状图 */
.recharts-bar[data-key="successRate"] .recharts-bar-rectangle {
  fill: #4f46e5;
}

/* 准确率柱状图 */
.recharts-bar[data-key="avgAccuracy"] .recharts-bar-rectangle {
  fill: #10b981;
}
