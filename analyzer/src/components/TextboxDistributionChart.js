import React, { useMemo } from 'react';

/**
 * 文本框数量分布图表组件
 * 展示表格单元格中文本框数量的分布统计
 */
const TextboxDistributionChart = ({ distributionData, className = '' }) => {
  const chartData = useMemo(() => {
    if (!distributionData || !distributionData.cell_textbox_counts) {
      return null;
    }

    const { cell_textbox_counts, total_cells, max_textboxes_per_cell, avg_textboxes_per_cell, outlier_cells } = distributionData;
    
    // 转换数据为图表格式
    const dataPoints = [];
    const maxCount = Math.max(...Object.keys(cell_textbox_counts).map(Number));
    
    for (let i = 1; i <= maxCount; i++) {
      const cellCount = cell_textbox_counts[i] || 0;
      if (cellCount > 0) {  // 只显示有数据的点
        dataPoints.push({
          textboxCount: i,
          cellCount: cellCount,
          percentage: ((cellCount / total_cells) * 100).toFixed(1)
        });
      }
    }

    // 检测异常值
    const outlierThreshold = avg_textboxes_per_cell * 2;
    const hasOutliers = outlier_cells && outlier_cells.length > 0;

    return {
      dataPoints,
      total_cells,
      max_textboxes_per_cell,
      avg_textboxes_per_cell,
      outlierThreshold,
      hasOutliers,
      outlier_cells
    };
  }, [distributionData]);

  if (!chartData) {
    return (
      <div className={`textbox-distribution-chart ${className}`}>
        <div className="chart-placeholder">暂无文本框分布数据</div>
      </div>
    );
  }

  const { dataPoints, total_cells, max_textboxes_per_cell, avg_textboxes_per_cell, outlierThreshold, hasOutliers, outlier_cells } = chartData;

  // 计算图表尺寸
  const chartWidth = 400;
  const chartHeight = 200;
  const margin = { top: 20, right: 20, bottom: 40, left: 40 };
  const innerWidth = chartWidth - margin.left - margin.right;
  const innerHeight = chartHeight - margin.top - margin.bottom;

  // 计算比例尺
  const maxCellCount = Math.max(...dataPoints.map(d => d.cellCount));
  const xScale = (textboxCount) => ((textboxCount - 1) / (max_textboxes_per_cell - 1)) * innerWidth;
  const yScale = (cellCount) => innerHeight - (cellCount / maxCellCount) * innerHeight;

  return (
    <div className={`textbox-distribution-chart ${className}`}>
      <div className="chart-header">
        <h4>📊 单元格文本框数量分布</h4>
        <div className="chart-summary">
          <span>总单元格: {total_cells}</span>
          <span>平均文本框数: {avg_textboxes_per_cell}</span>
          <span>最大文本框数: {max_textboxes_per_cell}</span>
        </div>
      </div>

      <div className="chart-container">
        <svg width={chartWidth} height={chartHeight} style={{ border: '1px solid #e0e0e0', backgroundColor: '#fafafa' }}>
          {/* 坐标轴 */}
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            {/* Y轴 */}
            <line x1={0} y1={0} x2={0} y2={innerHeight} stroke="#666" strokeWidth={1} />
            {/* X轴 */}
            <line x1={0} y1={innerHeight} x2={innerWidth} y2={innerHeight} stroke="#666" strokeWidth={1} />
            
            {/* Y轴刻度和标签 */}
            {[0, Math.ceil(maxCellCount / 4), Math.ceil(maxCellCount / 2), Math.ceil(maxCellCount * 3 / 4), maxCellCount].map((value, index) => {
              const y = yScale(value);
              return (
                <g key={index}>
                  <line x1={-5} y1={y} x2={0} y2={y} stroke="#666" strokeWidth={1} />
                  <text x={-8} y={y + 3} textAnchor="end" fontSize="10" fill="#666">{value}</text>
                </g>
              );
            })}

            {/* X轴刻度和标签 */}
            {dataPoints.map((point, index) => {
              const x = xScale(point.textboxCount);
              return (
                <g key={index}>
                  <line x1={x} y1={innerHeight} x2={x} y2={innerHeight + 5} stroke="#666" strokeWidth={1} />
                  <text x={x} y={innerHeight + 18} textAnchor="middle" fontSize="10" fill="#666">
                    {point.textboxCount}
                  </text>
                </g>
              );
            })}

            {/* 数据点和柱状图 */}
            {dataPoints.map((point, index) => {
              const x = xScale(point.textboxCount);
              const y = yScale(point.cellCount);
              const barHeight = innerHeight - y;
              const isOutlier = point.textboxCount > outlierThreshold;
              
              return (
                <g key={index}>
                  {/* 柱状图 */}
                  <rect
                    x={x - 8}
                    y={y}
                    width={16}
                    height={barHeight}
                    fill={isOutlier ? '#ff6b6b' : '#4dabf7'}
                    stroke={isOutlier ? '#e03131' : '#339af0'}
                    strokeWidth={1}
                    opacity={0.8}
                  />
                  
                  {/* 数据标签 */}
                  <text
                    x={x}
                    y={y - 5}
                    textAnchor="middle"
                    fontSize="9"
                    fill="#333"
                    fontWeight="bold"
                  >
                    {point.cellCount}
                  </text>
                  
                  {/* 百分比标签 */}
                  <text
                    x={x}
                    y={y - 15}
                    textAnchor="middle"
                    fontSize="8"
                    fill="#666"
                  >
                    {point.percentage}%
                  </text>
                </g>
              );
            })}

            {/* 平均值线 */}
            {avg_textboxes_per_cell > 1 && (
              <line
                x1={xScale(avg_textboxes_per_cell)}
                y1={0}
                x2={xScale(avg_textboxes_per_cell)}
                y2={innerHeight}
                stroke="#28a745"
                strokeWidth={2}
                strokeDasharray="5,5"
                opacity={0.7}
              />
            )}

            {/* 异常阈值线 */}
            {outlierThreshold > 1 && outlierThreshold <= max_textboxes_per_cell && (
              <line
                x1={xScale(outlierThreshold)}
                y1={0}
                x2={xScale(outlierThreshold)}
                y2={innerHeight}
                stroke="#dc3545"
                strokeWidth={2}
                strokeDasharray="3,3"
                opacity={0.7}
              />
            )}
          </g>

          {/* 坐标轴标签 */}
          <text x={chartWidth / 2} y={chartHeight - 5} textAnchor="middle" fontSize="12" fill="#333">
            每个单元格内的文本框数量
          </text>
          <text
            x={15}
            y={chartHeight / 2}
            textAnchor="middle"
            fontSize="12"
            fill="#333"
            transform={`rotate(-90, 15, ${chartHeight / 2})`}
          >
            单元格数量
          </text>
        </svg>

        {/* 图例 */}
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#4dabf7' }}></div>
            <span>正常单元格</span>
          </div>
          {hasOutliers && (
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#ff6b6b' }}></div>
              <span>异常单元格 (文本框数量 > {outlierThreshold.toFixed(1)})</span>
            </div>
          )}
          <div className="legend-item">
            <div className="legend-line" style={{ borderTop: '2px dashed #28a745' }}></div>
            <span>平均值 ({avg_textboxes_per_cell})</span>
          </div>
          {outlierThreshold > 1 && outlierThreshold <= max_textboxes_per_cell && (
            <div className="legend-item">
              <div className="legend-line" style={{ borderTop: '2px dashed #dc3545' }}></div>
              <span>异常阈值 ({outlierThreshold.toFixed(1)})</span>
            </div>
          )}
        </div>
      </div>

      {/* 异常检测结果 */}
      {hasOutliers && (
        <div className="outlier-detection">
          <h5>🚨 异常检测结果</h5>
          <p>检测到 {outlier_cells.length} 种异常情况：</p>
          <ul>
            {outlier_cells.map((outlier, index) => (
              <li key={index}>
                包含 <strong>{outlier.textbox_count}</strong> 个文本框的单元格
                （超过阈值 {outlier.threshold.toFixed(1)}）
              </li>
            ))}
          </ul>
          <p className="outlier-note">
            💡 这些单元格可能包含复杂的内容结构，需要特别关注解析质量。
          </p>
        </div>
      )}

      <style jsx>{`
        .textbox-distribution-chart {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          margin: 16px 0;
        }

        .chart-header h4 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 16px;
        }

        .chart-summary {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;
          font-size: 12px;
          color: #666;
        }

        .chart-summary span {
          background: #f8f9fa;
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .chart-container {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .chart-legend {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          margin-top: 12px;
          font-size: 11px;
        }

        .legend-item {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-line {
          width: 20px;
          height: 1px;
        }

        .outlier-detection {
          margin-top: 16px;
          padding: 12px;
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 6px;
        }

        .outlier-detection h5 {
          margin: 0 0 8px 0;
          color: #856404;
          font-size: 14px;
        }

        .outlier-detection p {
          margin: 4px 0;
          color: #856404;
          font-size: 12px;
        }

        .outlier-detection ul {
          margin: 8px 0;
          padding-left: 20px;
        }

        .outlier-detection li {
          color: #856404;
          font-size: 12px;
          margin: 2px 0;
        }

        .outlier-note {
          font-style: italic;
          margin-top: 8px !important;
        }

        .chart-placeholder {
          text-align: center;
          color: #999;
          padding: 40px;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default TextboxDistributionChart;
