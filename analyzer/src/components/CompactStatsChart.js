import React, { useMemo } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LabelList } from 'recharts';
import { calculateAccuracyForCase } from '../utils/dataProcessor';
import './CompactStatsChart.css';

/**
 * 紧凑的统计指标直方图组件
 * 使用直方图展示多路解析的成功率和准确率对比
 */
const CompactStatsChart = ({ cases = [] }) => {
  const chartData = useMemo(() => {
    if (!cases.length) return [];

    const total = cases.length;
    const stats = {
      kdcMarkdown: { success: 0, accuracy: 0 },
      kdcPlain: { success: 0, accuracy: 0 },
      kdcKdc: { success: 0, accuracy: 0 },
      monkeyOCR: { success: 0, accuracy: 0 },
      monkeyOCRV2: { success: 0, accuracy: 0 },
      vlLLM: { success: 0, accuracy: 0 },
      monkeyOCRLocal: { success: 0, accuracy: 0 }
    };

    cases.forEach(caseData => {
      // 使用统一的准确率计算函数
      // 优先使用人工标注数据作为基准
      const annotationData = caseData.annotation;
      const useAnnotationAsBaseline = !!(annotationData && annotationData.table_content);

      const accuracyResult = calculateAccuracyForCase(caseData, annotationData, useAnnotationAsBaseline);
      const { accuracies } = accuracyResult;

      if (!accuracies) return;

      // KDC Markdown
      if (caseData.kdcMarkdown?.result?.data?.[0]?.markdown) {
        stats.kdcMarkdown.success++;
        stats.kdcMarkdown.accuracy += accuracies.kdcMarkdown;
      }

      // KDC Plain
      if (caseData.kdcPlain?.result?.data?.[0]?.plain) {
        stats.kdcPlain.success++;
        stats.kdcPlain.accuracy += accuracies.kdcPlain;
      }

      // KDC KDC
      if (caseData.kdcKdc?.result?.data?.[0]?.kdc) {
        stats.kdcKdc.success++;
        stats.kdcKdc.accuracy += accuracies.kdcKdc;
      }

      // MonkeyOCR (table)
      if (caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html) {
        const isSuccess = caseData.monkeyOCR?.result?.success !== false &&
                         !caseData.monkeyOCR?.result?.is_timeout &&
                         accuracies.monkeyOCR > 0;

        if (isSuccess) {
          stats.monkeyOCR.success++;
          stats.monkeyOCR.accuracy += accuracies.monkeyOCR;
        }
      }

      // MonkeyOCR (parse)
      if (caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html) {
        const isSuccess = caseData.monkeyOCRV2?.result?.success !== false &&
                         !caseData.monkeyOCRV2?.result?.is_timeout &&
                         accuracies.monkeyOCRV2 > 0;

        if (isSuccess) {
          stats.monkeyOCRV2.success++;
          stats.monkeyOCRV2.accuracy += accuracies.monkeyOCRV2;
        }
      }

      // VL LLM
      if (caseData.vlLLMResult?.result) {
        const hasValidContent = accuracies.vlLLM > 0;

        if (hasValidContent) {
          stats.vlLLM.success++;
          stats.vlLLM.accuracy += accuracies.vlLLM;
        }
      }

      // MonkeyOCR Local
      if (caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result) {
        const isSuccess = accuracies.monkeyOCRLocal > 0;

        if (isSuccess) {
          stats.monkeyOCRLocal.success++;
          stats.monkeyOCRLocal.accuracy += accuracies.monkeyOCRLocal;
        }
      }
    });

    // 转换为图表数据格式
    return [
      {
        name: 'KDC Markdown',
        successRate: ((stats.kdcMarkdown.success / total) * 100).toFixed(1),
        avgAccuracy: stats.kdcMarkdown.success ? (stats.kdcMarkdown.accuracy / stats.kdcMarkdown.success).toFixed(1) : 0
      },
      {
        name: 'KDC Plain',
        successRate: ((stats.kdcPlain.success / total) * 100).toFixed(1),
        avgAccuracy: stats.kdcPlain.success ? (stats.kdcPlain.accuracy / stats.kdcPlain.success).toFixed(1) : 0
      },
      {
        name: 'KDC KDC',
        successRate: ((stats.kdcKdc.success / total) * 100).toFixed(1),
        avgAccuracy: stats.kdcKdc.success ? (stats.kdcKdc.accuracy / stats.kdcKdc.success).toFixed(1) : 0
      },
      {
        name: 'MonkeyOCR(table)',
        successRate: ((stats.monkeyOCR.success / total) * 100).toFixed(1),
        avgAccuracy: stats.monkeyOCR.success ? (stats.monkeyOCR.accuracy / stats.monkeyOCR.success).toFixed(1) : 0
      },
      {
        name: 'MonkeyOCR(parse)',
        successRate: ((stats.monkeyOCRV2.success / total) * 100).toFixed(1),
        avgAccuracy: stats.monkeyOCRV2.success ? (stats.monkeyOCRV2.accuracy / stats.monkeyOCRV2.success).toFixed(1) : 0
      },
      {
        name: 'VL-LLM',
        successRate: ((stats.vlLLM.success / total) * 100).toFixed(1),
        avgAccuracy: stats.vlLLM.success ? (stats.vlLLM.accuracy / stats.vlLLM.success).toFixed(1) : 0
      },
      {
        name: 'MonkeyOCR(local)',
        successRate: ((stats.monkeyOCRLocal.success / total) * 100).toFixed(1),
        avgAccuracy: stats.monkeyOCRLocal.success ? (stats.monkeyOCRLocal.accuracy / stats.monkeyOCRLocal.success).toFixed(1) : 0
      }
    ];
  }, [cases]);

  if (!chartData.length) {
    return (
      <div className="compact-stats-chart">
        <div className="chart-header">
          <h3>📊 解析器性能对比</h3>
        </div>
        <div className="chart-empty">暂无数据</div>
      </div>
    );
  }

  return (
    <div className="compact-stats-chart">
      <div className="chart-header">
        <h3>📊 解析器性能对比</h3>
        <div className="chart-subtitle">成功率 vs 平均准确率</div>
      </div>
      <div className="chart-container">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
            />
            <YAxis 
              domain={[0, 100]}
              fontSize={12}
            />
            <Tooltip 
              formatter={(value, name) => [`${value}%`, name === 'successRate' ? '成功率' : '平均准确率']}
              labelFormatter={(label) => `解析器: ${label}`}
            />
            <Legend />
            <Bar
              dataKey="successRate"
              fill="#8884d8"
              name="成功率"
              radius={[2, 2, 0, 0]}
            >
              <LabelList dataKey="successRate" position="top" fontSize={10} />
            </Bar>
            <Bar
              dataKey="avgAccuracy"
              fill="#82ca9d"
              name="平均准确率"
              radius={[2, 2, 0, 0]}
            >
              <LabelList dataKey="avgAccuracy" position="top" fontSize={10} />
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CompactStatsChart;
