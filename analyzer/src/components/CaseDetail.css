.case-detail {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  max-height: calc(100vh - 200px); /* 限制最大高度 */
  overflow-y: auto; /* 允许整体滚动 */
}

.case-detail-empty {
  padding: 60px 20px;
  text-align: center;
  color: #666;
  font-style: italic;
  font-size: 16px;
}

.case-detail-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.case-detail-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.case-detail-filename {
  font-size: 14px;
  color: #666;
  font-family: monospace;
  word-break: break-all;
}

/* 案例信息块 */
.case-info-block {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 基准选择控制块 */
.baseline-control-block {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 16px 20px;
}

.baseline-toggle {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.baseline-toggle label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.baseline-toggle input[type="checkbox"] {
  margin: 0;
}

.baseline-info {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.case-info-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.case-info-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.case-info-content {
  padding: 20px;
  display: flex;
  gap: 20px;
  align-items: flex-start;
  flex-wrap: wrap;
}

/* 准确率汇总块 */
.accuracy-summary-block {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.accuracy-summary-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accuracy-summary-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.baseline-toggle {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
}

.baseline-toggle label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  margin-bottom: 3px;
  color: #555;
}

.baseline-toggle input[type="checkbox"] {
  margin: 0;
}

.baseline-info {
  color: #666;
  font-style: italic;
  font-size: 11px;
}

.accuracy-summary-content {
  padding: 20px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

/* 解析详情块 */
.parsing-details-block {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.parsing-details-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.parsing-details-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.case-detail-content {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  padding: 0 20px; /* 添加左右内边距 */
}

.case-detail-row {
  display: table-row;
}

.case-detail-cell {
  display: table-cell;
  border: 1px solid #dee2e6;
  vertical-align: top;
  position: relative;
}

/* 原始文本列 */
.case-detail-original-text {
  width: 40%; /* 调整为40%宽度 */
  padding: 0;
}

/* 渲染结果列 */
.case-detail-rendered-result {
  width: 40%; /* 调整为40%宽度 */
  padding: 0;
}

/* 准确率报告列 */
.case-detail-accuracy-report {
  width: 20%; /* 新增准确率报告列，占20%宽度 */
  padding: 0;
}

.sequence-number {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  flex: 0 0 auto;
}

.file-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  flex: 1 1 200px;
}

.file-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  word-break: break-all;
  font-size: 14px;
}

.base-name {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.image-preview-container {
  text-align: center;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  flex: 0 0 auto;
  max-width: 300px; /* 限制最大宽度 */
  margin: 0 auto; /* 居中显示 */
}

.image-preview {
  max-width: 100%;
  height: auto;
  cursor: pointer;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  transition: transform 0.2s ease;
  display: block;
  /* 优化图片加载 */
  will-change: transform;
  backface-visibility: hidden;
}

.image-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.processing-status {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  flex: 1 1 200px;
}

.status-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-summary .status-label {
  font-size: 13px;
  color: #333;
  font-weight: 600;
  margin-bottom: 4px;
}

.status-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 12px;
  border: 1px solid;
  white-space: nowrap;
}

.status-indicator.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.status-indicator.error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* 解析状态块样式 */
.parsing-status-block {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.parsing-status-block h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.status-indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.status-indicators-grid .status-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 13px;
  font-weight: 500;
}

.status-name {
  flex: 1;
}

.status-icon {
  font-weight: bold;
  font-size: 14px;
}

.cell-header {
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.cell-content {
  padding: 8px;
  min-height: 100px;
  overflow-x: auto;
  font-size: 11px;
  /* 优化滚动行为 */
  overscroll-behavior: contain; /* 防止滚动传播 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c1c1c1 #f1f1f1; /* Firefox */
}

/* 自定义滚动条样式 */
.cell-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.cell-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cell-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.cell-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 当内容较多时自动调整高度 */
.cell-content.has-content {
  height: auto;
  min-height: 150px;
}

.cell-content.has-table {
  height: auto;
  min-height: 200px;
}

.text-content {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  font-family: 'Courier New', monospace;
}

.rendered-content {
  font-size: 14px;
  line-height: 1.5;
}

.rendered-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.rendered-content th,
.rendered-content td {
  border: 1px solid #dee2e6;
  padding: 8px;
  text-align: left;
  vertical-align: top;
}

.rendered-content th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.accuracy-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  min-width: 120px;
}

.accuracy-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.accuracy-value {
  font-size: 20px;
  font-weight: bold;
  color: #007bff;
}

/* 特征信息和指标信息容器 */
.features-metrics-container {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

/* 特征信息块 */
.features-summary-block {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 0; /* 允许收缩 */
}

/* 指标信息块 */
.metrics-summary-block {
  flex: 1;
  min-width: 0; /* 允许收缩 */
}

.features-summary-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.features-summary-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.features-summary-content {
  padding: 20px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.feature-item {
  text-align: center;
  padding: 12px;
  background-color: #f1f8ff;
  border-radius: 6px;
  min-width: 100px;
  border: 1px solid #d1ecf1;
}

.feature-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 6px;
  font-weight: 500;
}

.feature-value {
  font-size: 16px;
  font-weight: bold;
  color: #0c5460;
}

.feature-value-success {
  color: #28a745;
}

.feature-value-warning {
  color: #dc3545;
}

/* 行边框样式 - 6行布局 */
.case-detail-row-1 .case-detail-cell {
  border-top: 2px solid #333;
}

.case-detail-row-6 .case-detail-cell {
  border-bottom: 2px solid #333;
}

.case-detail-row .case-detail-cell:first-child {
  border-left: 2px solid #333;
}

.case-detail-row .case-detail-cell:last-child {
  border-right: 2px solid #333;
}

/* 中间行样式 */
.case-detail-row-2 .case-detail-cell,
.case-detail-row-3 .case-detail-cell,
.case-detail-row-4 .case-detail-cell,
.case-detail-row-5 .case-detail-cell {
  border-left: 2px solid #333;
  border-right: 2px solid #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .case-detail-content {
    display: block;
  }

  .case-detail-row {
    display: block;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
  }

  .case-detail-cell {
    display: block;
    width: 100%;
    border: none;
    border-bottom: 1px solid #dee2e6;
  }

  .case-detail-cell:last-child {
    border-bottom: none;
  }

  .case-detail-data-info {
    width: 100%;
  }

  .data-info-content {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
  }

  .sequence-number {
    flex: 0 0 auto;
  }

  .file-info {
    flex: 1 1 200px;
  }

  .image-preview-container {
    flex: 0 0 auto;
  }

  .processing-status {
    flex: 1 1 200px;
  }

  .cell-content {
    height: 200px;
  }
}
