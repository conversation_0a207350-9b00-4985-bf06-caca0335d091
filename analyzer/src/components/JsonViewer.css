.json-viewer {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 12px;
  line-height: 1.4;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.json-viewer-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
}

.json-viewer-placeholder {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.json-viewer-header {
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  padding: 8px 12px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.json-viewer-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.json-viewer-toggle:hover {
  background-color: #dee2e6;
}

.json-viewer-arrow {
  transition: transform 0.2s ease;
  font-size: 10px;
  color: #666;
}

.json-viewer-arrow.collapsed {
  transform: rotate(0deg);
}

.json-viewer-arrow.expanded {
  transform: rotate(90deg);
}

.json-viewer-content {
  padding: 12px;
  max-height: 500px;
  overflow-y: auto;
}

/* JSON节点样式 */
.json-node {
  margin: 2px 0;
}

.json-node-header {
  display: flex;
  align-items: center;
  margin: 2px 0;
}

.json-node-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  margin-right: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  min-width: 16px;
  height: 16px;
}

.json-node-toggle:hover {
  background-color: #e9ecef;
}

.json-arrow {
  font-size: 8px;
  color: #666;
  transition: transform 0.2s ease;
}

.json-arrow.collapsed {
  transform: rotate(0deg);
}

.json-arrow.expanded {
  transform: rotate(90deg);
}

/* 数据类型样式 */
.json-key {
  color: #0066cc;
  font-weight: 500;
}

.json-string {
  color: #008000;
}

.json-number {
  color: #1976d2;
  font-weight: 500;
}

.json-boolean {
  color: #9c27b0;
  font-weight: 500;
}

.json-null {
  color: #757575;
  font-style: italic;
}

.json-bracket {
  color: #333;
  font-weight: bold;
}

.json-colon {
  color: #333;
  margin: 0 2px;
}

.json-comma {
  color: #333;
}

.json-array-length,
.json-object-length {
  color: #666;
  font-size: 10px;
  margin-left: 4px;
}

.json-array-index {
  color: #ff5722;
  font-weight: 500;
  margin-right: 4px;
}

.json-array-empty,
.json-object-empty {
  color: #666;
  font-style: italic;
}

/* 内容区域样式 */
.json-array-content,
.json-object-content {
  margin-left: 0;
}

.json-array-item,
.json-object-item {
  margin: 1px 0;
  position: relative;
}

.json-object-item:hover,
.json-array-item:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.json-unknown {
  color: #e91e63;
  font-style: italic;
}

/* 滚动条样式 */
.json-viewer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.json-viewer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.json-viewer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.json-viewer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.json-viewer-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.json-viewer-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.json-viewer-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.json-viewer-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .json-viewer {
    font-size: 11px;
  }
  
  .json-viewer-toggle {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .json-viewer-content {
    padding: 8px;
    max-height: 400px;
  }
  
  .json-node-toggle {
    min-width: 14px;
    height: 14px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .json-viewer {
    border-color: #000;
  }
  
  .json-key {
    color: #0000ff;
  }
  
  .json-string {
    color: #006600;
  }
  
  .json-number {
    color: #000080;
  }
  
  .json-boolean {
    color: #800080;
  }
  
  .json-null {
    color: #404040;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .json-viewer {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .json-viewer-header {
    background-color: #4a5568;
    border-bottom-color: #718096;
  }
  
  .json-viewer-toggle {
    color: #e2e8f0;
  }
  
  .json-viewer-toggle:hover {
    background-color: #718096;
  }
  
  .json-key {
    color: #63b3ed;
  }
  
  .json-string {
    color: #68d391;
  }
  
  .json-number {
    color: #90cdf4;
  }
  
  .json-boolean {
    color: #d6bcfa;
  }
  
  .json-null {
    color: #a0aec0;
  }
  
  .json-bracket,
  .json-colon,
  .json-comma {
    color: #e2e8f0;
  }
  
  .json-array-length,
  .json-object-length {
    color: #a0aec0;
  }
  
  .json-array-index {
    color: #fc8181;
  }
  
  .json-object-item:hover,
  .json-array-item:hover {
    background-color: rgba(99, 179, 237, 0.1);
  }
} 