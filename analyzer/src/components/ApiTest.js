import React, { useState } from 'react';

const ApiTest = () => {
  const [testResult, setTestResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testDirectAccess = async () => {
    setLoading(true);
    setTestResult('测试中...');

    try {
      // 直接测试CORS服务器
      const response = await fetch('http://localhost:10000/dataset/');
      const text = await response.text();

      setTestResult(`
CORS服务器测试成功！
状态码: ${response.status}
响应类型: ${response.headers.get('content-type')}
响应长度: ${text.length}
响应内容前100字符: ${text.substring(0, 100)}...
      `);
    } catch (error) {
      setTestResult(`
CORS服务器测试失败！
错误类型: ${error.name}
错误信息: ${error.message}
      `);
    } finally {
      setLoading(false);
    }
  };

  const testAxios = async () => {
    setLoading(true);
    setTestResult('测试中...');
    
    try {
      const { getDatasetList } = await import('../services/api');
      const datasets = await getDatasetList();
      
      setTestResult(`
Axios测试成功！
数据集数量: ${datasets.length}
数据集列表: ${datasets.join(', ')}
      `);
    } catch (error) {
      setTestResult(`
Axios测试失败！
错误类型: ${error.name}
错误信息: ${error.message}
错误详情: ${JSON.stringify(error, null, 2)}
      `);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', background: 'white', margin: '20px', borderRadius: '8px' }}>
      <h3>API连接测试</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={testDirectAccess}
          disabled={loading}
          style={{ marginRight: '10px', padding: '8px 16px' }}
        >
          测试CORS服务器
        </button>
        
        <button 
          onClick={testAxios} 
          disabled={loading}
          style={{ padding: '8px 16px' }}
        >
          测试Axios请求
        </button>
      </div>
      
      <div style={{ 
        background: '#f8f9fa', 
        padding: '16px', 
        borderRadius: '4px',
        fontFamily: 'monospace',
        whiteSpace: 'pre-wrap',
        minHeight: '100px'
      }}>
        {testResult || '点击按钮开始测试...'}
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p><strong>测试说明：</strong></p>
        <ul>
          <li>代理访问：测试React代理到HTTP服务器的连接</li>
          <li>Axios请求：使用应用的API服务测试</li>
          <li>确保SSH端口转发正确：<code>ssh -L "3001:localhost:3001" -L "10000:localhost:10000"</code></li>
          <li>确保HTTP服务器运行：<code>python -m http.server 10000</code></li>
        </ul>
      </div>
    </div>
  );
};

export default ApiTest;
