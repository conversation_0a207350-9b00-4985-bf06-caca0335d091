.dataset-selector {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dataset-selector-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.dataset-selector-label {
  font-weight: 600;
  color: #333;
  min-width: 60px;
}

.dataset-selector-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.dataset-selector-select {
  flex: 1;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.dataset-selector-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.dataset-selector-select:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.dataset-selector-refresh-btn {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: all 0.2s ease;
  min-width: 40px;
}

.dataset-selector-refresh-btn:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.dataset-selector-refresh-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.dataset-selector-loading {
  color: #666;
  font-style: italic;
}

.dataset-selector-error {
  color: #dc3545;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dataset-selector-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
  font-size: 14px;
}

.dataset-selector-current {
  color: #333;
}

.dataset-selector-current strong {
  color: #007bff;
}

.dataset-selector-count {
  color: #666;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-selector-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .dataset-selector-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .dataset-selector-select {
    max-width: none;
  }
  
  .dataset-selector-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
