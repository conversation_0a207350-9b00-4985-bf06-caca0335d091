import React, { useMemo } from 'react';

/**
 * 指标信息面板组件
 * 展示MonkeyOCR和KDC的准确率比较分析
 */
const MetricsPanel = ({ caseData, className = '' }) => {
  const metricsData = useMemo(() => {
    if (!caseData || !caseData.features?.kdc?.accuracy_metrics) {
      return null;
    }

    const accuracy = caseData.features.kdc.accuracy_metrics;
    const textboxDistribution = caseData.features.kdc.textbox_count_distribution;
    
    return {
      accuracy,
      textboxDistribution,
      hasData: accuracy.has_comparison_data
    };
  }, [caseData]);

  if (!metricsData || !metricsData.hasData) {
    return (
      <div className={`metrics-panel ${className}`}>
        <div className="metrics-header">
          <h4>📊 指标信息</h4>
        </div>
        <div className="metrics-placeholder">
          暂无准确率比较数据
        </div>
      </div>
    );
  }

  const { accuracy, textboxDistribution } = metricsData;

  // 计算准确率百分比
  const kdcVsMonkeyOCR = (accuracy.kdc_vs_monkey_ocr * 100).toFixed(1);
  const kdcVsMonkeyOCRLatex = (accuracy.kdc_vs_monkey_ocr_latex * 100).toFixed(1);
  const monkeyOCRComparison = (accuracy.monkey_ocr_vs_monkey_ocr_latex * 100).toFixed(1);

  // 获取文本框分布信息
  const maxTextboxes = textboxDistribution?.max_textboxes_per_cell || 0;
  const avgTextboxes = textboxDistribution?.avg_textboxes_per_cell || 0;
  const totalCells = textboxDistribution?.total_cells || 0;
  const hasOutliers = textboxDistribution?.outlier_cells?.length > 0;

  return (
    <div className={`metrics-panel ${className}`}>
      <div className="metrics-header">
        <h4>📊 指标信息</h4>
      </div>

      <div className="metrics-content">
        {/* 准确率比较 */}
        <div className="metrics-section">
          <h5>准确率比较</h5>
          <div className="accuracy-comparison">
            <div className="comparison-item">
              <div className="comparison-label">KDC vs MonkeyOCR (table)</div>
              <div className="comparison-value">
                <div className="accuracy-bar">
                  <div 
                    className="accuracy-fill"
                    style={{ width: `${kdcVsMonkeyOCR}%` }}
                  ></div>
                </div>
                <span className="accuracy-text">{kdcVsMonkeyOCR}%</span>
              </div>
            </div>

            <div className="comparison-item">
              <div className="comparison-label">KDC vs MonkeyOCR (parse)</div>
              <div className="comparison-value">
                <div className="accuracy-bar">
                  <div 
                    className="accuracy-fill"
                    style={{ width: `${kdcVsMonkeyOCRLatex}%` }}
                  ></div>
                </div>
                <span className="accuracy-text">{kdcVsMonkeyOCRLatex}%</span>
              </div>
            </div>

            <div className="comparison-item">
              <div className="comparison-label">MonkeyOCR (table) vs (parse)</div>
              <div className="comparison-value">
                <div className="accuracy-bar">
                  <div 
                    className="accuracy-fill"
                    style={{ width: `${monkeyOCRComparison}%` }}
                  ></div>
                </div>
                <span className="accuracy-text">{monkeyOCRComparison}%</span>
              </div>
            </div>
          </div>
        </div>

        {/* 表格复杂度指标 */}
        <div className="metrics-section">
          <h5>表格复杂度</h5>
          <div className="complexity-metrics">
            <div className="metric-item">
              <div className="metric-label">总单元格数</div>
              <div className="metric-value">{totalCells}</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">平均文本框数/单元格</div>
              <div className="metric-value">{avgTextboxes}</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">最大文本框数/单元格</div>
              <div className="metric-value">{maxTextboxes}</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">复杂度等级</div>
              <div className={`metric-value complexity-level ${getComplexityLevel(maxTextboxes, avgTextboxes)}`}>
                {getComplexityLabel(maxTextboxes, avgTextboxes)}
              </div>
            </div>
          </div>
        </div>

        {/* 异常检测 */}
        {hasOutliers && (
          <div className="metrics-section">
            <h5>🚨 异常检测</h5>
            <div className="outlier-info">
              <p>检测到包含异常多文本框的单元格，可能影响解析准确率：</p>
              <ul>
                {textboxDistribution.outlier_cells.map((outlier, index) => (
                  <li key={index}>
                    {outlier.textbox_count} 个文本框 (超过阈值 {outlier.threshold.toFixed(1)})
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* 相关性分析 */}
        <div className="metrics-section">
          <h5>相关性分析</h5>
          <div className="correlation-analysis">
            <div className="analysis-item">
              <div className="analysis-label">复杂度对准确率的影响</div>
              <div className="analysis-value">
                {getComplexityImpact(maxTextboxes, avgTextboxes, accuracy)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .metrics-panel {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          height: fit-content;
        }

        .metrics-header h4 {
          margin: 0 0 16px 0;
          color: #333;
          font-size: 16px;
        }

        .metrics-placeholder {
          text-align: center;
          color: #999;
          padding: 40px;
          font-style: italic;
        }

        .metrics-content {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .metrics-section h5 {
          margin: 0 0 12px 0;
          color: #555;
          font-size: 14px;
          font-weight: 600;
        }

        .accuracy-comparison {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .comparison-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .comparison-label {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }

        .comparison-value {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .accuracy-bar {
          flex: 1;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
        }

        .accuracy-fill {
          height: 100%;
          background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
          transition: width 0.3s ease;
        }

        .accuracy-text {
          font-size: 12px;
          font-weight: 600;
          color: #333;
          min-width: 40px;
          text-align: right;
        }

        .complexity-metrics {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
        }

        .metric-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .metric-label {
          font-size: 11px;
          color: #666;
        }

        .metric-value {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .complexity-level.low {
          color: #28a745;
        }

        .complexity-level.medium {
          color: #ffc107;
        }

        .complexity-level.high {
          color: #dc3545;
        }

        .outlier-info {
          font-size: 12px;
          color: #856404;
          background: #fff3cd;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid #ffeaa7;
        }

        .outlier-info ul {
          margin: 8px 0 0 0;
          padding-left: 20px;
        }

        .outlier-info li {
          margin: 4px 0;
        }

        .correlation-analysis {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .analysis-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .analysis-label {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }

        .analysis-value {
          font-size: 12px;
          color: #333;
          background: #f8f9fa;
          padding: 8px;
          border-radius: 4px;
          border-left: 3px solid #007bff;
        }
      `}</style>
    </div>
  );
};

// 辅助函数：获取复杂度等级
function getComplexityLevel(maxTextboxes, avgTextboxes) {
  if (maxTextboxes <= 1 && avgTextboxes <= 1) return 'low';
  if (maxTextboxes <= 3 && avgTextboxes <= 2) return 'medium';
  return 'high';
}

// 辅助函数：获取复杂度标签
function getComplexityLabel(maxTextboxes, avgTextboxes) {
  const level = getComplexityLevel(maxTextboxes, avgTextboxes);
  switch (level) {
    case 'low': return '简单';
    case 'medium': return '中等';
    case 'high': return '复杂';
    default: return '未知';
  }
}

// 辅助函数：分析复杂度对准确率的影响
function getComplexityImpact(maxTextboxes, avgTextboxes, accuracy) {
  const complexityLevel = getComplexityLevel(maxTextboxes, avgTextboxes);
  const avgAccuracy = (accuracy.kdc_vs_monkey_ocr + accuracy.kdc_vs_monkey_ocr_latex) / 2;
  
  if (complexityLevel === 'high') {
    if (avgAccuracy < 0.7) {
      return '高复杂度表格显著影响解析准确率，建议优化解析策略';
    } else {
      return '尽管表格复杂度较高，但解析器表现良好';
    }
  } else if (complexityLevel === 'medium') {
    if (avgAccuracy < 0.8) {
      return '中等复杂度表格对准确率有一定影响';
    } else {
      return '中等复杂度表格，解析器处理效果良好';
    }
  } else {
    return '简单表格，解析器通常能获得较好的准确率';
  }
}

export default MetricsPanel;
