.accuracy-report {
  padding: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.accuracy-status {
  margin-bottom: 8px;
}

.accuracy-value {
  margin-bottom: 8px;
}

.accuracy-timeout {
  margin-bottom: 8px;
}

.missed-cells {
  margin-top: 12px;
}

.missed-detail-table {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.missed-detail-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.missed-detail-table th,
.missed-detail-table td {
  padding: 4px 8px;
  border: 1px solid #ddd;
  text-align: left;
  vertical-align: top;
}

.missed-detail-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 1;
}

.missed-detail-table td {
  word-break: break-word;
  max-width: 150px;
}

.missed-detail-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.missed-detail-table tbody tr:hover {
  background-color: #e9ecef;
}
