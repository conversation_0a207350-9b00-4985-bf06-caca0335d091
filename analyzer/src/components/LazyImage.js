import React, { useState, useRef, useEffect } from 'react';
import { useIntersectionObserver } from '../hooks/useVirtualization';
import './LazyImage.css';

const LazyImage = ({
  src,
  alt = '',
  placeholder = null,
  className = '',
  style = {},
  onClick,
  onLoad,
  onError,
  threshold = 0.1,
  rootMargin = '50px'
}) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [inView, setInView] = useState(false);
  const imgRef = useRef(null);

  // 使用交叉观察器检测图片是否进入视口
  const { observe, unobserve } = useIntersectionObserver({
    threshold,
    rootMargin
  });

  useEffect(() => {
    const imgElement = imgRef.current;
    if (imgElement) {
      observe(imgElement);
      
      return () => {
        unobserve(imgElement);
      };
    }
  }, [observe, unobserve]);

  // 监听交叉观察器的变化
  useEffect(() => {
    const imgElement = imgRef.current;
    if (imgElement) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.target === imgElement && entry.isIntersecting) {
              setInView(true);
              observer.unobserve(imgElement);
            }
          });
        },
        { threshold, rootMargin }
      );

      observer.observe(imgElement);

      return () => {
        observer.disconnect();
      };
    }
  }, [threshold, rootMargin]);

  const handleLoad = (event) => {
    setLoaded(true);
    if (onLoad) {
      onLoad(event);
    }
  };

  const handleError = (event) => {
    setError(true);
    if (onError) {
      onError(event);
    }
  };

  const handleClick = (event) => {
    if (onClick && loaded && !error) {
      onClick(event);
    }
  };

  // 默认占位符
  const defaultPlaceholder = (
    <div className="lazy-image-placeholder">
      <div className="lazy-image-placeholder-icon">🖼️</div>
      <div className="lazy-image-placeholder-text">加载中...</div>
    </div>
  );

  // 错误占位符
  const errorPlaceholder = (
    <div className="lazy-image-error">
      <div className="lazy-image-error-icon">❌</div>
      <div className="lazy-image-error-text">加载失败</div>
    </div>
  );

  return (
    <div
      ref={imgRef}
      className={`lazy-image ${className} ${loaded ? 'lazy-image-loaded' : ''} ${error ? 'lazy-image-error' : ''}`}
      style={style}
      onClick={handleClick}
    >
      {/* 只有在视口内才加载图片 */}
      {inView && (
        <img
          src={src}
          alt={alt}
          className="lazy-image-img"
          onLoad={handleLoad}
          onError={handleError}
          style={{
            opacity: loaded ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}
        />
      )}

      {/* 显示占位符或错误状态 */}
      {!loaded && !error && (
        <div className="lazy-image-placeholder-container">
          {placeholder || defaultPlaceholder}
        </div>
      )}

      {error && (
        <div className="lazy-image-error-container">
          {errorPlaceholder}
        </div>
      )}

      {/* 加载指示器 */}
      {inView && !loaded && !error && (
        <div className="lazy-image-loading">
          <div className="lazy-image-loading-spinner"></div>
        </div>
      )}
    </div>
  );
};

export default LazyImage;
