.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.image-modal-content {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-modal-close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.image-modal-close:hover {
  background: rgba(0, 0, 0, 0.9);
}

.image-modal-title {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #333;
  font-size: 16px;
  word-break: break-all;
}

.image-modal-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 200px;
  background-color: #f8f9fa;
}

.image-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-modal-error {
  color: #dc3545;
  font-size: 16px;
  text-align: center;
  padding: 40px;
}

.image-modal-controls {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-btn {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
}

.zoom-level {
  min-width: 50px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.action-controls {
  display: flex;
  gap: 12px;
}

.image-modal-btn {
  padding: 8px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.image-modal-btn:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.image-modal-btn:first-child {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.image-modal-btn:first-child:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-modal-overlay {
    padding: 10px;
  }
  
  .image-modal-content {
    max-width: 100vw;
    max-height: 100vh;
  }
  
  .image-modal-title {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .image-modal-image-container {
    padding: 15px;
  }
  
  .image-modal-controls {
    padding: 12px 16px;
    flex-direction: column;
  }
  
  .image-modal-btn {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .image-modal-btn:last-child {
    margin-bottom: 0;
  }
}

/* 动画效果 */
.image-modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.image-modal-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
