.lazy-image {
  position: relative;
  display: block;
  overflow: hidden;
  background-color: #f8f9fa;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 100%;
  height: auto;
  min-height: 100px;
}

.lazy-image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.lazy-image-placeholder-container,
.lazy-image-error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.lazy-image-placeholder {
  text-align: center;
  color: #666;
  padding: 20px;
}

.lazy-image-placeholder-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.lazy-image-placeholder-text {
  font-size: 12px;
  font-style: italic;
}

.lazy-image-error {
  text-align: center;
  color: #dc3545;
  padding: 20px;
}

.lazy-image-error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.lazy-image-error-text {
  font-size: 12px;
  font-style: italic;
}

.lazy-image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.lazy-image-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载完成状态 */
.lazy-image-loaded {
  cursor: pointer;
}

.lazy-image-loaded:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 错误状态 */
.lazy-image-error {
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lazy-image-placeholder-icon,
  .lazy-image-error-icon {
    font-size: 20px;
    margin-bottom: 6px;
  }
  
  .lazy-image-placeholder-text,
  .lazy-image-error-text {
    font-size: 11px;
  }
  
  .lazy-image-loading-spinner {
    width: 20px;
    height: 20px;
  }
  
  .lazy-image-placeholder,
  .lazy-image-error {
    padding: 15px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .lazy-image {
    border: 1px solid #000;
  }
  
  .lazy-image-placeholder-container,
  .lazy-image-error-container {
    background-color: #fff;
  }
  
  .lazy-image-placeholder {
    color: #000;
  }
  
  .lazy-image-error {
    color: #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .lazy-image {
    transition: none;
  }
  
  .lazy-image-img {
    transition: none;
  }
  
  .lazy-image-loaded:hover {
    transform: none;
  }
  
  .lazy-image-loading-spinner {
    animation: none;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .lazy-image {
    background-color: #343a40;
  }
  
  .lazy-image-placeholder-container,
  .lazy-image-error-container {
    background-color: #343a40;
  }
  
  .lazy-image-placeholder {
    color: #adb5bd;
  }
  
  .lazy-image-error {
    color: #f8d7da;
  }
}
