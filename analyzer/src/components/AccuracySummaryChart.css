.accuracy-summary-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.accuracy-summary-chart .chart-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.accuracy-summary-chart .chart-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.accuracy-summary-chart .chart-subtitle {
  margin: 4px 0 0;
  font-size: 11px;
  opacity: 0.9;
  font-weight: 400;
}

.accuracy-summary-chart .chart-container {
  padding: 16px;
  background: #fafafa;
}

.accuracy-summary-chart .chart-empty {
  padding: 30px 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.baseline {
  background-color: #6366f1;
}

.legend-color.success {
  background-color: #10b981;
}

.legend-color.failed {
  background-color: #ef4444;
}

/* 自定义Recharts样式 */
.accuracy-summary-chart .recharts-cartesian-grid-horizontal line,
.accuracy-summary-chart .recharts-cartesian-grid-vertical line {
  stroke: #e8e8e8;
  stroke-width: 1;
}

.accuracy-summary-chart .recharts-tooltip-wrapper {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.accuracy-summary-chart .recharts-default-tooltip {
  background: white !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.accuracy-summary-chart .recharts-tooltip-label {
  color: #333 !important;
  font-weight: 600 !important;
  margin-bottom: 4px !important;
}

.accuracy-summary-chart .recharts-tooltip-item {
  color: #666 !important;
}

/* 柱状图动态颜色 */
.accuracy-summary-chart .recharts-bar-rectangle {
  transition: opacity 0.2s ease;
}

.accuracy-summary-chart .recharts-bar-rectangle:hover {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .accuracy-summary-chart .chart-header {
    padding: 10px 12px 6px;
  }
  
  .accuracy-summary-chart .chart-header h4 {
    font-size: 14px;
  }
  
  .accuracy-summary-chart .chart-subtitle {
    font-size: 10px;
  }
  
  .accuracy-summary-chart .chart-container {
    padding: 12px;
  }
  
  .chart-legend {
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .legend-item {
    font-size: 11px;
  }
}

/* 动画效果 */
.accuracy-summary-chart {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
