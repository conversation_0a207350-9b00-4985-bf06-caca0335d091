import React, { useState, useEffect } from 'react';
import { getAnnotations, getImageAnnotations, findImageId, createAnnotation, updateAnnotation, deleteAnnotation } from '../../services/api';
import AnnotationEditor from './AnnotationEditor';
import AnnotationList from './AnnotationList';
import AnnotationManager from '../AnnotationManager';
import './AnnotationPanel.css';

const AnnotationPanel = ({ selectedCase, selectedDataset, onAnnotationChange }) => {
  const [annotations, setAnnotations] = useState([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('case'); // case | dataset

  // 加载图片的标注数据
  useEffect(() => {
    if (selectedCase && selectedDataset) {
      loadAnnotations();
    }
  }, [selectedCase, selectedDataset]);

  const loadAnnotations = async () => {
    if (!selectedCase || !selectedDataset) {
      console.log('loadAnnotations: 缺少必要参数', { selectedCase: !!selectedCase, selectedDataset });
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('loadAnnotations: 开始加载标注', {
        selectedDataset,
        fileName: selectedCase.fileName,
        imageName: selectedCase.imageName,
        caseId: selectedCase.id
      });

      // 方法1: 通过图片ID直接获取标注（更可靠）
      try {
        const imageId = await findImageId(selectedDataset, selectedCase.fileName);
        console.log('loadAnnotations: 找到图片ID', imageId);

        if (imageId) {
          const imageAnnotations = await getImageAnnotations(imageId);
          console.log('loadAnnotations: 通过图片ID获取的标注', imageAnnotations);
          setAnnotations(imageAnnotations);
          return;
        }
      } catch (err) {
        console.warn('loadAnnotations: 通过图片ID获取标注失败，尝试备用方法', err);
      }

      // 方法2: 备用方法 - 通过数据集筛选
      console.log('loadAnnotations: 使用备用方法获取标注');
      const allAnnotations = await getAnnotations({
        dataset_name: selectedDataset,
      });

      console.log('loadAnnotations: 获取到的所有标注', allAnnotations);

      // 筛选出当前图片的标注
      const imageAnnotations = allAnnotations.filter(annotation => {
        // 尝试多种匹配方式
        const exactMatch = annotation.image_filename === selectedCase.fileName;
        const baseNameMatch = annotation.image_filename === selectedCase.imageName;
        const withoutExtMatch = annotation.image_filename.replace(/\.(png|jpg|jpeg|gif|webp)$/i, '') ===
                               selectedCase.fileName.replace(/\.(png|jpg|jpeg|gif|webp|pdf)$/i, '');

        const match = exactMatch || baseNameMatch || withoutExtMatch;

        console.log('loadAnnotations: 文件名匹配检查', {
          annotationFile: annotation.image_filename,
          selectedFile: selectedCase.fileName,
          selectedImageName: selectedCase.imageName,
          exactMatch,
          baseNameMatch,
          withoutExtMatch,
          finalMatch: match
        });

        return match;
      });

      console.log('loadAnnotations: 筛选后的标注', imageAnnotations);
      setAnnotations(imageAnnotations);
    } catch (err) {
      console.error('加载标注数据失败:', err);
      setError('加载标注数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAnnotation = async (annotationData) => {
    try {
      const newAnnotation = await createAnnotation({
        dataset_name: selectedDataset,           // 使用传递的数据集名称
        image_filename: selectedCase.fileName,   // 使用图片文件名
        annotator: annotationData.annotator || 'anonymous',
        table_structure: JSON.stringify(annotationData.structure),
        table_content: annotationData.content,
        annotation_type: 'manual',
        status: 'draft'
      });
      
      setAnnotations([...annotations, newAnnotation]);
      setIsEditing(false);
      setSelectedAnnotation(null);
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      console.error('创建标注失败:', err);
      setError('创建标注失败');
    }
  };

  const handleUpdateAnnotation = async (annotationId, updateData) => {
    try {
      const updatedAnnotation = await updateAnnotation(annotationId, {
        table_structure: JSON.stringify(updateData.structure),
        table_content: updateData.content,
        status: updateData.status
      });
      
      setAnnotations(annotations.map(ann => 
        ann.id === annotationId ? updatedAnnotation : ann
      ));
      setIsEditing(false);
      setSelectedAnnotation(null);
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      console.error('更新标注失败:', err);
      setError('更新标注失败');
    }
  };

  const handleDeleteAnnotation = async (annotationId) => {
    if (!window.confirm('确定要删除这个标注吗？')) {
      return;
    }
    
    try {
      await deleteAnnotation(annotationId);
      setAnnotations(annotations.filter(ann => ann.id !== annotationId));
      
      if (selectedAnnotation && selectedAnnotation.id === annotationId) {
        setSelectedAnnotation(null);
        setIsEditing(false);
      }
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      console.error('删除标注失败:', err);
      setError('删除标注失败');
    }
  };

  const handleEditAnnotation = (annotation) => {
    setSelectedAnnotation(annotation);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setSelectedAnnotation(null);
    setIsEditing(false);
  };

  const handleStartNewAnnotation = () => {
    setSelectedAnnotation(null);
    setIsEditing(true);
  };

  return (
    <div className="annotation-panel">
      <div className="annotation-panel-header">
        <h3>人工标注</h3>
        <div className="annotation-tab-navigation">
          <button
            className={`tab-button ${activeTab === 'case' ? 'active' : ''}`}
            onClick={() => setActiveTab('case')}
          >
            案例标注
          </button>
          <button
            className={`tab-button ${activeTab === 'dataset' ? 'active' : ''}`}
            onClick={() => setActiveTab('dataset')}
          >
            数据集管理
          </button>
        </div>
      </div>

      {error && (
        <div className="annotation-error">
          <p>{error}</p>
          <button onClick={() => setError(null)}>关闭</button>
        </div>
      )}

      <div className="annotation-panel-content">
        {activeTab === 'dataset' ? (
          <AnnotationManager
            selectedDataset={selectedDataset}
            onAnnotationChange={onAnnotationChange}
          />
        ) : (
          <>
            {!selectedCase ? (
              <div className="annotation-panel-empty">
                <p>请选择一个测试案例开始标注</p>
              </div>
            ) : isEditing ? (
              <AnnotationEditor
                annotation={selectedAnnotation}
                selectedCase={selectedCase}
                onSave={selectedAnnotation ?
                  (data) => handleUpdateAnnotation(selectedAnnotation.id, data) :
                  handleCreateAnnotation
                }
                onCancel={handleCancelEdit}
              />
            ) : (
              <>
                <div className="annotation-panel-info">
                  <span>图片: {selectedCase.fileName}</span>
                  <span>标注数量: {annotations.length}</span>
                </div>

                <div className="annotation-panel-actions">
                  <button
                    className="btn btn-primary"
                    onClick={handleStartNewAnnotation}
                  >
                    新建标注
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={loadAnnotations}
                    disabled={loading}
                  >
                    {loading ? '加载中...' : '刷新'}
                  </button>
                </div>

                <AnnotationList
                  annotations={annotations}
                  onEdit={handleEditAnnotation}
                  onDelete={handleDeleteAnnotation}
                  loading={loading}
                />
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AnnotationPanel;
