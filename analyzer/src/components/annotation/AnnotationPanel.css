/* 标注面板样式 */
.annotation-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.annotation-panel-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.annotation-panel-header h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.annotation-tab-navigation {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.annotation-tab-navigation .tab-button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: #fff;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.annotation-tab-navigation .tab-button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.annotation-tab-navigation .tab-button.active {
  background: #007bff;
  color: #fff;
  border-color: #007bff;
}

.annotation-tab-navigation .tab-button.active:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.annotation-panel-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6c757d;
}

.annotation-panel-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.annotation-error {
  margin: 16px;
  padding: 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.annotation-error button {
  background: none;
  border: none;
  color: #721c24;
  cursor: pointer;
  font-weight: bold;
}

.annotation-panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.annotation-panel-actions {
  padding: 16px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #e1e5e9;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}
