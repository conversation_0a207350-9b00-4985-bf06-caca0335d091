/* 标注列表样式 */
.annotation-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.annotation-list-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.annotation-list-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.annotation-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.annotation-list-loading,
.annotation-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.annotation-list-empty p {
  margin: 4px 0;
}

/* 标注项样式 */
.annotation-item {
  margin-bottom: 12px;
  padding: 16px;
  background: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  transition: box-shadow 0.2s;
}

.annotation-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.annotation-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.annotation-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.annotation-id {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
}

.annotation-annotator {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.annotation-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-draft {
  background: #fff3cd;
  color: #856404;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-reviewed {
  background: #d1ecf1;
  color: #0c5460;
}

.annotation-item-actions {
  display: flex;
  gap: 8px;
}

.annotation-item-content {
  margin-bottom: 12px;
}

.annotation-structure {
  margin-bottom: 8px;
}

.structure-info {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.annotation-content-preview {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 8px;
  max-height: 100px;
  overflow: hidden;
}

.annotation-content-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.annotation-item-footer {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6c757d;
  border-top: 1px solid #f1f3f4;
  padding-top: 8px;
}

.annotation-date {
  display: flex;
  align-items: center;
}
