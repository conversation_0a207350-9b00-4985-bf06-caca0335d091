/* 标注编辑器样式 */
.annotation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.annotation-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.annotation-editor-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.annotation-editor-actions {
  display: flex;
  gap: 8px;
}

.annotation-editor-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
}

.form-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: end;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 内容模式切换 */
.content-mode-toggle {
  display: flex;
  gap: 4px;
}

.content-mode-toggle .btn.active {
  background: #007bff;
  color: white;
}

/* 内容编辑器 */
.content-editor {
  width: 100%;
  min-height: 300px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s;
}

.content-editor:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 内容预览 */
.content-preview {
  min-height: 300px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #f8f9fa;
  overflow: auto;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.preview-table th,
.preview-table td {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  text-align: left;
}

.preview-table th {
  background: #e9ecef;
  font-weight: 600;
}

.preview-table .header-row {
  background: #e9ecef;
}

.content-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}
