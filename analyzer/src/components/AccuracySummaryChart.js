import React, { useMemo } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell, LabelList } from 'recharts';
import { calculateAccuracyForCase } from '../utils/dataProcessor';
import './AccuracySummaryChart.css';

/**
 * 准确率汇总直方图组件
 * 显示当前案例的各解析器准确率对比
 */
const AccuracySummaryChart = ({ caseData, index, fileName, annotationData, useAnnotationAsBaseline }) => {
  const chartData = useMemo(() => {
    if (!caseData) return [];

    // 使用统一的准确率计算函数
    const accuracyResult = calculateAccuracyForCase(caseData, annotationData, useAnnotationAsBaseline);
    const { accuracies } = accuracyResult;

    if (!accuracies) return [];

    // 转换为图表数据格式
    const accuracyData = [];

    // KDC Markdown
    if (caseData.kdcMarkdown?.result?.data?.[0]?.markdown) {
      accuracyData.push({
        name: 'KDC Markdown',
        accuracy: accuracies.kdcMarkdown,
        status: 'success'
      });
    }

    // KDC Plain
    if (caseData.kdcPlain?.result?.data?.[0]?.plain) {
      accuracyData.push({
        name: 'KDC Plain',
        accuracy: accuracies.kdcPlain,
        status: useAnnotationAsBaseline ? 'success' : 'baseline'
      });
    }

    // KDC KDC
    if (caseData.kdcKdc?.result?.data?.[0]?.kdc) {
      accuracyData.push({
        name: 'KDC KDC',
        accuracy: accuracies.kdcKdc,
        status: 'success'
      });
    }

    // MonkeyOCR (table)
    if (caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html) {
      const isSuccess = caseData.monkeyOCR?.result?.success !== false &&
                       !caseData.monkeyOCR?.result?.is_timeout &&
                       accuracies.monkeyOCR > 0;

      accuracyData.push({
        name: 'MonkeyOCR(table)',
        accuracy: accuracies.monkeyOCR,
        status: isSuccess ? 'success' : 'failed'
      });
    }

    // MonkeyOCR (parse)
    if (caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html) {
      const isSuccess = caseData.monkeyOCRV2?.result?.success !== false &&
                       !caseData.monkeyOCRV2?.result?.is_timeout &&
                       accuracies.monkeyOCRV2 > 0;

      accuracyData.push({
        name: 'MonkeyOCR(parse)',
        accuracy: accuracies.monkeyOCRV2,
        status: isSuccess ? 'success' : 'failed'
      });
    }

    // VL LLM
    if (caseData.vlLLMResult?.result) {
      const hasValidContent = accuracies.vlLLM > 0;

      accuracyData.push({
        name: 'VL-LLM',
        accuracy: accuracies.vlLLM,
        status: hasValidContent ? 'success' : 'failed'
      });
    }

    // MonkeyOCR Local
    if (caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result) {
      const isSuccess = accuracies.monkeyOCRLocal > 0;

      accuracyData.push({
        name: 'MonkeyOCR(local)',
        accuracy: accuracies.monkeyOCRLocal,
        status: isSuccess ? 'success' : 'failed'
      });
    }

    return accuracyData;
  }, [caseData, annotationData, useAnnotationAsBaseline]);

  if (!chartData.length) {
    return (
      <div className="accuracy-summary-chart">
        <div className="chart-header">
          <h4>📈 准确率汇总</h4>
          <div className="chart-subtitle">案例 #{index} - {fileName}</div>
        </div>
        <div className="chart-empty">暂无准确率数据</div>
      </div>
    );
  }

  // 自定义柱状图颜色
  const getBarColor = (status) => {
    switch (status) {
      case 'baseline': return '#6366f1'; // 基准 - 蓝色
      case 'success': return '#10b981';  // 成功 - 绿色
      case 'failed': return '#ef4444';   // 失败 - 红色
      default: return '#9ca3af';         // 默认 - 灰色
    }
  };

  return (
    <div className="accuracy-summary-chart">
      <div className="chart-header">
        <h4>📈 准确率汇总</h4>
        <div className="chart-subtitle">案例 #{index} - {fileName}</div>
      </div>
      <div className="chart-container">
        <ResponsiveContainer width="100%" height={250}>
          <BarChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 40,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              angle={-45}
              textAnchor="end"
              height={60}
              fontSize={11}
            />
            <YAxis 
              domain={[0, 100]}
              fontSize={11}
              label={{ value: '准确率 (%)', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip 
              formatter={(value) => [`${value.toFixed(1)}%`, '准确率']}
              labelFormatter={(label) => `解析器: ${label}`}
            />
            <Bar
              dataKey="accuracy"
              radius={[3, 3, 0, 0]}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(entry.status)} />
              ))}
              <LabelList
                dataKey="accuracy"
                position="top"
                fontSize={10}
                formatter={(value) => `${value.toFixed(1)}%`}
              />
            </Bar>
          </BarChart>
        </ResponsiveContainer>
        
        {/* 图例 */}
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color baseline"></div>
            <span>基准</span>
          </div>
          <div className="legend-item">
            <div className="legend-color success"></div>
            <span>成功解析</span>
          </div>
          <div className="legend-item">
            <div className="legend-color failed"></div>
            <span>解析失败</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccuracySummaryChart;
