.virtual-list {
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.virtual-list-content {
  width: 100%;
}

.virtual-list-item {
  box-sizing: border-box;
  border-bottom: 1px solid #e9ecef;
}

.virtual-list-item:last-child {
  border-bottom: none;
}

/* 空状态样式 */
.virtual-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.virtual-list-empty-content {
  text-align: center;
  color: #666;
}

.virtual-list-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.virtual-list-empty-text {
  font-size: 16px;
  font-style: italic;
}

/* 加载状态样式 */
.virtual-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
  position: sticky;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

.virtual-list-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 没有更多数据样式 */
.virtual-list-no-more {
  text-align: center;
  padding: 16px;
  color: #999;
  font-size: 14px;
  font-style: italic;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* 滚动条样式 */
.virtual-list::-webkit-scrollbar {
  width: 8px;
}

.virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 性能优化相关样式 */
.virtual-list-item {
  will-change: transform;
  contain: layout style paint;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtual-list-empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .virtual-list-empty-text {
    font-size: 14px;
  }
  
  .virtual-list-loading {
    padding: 16px;
    font-size: 13px;
  }
  
  .virtual-list-loading-spinner {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }
  
  .virtual-list-no-more {
    padding: 12px;
    font-size: 13px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .virtual-list {
    border: 2px solid #000;
  }
  
  .virtual-list-item {
    border-bottom-color: #000;
  }
  
  .virtual-list-loading {
    background-color: #fff;
    color: #000;
  }
  
  .virtual-list-no-more {
    background-color: #f0f0f0;
    color: #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .virtual-list-loading-spinner {
    animation: none;
  }
  
  .virtual-list {
    scroll-behavior: auto;
  }
}
