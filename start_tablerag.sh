#!/bin/bash

# TableRAG 完整系统启动脚本

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "TableRAG 人工标注系统启动脚本"
echo "================================="
echo "项目目录: $SCRIPT_DIR"
echo ""

# 检查依赖
echo "检查系统依赖..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 python3 命令"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 node 命令"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm 命令"
    exit 1
fi

echo "✓ 系统依赖检查通过"
echo ""

# 安装后端依赖
echo "安装后端依赖..."
cd "$SCRIPT_DIR/backend"
pip install -r requirements.txt
echo "✓ 后端依赖安装完成"
echo ""

# 安装前端依赖
echo "检查前端依赖..."
cd "$SCRIPT_DIR/analyzer"
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
    echo "✓ 前端依赖安装完成"
else
    echo "✓ 前端依赖已存在"
fi
echo ""

# 配置数据库
echo "配置数据库..."
export DATABASE_URL=${DATABASE_URL:-"mysql+pymysql://aidocsuser:aidocspass@localhost:13306/aidocsdb"}
echo "数据库连接: $DATABASE_URL"
echo ""

# 启动后端服务
echo "启动后端服务..."
cd "$SCRIPT_DIR/backend"
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# 后台启动后端
nohup python main.py > backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务已启动 (PID: $BACKEND_PID)"
echo "日志文件: $SCRIPT_DIR/backend/backend.log"

# 等待后端启动
echo "等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✓ 后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "错误: 后端服务启动超时"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    sleep 1
done
echo ""

# 启动前端服务
echo "启动前端服务..."
cd "$SCRIPT_DIR/analyzer"

# 检查端口3000是否被占用
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "警告: 端口3000已被占用，前端可能已在运行"
    echo "请访问: http://localhost:3000"
else
    # 后台启动前端
    nohup npm start > frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo "前端服务已启动 (PID: $FRONTEND_PID)"
    echo "日志文件: $SCRIPT_DIR/analyzer/frontend.log"
    
    # 等待前端启动
    echo "等待前端服务启动..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✓ 前端服务启动成功"
            break
        fi
        if [ $i -eq 60 ]; then
            echo "警告: 前端服务启动可能需要更长时间"
            break
        fi
        sleep 2
    done
fi

echo ""
echo "TableRAG 系统启动完成！"
echo "========================="
echo "后端服务: http://localhost:8000"
echo "  - API文档: http://localhost:8000/docs"
echo "  - 健康检查: http://localhost:8000/health"
echo ""
echo "前端服务: http://localhost:3000"
echo "  - 解析分析: 查看多路OCR解析结果"
echo "  - 人工标注: 创建和管理标注数据"
echo ""
echo "功能说明:"
echo "1. 数据集管理: 自动发现和管理测试数据集"
echo "2. 解析结果展示: 查看KDC、MonkeyOCR、VL LLM等解析结果"
echo "3. 人工标注: 创建标准答案，支持Markdown和HTML格式"
echo "4. 准确率评估: 自动计算解析结果与标注数据的准确率"
echo "5. 报告生成: 生成详细的准确率分析报告"
echo ""
echo "进程信息:"
echo "  后端PID: $BACKEND_PID"
if [ ! -z "$FRONTEND_PID" ]; then
    echo "  前端PID: $FRONTEND_PID"
fi
echo ""
echo "停止服务:"
echo "  kill $BACKEND_PID"
if [ ! -z "$FRONTEND_PID" ]; then
    echo "  kill $FRONTEND_PID"
fi
echo ""
echo "或者运行: pkill -f 'python main.py' && pkill -f 'react-scripts'"
