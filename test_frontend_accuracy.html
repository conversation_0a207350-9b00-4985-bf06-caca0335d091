<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端准确率计算</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            font-weight: bold;
            color: #007bff;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>前端准确率计算测试</h1>
    
    <div class="test-section">
        <h3>测试1: HTML vs Markdown 表格比较</h3>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 相同内容不同格式</h3>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 部分匹配</h3>
        <div id="test3-result"></div>
    </div>

    <script>
        // 复制前端的表格解析和比较逻辑
        const parseTableContent = (content) => {
            try {
                // 尝试解析HTML表格
                if (content.includes('<table')) {
                    return parseHTMLTable(content);
                }
                
                // 尝试解析Markdown表格
                if (content.includes('|')) {
                    return parseMarkdownTable(content);
                }
                
                return null;
            } catch (error) {
                return null;
            }
        };

        const parseHTMLTable = (htmlContent) => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            const table = doc.querySelector('table');
            
            if (!table) return null;
            
            const rows = [];
            const trs = table.querySelectorAll('tr');
            
            trs.forEach(tr => {
                const cells = [];
                const tds = tr.querySelectorAll('th, td');
                tds.forEach(td => {
                    cells.push(td.textContent.trim());
                });
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });
            
            return rows;
        };

        const parseMarkdownTable = (markdownContent) => {
            const lines = markdownContent.split('\n').map(line => line.trim()).filter(line => line);
            const tableLines = lines.filter(line => line.startsWith('|') && line.endsWith('|'));
            
            if (tableLines.length < 2) return null;
            
            // 移除分隔符行
            const dataLines = tableLines.filter(line => !line.match(/^\|[\s\-\|]+\|$/));
            
            const rows = [];
            dataLines.forEach(line => {
                const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });
            
            return rows;
        };

        const compareTableData = (table1, table2) => {
            if (!table1 || !table2) return 0;
            
            // 检查行数和列数
            if (table1.length !== table2.length) {
                return Math.max(0, 100 - Math.abs(table1.length - table2.length) * 10);
            }
            
            let totalCells = 0;
            let matchingCells = 0;
            
            for (let i = 0; i < table1.length; i++) {
                const row1 = table1[i] || [];
                const row2 = table2[i] || [];
                const maxCols = Math.max(row1.length, row2.length);
                
                for (let j = 0; j < maxCols; j++) {
                    totalCells++;
                    const cell1 = (row1[j] || '').toLowerCase().trim();
                    const cell2 = (row2[j] || '').toLowerCase().trim();
                    
                    if (cell1 === cell2) {
                        matchingCells++;
                    }
                }
            }
            
            return totalCells > 0 ? (matchingCells / totalCells) * 100 : 0;
        };

        const calculateAccuracy = (expected, actual) => {
            if (!expected || !actual) return 0;
            
            try {
                // 尝试解析表格内容进行比较
                const expectedTable = parseTableContent(String(expected));
                const actualTable = parseTableContent(String(actual));
                
                if (expectedTable && actualTable) {
                    return compareTableData(expectedTable, actualTable);
                } else {
                    // 如果无法解析为表格，使用简单字符串比较
                    return 0; // 简化处理
                }
            } catch (error) {
                console.warn('表格解析失败:', error);
                return 0;
            }
        };

        // 测试数据
        const htmlTable = `<table>
  <thead>
    <tr>
      <th>项目</th>
      <th>营业收入（万元）</th>
      <th>营业成本（万元）</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>主营业务收入</td>
      <td>12000</td>
      <td>8400</td>
    </tr>
    <tr>
      <td>投资收益</td>
      <td>500</td>
      <td>200</td>
    </tr>
  </tbody>
</table>`;

        const markdownTable = `| 项目         | 营业收入（万元） | 营业成本（万元） |
|--------------|------------------|------------------|
| 主营业务收入 | 12000            | 8400             |
| 投资收益     | 500              | 200              |`;

        const markdownTablePartial = `| 项目         | 营业收入（万元） | 营业成本（万元） |
|--------------|------------------|------------------|
| 主营业务收入 | 12000            | 8400             |
| 投资收益     | 500              | 300              |`; // 最后一个数字不同

        // 执行测试
        function runTests() {
            // 测试1: 完全匹配
            const accuracy1 = calculateAccuracy(htmlTable, markdownTable);
            document.getElementById('test1-result').innerHTML = `
                <p>HTML表格 vs Markdown表格</p>
                <p class="result ${accuracy1 === 100 ? 'success' : 'warning'}">准确率: ${accuracy1.toFixed(1)}%</p>
                <p>${accuracy1 === 100 ? '✅ 完美匹配' : '⚠️ 未完全匹配'}</p>
            `;

            // 测试2: 相同格式
            const accuracy2 = calculateAccuracy(markdownTable, markdownTable);
            document.getElementById('test2-result').innerHTML = `
                <p>Markdown表格 vs 相同Markdown表格</p>
                <p class="result ${accuracy2 === 100 ? 'success' : 'error'}">准确率: ${accuracy2.toFixed(1)}%</p>
                <p>${accuracy2 === 100 ? '✅ 完美匹配' : '❌ 应该完美匹配'}</p>
            `;

            // 测试3: 部分匹配
            const accuracy3 = calculateAccuracy(htmlTable, markdownTablePartial);
            document.getElementById('test3-result').innerHTML = `
                <p>HTML表格 vs 部分不同的Markdown表格</p>
                <p class="result ${accuracy3 > 80 ? 'success' : accuracy3 > 50 ? 'warning' : 'error'}">准确率: ${accuracy3.toFixed(1)}%</p>
                <p>${accuracy3 > 80 ? '✅ 高准确率' : accuracy3 > 50 ? '⚠️ 中等准确率' : '❌ 低准确率'}</p>
            `;

            // 显示解析结果
            console.log('HTML解析结果:', parseHTMLTable(htmlTable));
            console.log('Markdown解析结果:', parseMarkdownTable(markdownTable));
            console.log('部分不同Markdown解析结果:', parseMarkdownTable(markdownTablePartial));
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
