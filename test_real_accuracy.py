#!/usr/bin/env python3
"""
测试test20数据集的实际准确率计算

使用真实的解析结果和标注数据
"""

import sys
import os
import json
import requests

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def get_real_data():
    """获取真实的解析结果和标注数据"""
    print("📊 获取test20数据集的真实数据...")
    
    # 获取标注数据
    try:
        response = requests.get("http://localhost:8000/api/annotations?dataset_name=test20")
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return None, None
        annotations = response.json()
        print(f"✅ 获取到 {len(annotations)} 个标注")
    except Exception as e:
        print(f"❌ 获取标注数据失败: {e}")
        return None, None
    
    # 读取解析结果
    try:
        with open('parse_results/test20/latest_results.json', 'r', encoding='utf-8') as f:
            parse_results = json.load(f)
        print(f"✅ 读取解析结果成功")
    except Exception as e:
        print(f"❌ 读取解析结果失败: {e}")
        return None, None
    
    return annotations, parse_results

def extract_vl_llm_results(parse_results):
    """提取VL LLM的解析结果"""
    vl_llm_results = {}

    # 检查解析结果结构
    if 'parse_results' in parse_results and 'vl_llm' in parse_results['parse_results']:
        vl_llm_data = parse_results['parse_results']['vl_llm']
    elif 'vl_llm' in parse_results:
        vl_llm_data = parse_results['vl_llm']
    else:
        print("❌ 未找到vl_llm结果")
        print(f"可用的解析器: {list(parse_results.get('parse_results', {}).keys())}")
        return vl_llm_results

    for result in vl_llm_data:
        image_name = result.get('original_image')
        if image_name and result.get('success'):
            # 直接使用原始结果结构
            vl_llm_results[image_name] = {
                'parser_name': 'vl_llm',
                'result': result.get('result', {})
            }
            print(f"✅ 提取VL LLM结果: {image_name}")

    return vl_llm_results

def test_accuracy_for_image(annotation, vl_llm_result):
    """测试单个图片的准确率"""
    try:
        from services.accuracy import AccuracyCalculator
        
        calculator = AccuracyCalculator()
        
        # 准备标注数据
        ground_truth = {
            'table_structure': annotation['table_structure'],
            'table_content': annotation['table_content']
        }
        
        # 计算准确率
        result = calculator.calculate_accuracy(ground_truth, vl_llm_result)
        
        return result
        
    except Exception as e:
        print(f"❌ 准确率计算失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始测试test20数据集的实际准确率\n")
    
    # 获取数据
    annotations, parse_results = get_real_data()
    if not annotations or not parse_results:
        return 1
    
    # 提取VL LLM结果
    vl_llm_results = extract_vl_llm_results(parse_results)
    print(f"✅ 提取到 {len(vl_llm_results)} 个VL LLM结果")
    
    # 为每个标注计算准确率
    total_results = []
    
    for annotation in annotations:
        image_filename = annotation['image_filename']
        print(f"\n📋 处理图片: {image_filename}")
        
        if image_filename in vl_llm_results:
            vl_llm_result = vl_llm_results[image_filename]
            
            # 显示数据内容
            print(f"标注内容预览: {annotation['table_content'][:100]}...")
            
            vl_content = vl_llm_result['result']['content']['choices'][0]['message']['content']
            print(f"VL LLM结果预览: {vl_content[:100]}...")
            
            # 计算准确率
            accuracy = test_accuracy_for_image(annotation, vl_llm_result)
            
            if accuracy:
                total_results.append(accuracy)
                print(f"✅ 准确率计算结果:")
                print(f"   结构准确率: {accuracy['structure_accuracy']}")
                print(f"   内容准确率: {accuracy['content_accuracy']}")
                print(f"   综合准确率: {accuracy['overall_accuracy']}")
            else:
                print(f"❌ 准确率计算失败")
        else:
            print(f"❌ 未找到对应的VL LLM结果")
    
    # 计算平均准确率
    if total_results:
        avg_structure = sum(r['structure_accuracy'] for r in total_results) / len(total_results)
        avg_content = sum(r['content_accuracy'] for r in total_results) / len(total_results)
        avg_overall = sum(r['overall_accuracy'] for r in total_results) / len(total_results)
        
        print(f"\n🎯 test20数据集VL LLM平均准确率:")
        print(f"   结构准确率: {avg_structure:.4f}")
        print(f"   内容准确率: {avg_content:.4f}")
        print(f"   综合准确率: {avg_overall:.4f}")
        
        if avg_content > 0.8:
            print("✅ 内容准确率正常")
        else:
            print("⚠️  内容准确率偏低，需要检查")
            
        return 0
    else:
        print("❌ 没有成功计算任何准确率")
        return 1

if __name__ == "__main__":
    sys.exit(main())
