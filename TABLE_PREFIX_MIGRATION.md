# TableRAG 表名前缀迁移指南

为了避免在共享数据库环境中与其他应用的表名冲突，TableRAG 的所有数据库表现在都使用 `tablerag_` 前缀。

## 📋 表名变更

| 旧表名 | 新表名 |
|--------|--------|
| `datasets` | `tablerag_datasets` |
| `images` | `tablerag_images` |
| `annotations` | `tablerag_annotations` |
| `accuracy_evaluations` | `tablerag_accuracy_evaluations` |
| `reports` | `tablerag_reports` |

## 🚀 迁移方案

### 方案1: 全新安装（推荐）

如果您是首次安装或者可以重新开始，直接使用新版本即可：

```bash
# 启动新版本，会自动创建带前缀的表
./start_tablerag.sh
```

### 方案2: 数据迁移

如果您已有重要的标注数据需要保留，可以使用迁移脚本：

```bash
# 1. 备份现有数据库
cp backend/tablerag_annotations.db backend/tablerag_annotations_backup.db

# 2. 运行迁移脚本
cd backend
DATABASE_URL=sqlite:///./tablerag_annotations.db python migrate_table_names.py

# 3. 验证迁移结果
python -c "
import sqlite3
conn = sqlite3.connect('tablerag_annotations.db')
cursor = conn.cursor()
cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\"')
tables = [row[0] for row in cursor.fetchall()]
print('迁移后的表:', tables)
conn.close()
"

# 4. 重启服务
python main.py
```

### 方案3: MySQL 迁移

对于 MySQL 数据库：

```bash
# 1. 备份数据库
mysqldump -u root -p tablerag > tablerag_backup.sql

# 2. 运行迁移脚本
DATABASE_URL=mysql+pymysql://user:password@localhost/tablerag python migrate_table_names.py

# 3. 重启服务
python main.py
```

## 🔧 配置更新

### 环境变量

无需更改环境变量，表名前缀是在代码层面处理的：

```bash
# 这些配置保持不变
DATABASE_URL=sqlite:///./tablerag_annotations.db
DATABASE_URL=mysql+pymysql://user:password@localhost/tablerag
```

### 应用代码

应用代码已经更新，无需手动修改。所有的 SQLAlchemy 模型都已经使用新的表名。

## 🧪 验证迁移

### 检查表结构

```bash
# SQLite
python check_tables.py

# MySQL
mysql -u root -p tablerag -e "SHOW TABLES LIKE 'tablerag_%'"
```

### 测试功能

```bash
# 测试API功能
./test_annotation_api.sh
```

## 🔄 回滚方案

如果迁移出现问题，可以回滚到原始状态：

### SQLite 回滚

```bash
# 恢复备份
cp backend/tablerag_annotations_backup.db backend/tablerag_annotations.db

# 使用旧版本代码（如果需要）
git checkout <previous_commit>
```

### MySQL 回滚

```bash
# 删除新表
mysql -u root -p tablerag -e "
DROP TABLE IF EXISTS tablerag_datasets;
DROP TABLE IF EXISTS tablerag_images;
DROP TABLE IF EXISTS tablerag_annotations;
DROP TABLE IF EXISTS tablerag_accuracy_evaluations;
DROP TABLE IF EXISTS tablerag_reports;
"

# 恢复备份
mysql -u root -p tablerag < tablerag_backup.sql
```

## 📊 迁移脚本功能

`migrate_table_names.py` 脚本提供以下功能：

1. **自动检测**: 检测现有表名和数据库类型
2. **安全迁移**: 使用事务确保数据一致性
3. **多数据库支持**: 支持 SQLite、MySQL、PostgreSQL
4. **验证功能**: 迁移后自动验证结果
5. **错误处理**: 出错时自动回滚

## ⚠️ 注意事项

1. **备份数据**: 迁移前务必备份数据库
2. **停止服务**: 迁移期间停止 TableRAG 服务
3. **权限检查**: 确保数据库用户有 ALTER TABLE 权限
4. **测试环境**: 建议先在测试环境验证迁移过程

## 🆘 故障排除

### 常见问题

1. **权限不足**
   ```
   ERROR: Access denied for user 'xxx'@'localhost' to database 'tablerag'
   ```
   解决：确保数据库用户有足够权限

2. **表已存在**
   ```
   ERROR: Table 'tablerag_datasets' already exists
   ```
   解决：检查是否已经迁移过，或手动删除冲突表

3. **外键约束**
   ```
   ERROR: Cannot drop table due to foreign key constraints
   ```
   解决：迁移脚本会自动处理外键关系

### 获取帮助

如果遇到问题，请：

1. 检查迁移脚本的输出日志
2. 验证数据库连接和权限
3. 查看 TableRAG 服务日志
4. 联系技术支持

## 📈 迁移后的优势

1. **命名空间隔离**: 避免与其他应用表名冲突
2. **更好的管理**: 清晰标识 TableRAG 相关表
3. **部署灵活性**: 可以与其他应用共享数据库
4. **维护便利**: 便于数据库管理和监控

迁移完成后，TableRAG 将继续正常工作，所有功能保持不变。
