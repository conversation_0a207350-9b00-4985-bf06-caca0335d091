#!/bin/bash

# TableRAG 标注API测试脚本

set -e

echo "TableRAG 标注API测试"
echo "==================="

BASE_URL="http://localhost:8000"

# 检查后端服务是否运行
echo "1. 检查后端服务状态..."
if curl -s "$BASE_URL/health" > /dev/null; then
    echo "✓ 后端服务正常运行"
else
    echo "✗ 后端服务未运行，请先启动后端服务"
    exit 1
fi

# 获取数据集列表
echo ""
echo "2. 获取数据集列表..."
DATASETS=$(curl -s "$BASE_URL/api/datasets")
echo "数据集数量: $(echo "$DATASETS" | jq length)"
echo "数据集列表: $(echo "$DATASETS" | jq -r '.[].name' | tr '\n' ' ')"

# 获取kingsoft数据集的图片
echo ""
echo "3. 获取kingsoft数据集图片..."
IMAGES=$(curl -s "$BASE_URL/api/datasets/kingsoft/images")
IMAGE_COUNT=$(echo "$IMAGES" | jq length)
echo "图片数量: $IMAGE_COUNT"
if [ "$IMAGE_COUNT" -gt 0 ]; then
    FIRST_IMAGE=$(echo "$IMAGES" | jq -r '.[0].filename')
    echo "第一张图片: $FIRST_IMAGE"
else
    echo "✗ 没有找到图片"
    exit 1
fi

# 创建测试标注
echo ""
echo "4. 创建测试标注..."
ANNOTATION_DATA='{
    "dataset_name": "kingsoft",
    "image_filename": "'$FIRST_IMAGE'",
    "annotator": "API测试",
    "table_structure": "{\"rows\":4,\"cols\":2,\"merged_cells\":[],\"headers\":[]}",
    "table_content": "| 检验项目 | 检验结果 |\n| --- | --- |\n| 性状 | 符合规定 |\n| 鉴别 | 符合规定 |\n| 含量 | 103.7% |",
    "annotation_type": "manual",
    "status": "completed"
}'

ANNOTATION_RESULT=$(curl -s -X POST "$BASE_URL/api/annotations" \
    -H "Content-Type: application/json" \
    -d "$ANNOTATION_DATA")

if echo "$ANNOTATION_RESULT" | jq -e '.id' > /dev/null; then
    ANNOTATION_ID=$(echo "$ANNOTATION_RESULT" | jq -r '.id')
    echo "✓ 标注创建成功，ID: $ANNOTATION_ID"
else
    echo "✗ 标注创建失败:"
    echo "$ANNOTATION_RESULT"
    exit 1
fi

# 获取标注列表
echo ""
echo "5. 获取标注列表..."
ANNOTATIONS=$(curl -s "$BASE_URL/api/annotations")
ANNOTATION_COUNT=$(echo "$ANNOTATIONS" | jq length)
echo "标注数量: $ANNOTATION_COUNT"

# 获取单个标注
echo ""
echo "6. 获取单个标注..."
SINGLE_ANNOTATION=$(curl -s "$BASE_URL/api/annotations/$ANNOTATION_ID")
ANNOTATOR=$(echo "$SINGLE_ANNOTATION" | jq -r '.annotator')
STATUS=$(echo "$SINGLE_ANNOTATION" | jq -r '.status')
echo "标注员: $ANNOTATOR"
echo "状态: $STATUS"

# 更新标注
echo ""
echo "7. 更新标注状态..."
UPDATE_DATA='{"status": "reviewed"}'
UPDATED_ANNOTATION=$(curl -s -X PUT "$BASE_URL/api/annotations/$ANNOTATION_ID" \
    -H "Content-Type: application/json" \
    -d "$UPDATE_DATA")

NEW_STATUS=$(echo "$UPDATED_ANNOTATION" | jq -r '.status')
echo "更新后状态: $NEW_STATUS"

# 测试准确率评估（模拟）
echo ""
echo "8. 测试准确率评估..."
IMAGE_ID=$(echo "$SINGLE_ANNOTATION" | jq -r '.image_id')
EVAL_DATA='{
    "image_id": '$IMAGE_ID',
    "annotation_id": '$ANNOTATION_ID',
    "parser_results": {
        "parser_name": "test_parser",
        "result": "| 检验项目 | 检验结果 |\n| --- | --- |\n| 性状 | 符合规定 |\n| 鉴别 | 符合规定 |\n| 含量 | 103.5% |",
        "table_structure": "{\"rows\":4,\"cols\":2}"
    }
}'

EVAL_RESULT=$(curl -s -X POST "$BASE_URL/api/evaluate" \
    -H "Content-Type: application/json" \
    -d "$EVAL_DATA")

if echo "$EVAL_RESULT" | jq -e '.overall_accuracy' > /dev/null; then
    ACCURACY=$(echo "$EVAL_RESULT" | jq -r '.overall_accuracy')
    echo "✓ 准确率评估成功，综合准确率: $ACCURACY"
else
    echo "✗ 准确率评估失败:"
    echo "$EVAL_RESULT"
fi

# 生成报告
echo ""
echo "9. 生成准确率报告..."
REPORT_DATA='{
    "dataset_name": "kingsoft",
    "report_type": "accuracy",
    "title": "API测试报告",
    "created_by": "测试脚本"
}'

REPORT_RESULT=$(curl -s -X POST "$BASE_URL/api/reports/generate" \
    -H "Content-Type: application/json" \
    -d "$REPORT_DATA")

if echo "$REPORT_RESULT" | jq -e '.id' > /dev/null; then
    REPORT_ID=$(echo "$REPORT_RESULT" | jq -r '.id')
    echo "✓ 报告生成成功，ID: $REPORT_ID"
else
    echo "✗ 报告生成失败:"
    echo "$REPORT_RESULT"
fi

# 清理测试数据（可选）
echo ""
echo "10. 清理测试数据..."
read -p "是否删除测试标注? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    DELETE_RESULT=$(curl -s -X DELETE "$BASE_URL/api/annotations/$ANNOTATION_ID")
    echo "✓ 测试标注已删除"
fi

echo ""
echo "API测试完成！"
echo "=============="
echo "所有核心功能都正常工作："
echo "- ✓ 数据集管理"
echo "- ✓ 图片管理"
echo "- ✓ 标注创建"
echo "- ✓ 标注查询"
echo "- ✓ 标注更新"
echo "- ✓ 准确率评估"
echo "- ✓ 报告生成"
echo ""
echo "现在可以在前端界面 http://localhost:3000 中使用标注功能了！"
